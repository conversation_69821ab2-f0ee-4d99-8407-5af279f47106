import 'package:flutter/material.dart';
import 'package:test/test.dart';

// Import only theme components to test them in isolation
import 'package:shadcn/src/theme/extensions/shadcn_accordion_theme.dart';
import 'package:shadcn/src/constants/shadcn_tokens.dart';

void main() {
  group('ShadcnAccordionTheme Tests', () {
    late ColorScheme lightColorScheme;
    late ColorScheme darkColorScheme;

    setUp(() {
      lightColorScheme = ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.light);
      darkColorScheme = ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.dark);
    });

    test('creates valid default theme', () {
      final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
      
      expect(theme.headerBackground, lightColorScheme.surface);
      expect(theme.headerForeground, lightColorScheme.onSurface);
      expect(theme.contentBackground, lightColorScheme.surface);
      expect(theme.contentForeground, lightColorScheme.onSurface.withOpacity(0.8));
      expect(theme.borderColor, lightColorScheme.outline);
      expect(theme.triggerIconColor, lightColorScheme.onSurface.withOpacity(0.6));
      expect(theme.triggerIconHoverColor, lightColorScheme.onSurface);
      
      expect(theme.headerPadding, isNotNull);
      expect(theme.contentPadding, isNotNull);
      expect(theme.borderRadius, isNotNull);
      expect(theme.borderWidth, ShadcnTokens.borderWidth);
      expect(theme.triggerIconSize, ShadcnTokens.iconSizeMd);
      expect(theme.animationDuration, ShadcnTokens.durationNormal);
      expect(theme.animationCurve, Curves.easeInOut);
      expect(theme.elevation, ShadcnTokens.elevationNone);
    });

    test('creates different hover backgrounds for light and dark themes', () {
      final lightTheme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
      final darkTheme = ShadcnAccordionTheme.defaultTheme(darkColorScheme);
      
      expect(lightTheme.headerHoverBackground, 
             lightColorScheme.surfaceVariant.withOpacity(0.05));
      expect(darkTheme.headerHoverBackground, 
             darkColorScheme.surfaceVariant.withOpacity(0.1));
    });

    test('copyWith creates new instance with updated values', () {
      final originalTheme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
      final newTheme = originalTheme.copyWith(
        headerBackground: Colors.red,
        contentBackground: Colors.green,
        borderWidth: 3.0,
        triggerIconSize: 30.0,
        animationDuration: const Duration(milliseconds: 500),
      );
      
      expect(newTheme.headerBackground, Colors.red);
      expect(newTheme.contentBackground, Colors.green);
      expect(newTheme.borderWidth, 3.0);
      expect(newTheme.triggerIconSize, 30.0);
      expect(newTheme.animationDuration, const Duration(milliseconds: 500));
      
      // Other values should remain the same
      expect(newTheme.headerForeground, originalTheme.headerForeground);
      expect(newTheme.contentForeground, originalTheme.contentForeground);
      expect(newTheme.borderColor, originalTheme.borderColor);
    });

    test('lerp interpolates values correctly', () {
      final theme1 = ShadcnAccordionTheme(
        headerBackground: Colors.red,
        headerForeground: Colors.white,
        borderWidth: 1.0,
        triggerIconSize: 16.0,
      );
      
      final theme2 = ShadcnAccordionTheme(
        headerBackground: Colors.blue,
        headerForeground: Colors.black,
        borderWidth: 3.0,
        triggerIconSize: 24.0,
      );
      
      final lerpedTheme = theme1.lerp(theme2, 0.5);
      
      expect(lerpedTheme.headerBackground, 
             Color.lerp(Colors.red, Colors.blue, 0.5));
      expect(lerpedTheme.headerForeground, 
             Color.lerp(Colors.white, Colors.black, 0.5));
      expect(lerpedTheme.borderWidth, 2.0); // midpoint
      expect(lerpedTheme.triggerIconSize, 20.0); // midpoint
    });

    test('lerp handles null other theme', () {
      final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
      final lerpedTheme = theme.lerp(null, 0.5);
      
      expect(lerpedTheme, theme);
    });

    test('validation passes for valid themes', () {
      const theme = ShadcnAccordionTheme(
        borderWidth: 2.0,
        triggerIconSize: 20.0,
        elevation: 4.0,
        animationDuration: Duration(milliseconds: 300),
      );
      
      expect(theme.validate(), isTrue);
    });

    test('validation fails for invalid border width', () {
      const theme = ShadcnAccordionTheme(borderWidth: -1.0);
      
      expect(theme.validate(), isFalse);
      expect(() => theme.validate(throwOnError: true), throwsException);
    });

    test('validation fails for invalid trigger icon size', () {
      const theme = ShadcnAccordionTheme(triggerIconSize: 0.0);
      
      expect(theme.validate(), isFalse);
      expect(() => theme.validate(throwOnError: true), throwsException);
    });

    test('validation fails for negative elevation', () {
      const theme = ShadcnAccordionTheme(elevation: -1.0);
      
      expect(theme.validate(), isFalse);
      expect(() => theme.validate(throwOnError: true), throwsException);
    });

    test('equality works correctly', () {
      const theme1 = ShadcnAccordionTheme(
        headerBackground: Colors.red,
        borderWidth: 2.0,
        animationDuration: Duration(milliseconds: 300),
      );
      
      const theme2 = ShadcnAccordionTheme(
        headerBackground: Colors.red,
        borderWidth: 2.0,
        animationDuration: Duration(milliseconds: 300),
      );
      
      expect(theme1, equals(theme2));
      expect(theme1.hashCode, equals(theme2.hashCode));
    });

    test('inequality works correctly', () {
      const theme1 = ShadcnAccordionTheme(
        headerBackground: Colors.red,
        borderWidth: 2.0,
      );
      
      const theme2 = ShadcnAccordionTheme(
        headerBackground: Colors.blue,
        borderWidth: 2.0,
      );
      
      expect(theme1, isNot(equals(theme2)));
    });

    test('lightTheme factory creates optimized light theme', () {
      final theme = ShadcnAccordionTheme.lightTheme(lightColorScheme);
      
      expect(theme.headerBackground, lightColorScheme.surface);
      expect(theme.headerHoverBackground, 
             lightColorScheme.surfaceVariant.withOpacity(0.05));
      expect(theme.borderColor, lightColorScheme.outline.withOpacity(0.2));
    });

    test('darkTheme factory creates optimized dark theme', () {
      final theme = ShadcnAccordionTheme.darkTheme(darkColorScheme);
      
      expect(theme.headerBackground, darkColorScheme.surface);
      expect(theme.headerHoverBackground, 
             darkColorScheme.surfaceVariant.withOpacity(0.1));
      expect(theme.borderColor, darkColorScheme.outline.withOpacity(0.3));
    });

    test('uses consistent design tokens', () {
      final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
      
      expect(theme.borderWidth, ShadcnTokens.borderWidth);
      expect(theme.triggerIconSize, ShadcnTokens.iconSizeMd);
      expect(theme.animationDuration, ShadcnTokens.durationNormal);
      expect(theme.elevation, ShadcnTokens.elevationNone);
      
      expect(theme.borderRadius, BorderRadius.circular(ShadcnTokens.radiusMd));
      
      expect(theme.headerTextStyle?.fontSize, ShadcnTokens.fontSizeMd);
      expect(theme.headerTextStyle?.fontWeight, ShadcnTokens.fontWeightMedium);
      expect(theme.contentTextStyle?.fontSize, ShadcnTokens.fontSizeMd);
      expect(theme.contentTextStyle?.fontWeight, ShadcnTokens.fontWeightNormal);
    });
  });
}