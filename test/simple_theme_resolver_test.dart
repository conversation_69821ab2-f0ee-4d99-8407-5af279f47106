import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/utils/theme_resolver.dart';

void main() {
  group('ShadcnThemeResolver - Basic Tests', () {
    test('ThemeException can be created', () {
      const message = 'Test error';
      const exception = ThemeException(message);
      
      expect(exception.message, equals(message));
      expect(exception.toString(), equals('ThemeException: $message'));
    });

    test('validateThemeProperty returns true for non-null values', () {
      const value = 'test';
      final result = ShadcnThemeResolver.validateThemeProperty(
        value,
        'testProperty',
      );
      
      expect(result, isTrue);
    });

    test('validateThemeProperty returns false for null values', () {
      final result = ShadcnThemeResolver.validateThemeProperty(
        null,
        'testProperty',
      );
      
      expect(result, isFalse);
    });

    test('validateThemeProperty throws when throwOnError is true', () {
      expect(
        () => ShadcnThemeResolver.validateThemeProperty(
          null,
          'testProperty',
          throwOnError: true,
        ),
        throwsA(isA<ThemeException>()),
      );
    });

    test('validateColorScheme returns true for valid color scheme', () {
      final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
      final result = ShadcnThemeResolver.validateColorScheme(colorScheme);
      
      expect(result, isTrue);
    });

    test('validateTextTheme returns true for valid text theme', () {
      final textTheme = ThemeData.light().textTheme;
      final result = ShadcnThemeResolver.validateTextTheme(textTheme);
      
      expect(result, isTrue);
    });
  });
}