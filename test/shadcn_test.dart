import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnTokens', () {
    test('provides correct spacing values', () {
      expect(ShadcnTokens.spacing0, 0.0);
      expect(ShadcnTokens.spacing1, 4.0);
      expect(ShadcnTokens.spacing2, 8.0);
      expect(ShadcnTokens.spacing4, 16.0);
    });

    test('provides correct size values', () {
      expect(ShadcnTokens.buttonHeightMd, 40.0);
      expect(ShadcnTokens.inputHeightMd, 36.0);
      expect(ShadcnTokens.buttonHeightSm, 36.0);
      expect(ShadcnTokens.inputHeightSm, 32.0);
    });

    test('provides correct border radius values', () {
      expect(ShadcnTokens.radiusMd, 6.0);
      expect(ShadcnTokens.radiusLg, 8.0);
      expect(ShadcnTokens.radiusSm, 4.0);
    });
  });

  group('ShadcnColorScheme', () {
    test('creates from Material ColorScheme correctly', () {
      final materialColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      
      final shadcnColorScheme = ShadcnColorScheme.fromMaterial(materialColorScheme);
      
      expect(shadcnColorScheme.background, materialColorScheme.surface);
      expect(shadcnColorScheme.foreground, materialColorScheme.onSurface);
      expect(shadcnColorScheme.ring, materialColorScheme.primary);
      expect(shadcnColorScheme.destructive, materialColorScheme.error);
    });

    test('creates light theme correctly', () {
      final lightScheme = ShadcnColorScheme.light();
      
      expect(lightScheme.background, isA<Color>());
      expect(lightScheme.foreground, isA<Color>());
      expect(lightScheme.card, isA<Color>());
      expect(lightScheme.border, isA<Color>());
    });

    test('creates dark theme correctly', () {
      final darkScheme = ShadcnColorScheme.dark();
      
      expect(darkScheme.background, isA<Color>());
      expect(darkScheme.foreground, isA<Color>());
      expect(darkScheme.card, isA<Color>());
      expect(darkScheme.border, isA<Color>());
    });

    test('copyWith works correctly', () {
      final original = ShadcnColorScheme.light();
      final modified = original.copyWith(ring: Colors.red);
      
      expect(modified.ring, Colors.red);
      expect(modified.background, original.background);
      expect(modified.foreground, original.foreground);
    });

    test('lerp works correctly', () {
      final scheme1 = ShadcnColorScheme.light();
      final scheme2 = ShadcnColorScheme.dark();
      
      final lerped = scheme1.lerp(scheme2, 0.5);
      expect(lerped, isA<ShadcnColorScheme>());
      expect(lerped.background, isA<Color>());
    });
  });

  group('ShadcnThemeResolver', () {
    testWidgets('resolves theme extension correctly', (tester) async {
      final testExtension = ShadcnColorScheme.light();
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(extensions: [testExtension]),
          home: Builder(
            builder: (context) {
              final resolved = ShadcnThemeResolver.resolveThemeExtension<ShadcnColorScheme>(
                context,
                (colorScheme) => ShadcnColorScheme.fromMaterial(colorScheme),
              );
              
              expect(resolved, equals(testExtension));
              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('validates theme context correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(
                () => ShadcnThemeResolver.validateThemeContext(context),
                returnsNormally,
              );
              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('resolves color correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final resolvedColor = ShadcnThemeResolver.resolveColor(
                context,
                null,
                (theme) => theme.colorScheme.primary,
                Colors.red,
              );
              
              expect(resolvedColor, Theme.of(context).colorScheme.primary);
              return const SizedBox();
            },
          ),
        ),
      );
    });
  });
}
