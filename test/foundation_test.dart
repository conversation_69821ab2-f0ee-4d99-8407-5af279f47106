import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('Foundation Tests', () {
    test('ShadcnTokens provides correct values', () {
      expect(ShadcnTokens.buttonHeightMd, 40.0);
      expect(ShadcnTokens.inputHeightMd, 36.0);
      expect(ShadcnTokens.radiusMd, 6.0);
      expect(ShadcnTokens.spacing4, 16.0);
    });

    test('ShadcnColorScheme creates from Material correctly', () {
      final materialColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      
      final shadcnColorScheme = ShadcnColorScheme.fromMaterial(materialColorScheme);
      
      expect(shadcnColorScheme.ring, materialColorScheme.primary);
      expect(shadcnColorScheme.destructive, materialColorScheme.error);
      expect(shadcnColorScheme.border, materialColorScheme.outline);
    });

    test('ShadcnColorScheme copyWith works', () {
      final original = ShadcnColorScheme.light();
      final modified = original.copyWith(ring: Colors.red);
      
      expect(modified.ring, Colors.red);
      expect(modified.background, original.background);
    });

    test('ShadcnColorScheme lerp works correctly', () {
      final scheme1 = ShadcnColorScheme.light();
      final scheme2 = ShadcnColorScheme.dark();
      
      final interpolated = scheme1.lerp(scheme2, 0.5);
      
      expect(interpolated, isA<ShadcnColorScheme>());
    });
  });
}