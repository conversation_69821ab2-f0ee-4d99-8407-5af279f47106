import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/theme/extensions/shadcn_radio_group_theme.dart';

void main() {
  group('ShadcnRadioGroupTheme', () {
    group('defaultTheme', () {
      testWidgets('creates theme with light color scheme', (tester) async {
        const lightColorScheme = ColorScheme.light();
        final theme = ShadcnRadioGroupTheme.defaultTheme(lightColorScheme);

        expect(theme.selectedInnerCircle, equals(lightColorScheme.primary));
        expect(theme.selectedBorder, equals(lightColorScheme.primary));
        expect(theme.unselectedBackground, equals(Colors.transparent));
        expect(theme.size, equals(20.0));
      });

      testWidgets('creates theme with dark color scheme', (tester) async {
        const darkColorScheme = ColorScheme.dark();
        final theme = ShadcnRadioGroupTheme.defaultTheme(darkColorScheme);

        expect(theme.selectedInnerCircle, equals(darkColorScheme.primary));
        expect(theme.selectedBorder, equals(darkColorScheme.primary));
        expect(theme.unselectedBackground, equals(Colors.transparent));
        expect(theme.size, equals(20.0));
      });

      testWidgets('has proper default values', (tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnRadioGroupTheme.defaultTheme(colorScheme);

        expect(theme.size, equals(20.0));
        expect(theme.smallSize, equals(16.0));
        expect(theme.largeSize, equals(24.0));
        expect(theme.innerCircleSize, equals(8.0));
        expect(theme.smallInnerCircleSize, equals(6.0));
        expect(theme.largeInnerCircleSize, equals(10.0));
        expect(theme.borderWidth, equals(1.0));
        expect(theme.animationDuration, equals(const Duration(milliseconds: 150)));
        expect(theme.direction, equals(Axis.vertical));
      });
    });

    group('copyWith', () {
      test('creates new instance with updated values', () {
        const original = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.red,
          size: 20.0,
          direction: Axis.vertical,
        );

        final updated = original.copyWith(
          selectedInnerCircle: Colors.blue,
          selectedBorder: Colors.white,
          direction: Axis.horizontal,
        );

        expect(updated.selectedInnerCircle, equals(Colors.blue));
        expect(updated.selectedBorder, equals(Colors.white));
        expect(updated.direction, equals(Axis.horizontal));
        expect(updated.size, equals(20.0)); // Unchanged
      });

      test('keeps original values when null provided', () {
        const original = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.red,
          selectedBorder: Colors.white,
          size: 20.0,
          direction: Axis.horizontal,
        );

        final updated = original.copyWith();

        expect(updated.selectedInnerCircle, equals(Colors.red));
        expect(updated.selectedBorder, equals(Colors.white));
        expect(updated.size, equals(20.0));
        expect(updated.direction, equals(Axis.horizontal));
      });
    });

    group('lerp', () {
      test('interpolates between two themes correctly', () {
        const themeA = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.red,
          size: 16.0,
          innerCircleSize: 6.0,
        );

        const themeB = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.blue,
          size: 24.0,
          innerCircleSize: 10.0,
        );

        final lerped = themeA.lerp(themeB, 0.5);

        expect(lerped.size, equals(20.0)); // Midpoint between 16 and 24
        expect(lerped.innerCircleSize, equals(8.0)); // Midpoint between 6 and 10
        // Color lerping is more complex, but we can verify it's not the original colors
        expect(lerped.selectedInnerCircle, isNot(equals(Colors.red)));
        expect(lerped.selectedInnerCircle, isNot(equals(Colors.blue)));
      });

      test('returns original theme when other is null', () {
        const theme = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.red,
          size: 20.0,
        );

        final lerped = theme.lerp(null, 0.5);

        expect(lerped.selectedInnerCircle, equals(Colors.red));
        expect(lerped.size, equals(20.0));
      });

      test('handles t = 0.0 correctly', () {
        const themeA = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.red,
          size: 16.0,
        );

        const themeB = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.blue,
          size: 24.0,
        );

        final lerped = themeA.lerp(themeB, 0.0);

        expect(lerped.selectedInnerCircle, equals(Colors.red));
        expect(lerped.size, equals(16.0));
      });

      test('handles t = 1.0 correctly', () {
        const themeA = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.red,
          size: 16.0,
        );

        const themeB = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.blue,
          size: 24.0,
        );

        final lerped = themeA.lerp(themeB, 1.0);

        expect(lerped.selectedInnerCircle, equals(Colors.blue));
        expect(lerped.size, equals(24.0));
      });
    });

    group('validate', () {
      test('returns true for valid theme', () {
        const theme = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.blue,
          selectedBorder: Colors.white,
          size: 20.0,
          innerCircleSize: 8.0,
          borderWidth: 1.0,
          itemSpacing: 16.0,
        );

        expect(theme.validate(), isTrue);
      });

      test('validates size constraints', () {
        const invalidTheme = ShadcnRadioGroupTheme(
          size: -5.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('validates border width constraints', () {
        const invalidTheme = ShadcnRadioGroupTheme(
          borderWidth: -1.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('validates item spacing constraints', () {
        const invalidTheme = ShadcnRadioGroupTheme(
          itemSpacing: -5.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('throws exception when throwOnError is true', () {
        const invalidTheme = ShadcnRadioGroupTheme(
          size: -5.0,
        );

        expect(() => invalidTheme.validate(throwOnError: true), throwsA(isA<ThemeException>()));
      });
    });

    group('resolveSizeForVariant', () {
      test('returns correct size for each variant', () {
        const theme = ShadcnRadioGroupTheme(
          smallSize: 16.0,
          size: 20.0,
          largeSize: 24.0,
        );

        expect(theme.resolveSizeForVariant(ShadcnRadioGroupSize.small), equals(16.0));
        expect(theme.resolveSizeForVariant(ShadcnRadioGroupSize.medium), equals(20.0));
        expect(theme.resolveSizeForVariant(ShadcnRadioGroupSize.large), equals(24.0));
      });

      test('falls back to defaults when sizes not specified', () {
        const theme = ShadcnRadioGroupTheme();

        expect(theme.resolveSizeForVariant(ShadcnRadioGroupSize.small), equals(16.0));
        expect(theme.resolveSizeForVariant(ShadcnRadioGroupSize.medium), equals(20.0));
        expect(theme.resolveSizeForVariant(ShadcnRadioGroupSize.large), equals(24.0));
      });
    });

    group('resolveInnerCircleSizeForVariant', () {
      test('returns correct inner circle size for each variant', () {
        const theme = ShadcnRadioGroupTheme(
          smallInnerCircleSize: 6.0,
          innerCircleSize: 8.0,
          largeInnerCircleSize: 10.0,
        );

        expect(theme.resolveInnerCircleSizeForVariant(ShadcnRadioGroupSize.small), equals(6.0));
        expect(theme.resolveInnerCircleSizeForVariant(ShadcnRadioGroupSize.medium), equals(8.0));
        expect(theme.resolveInnerCircleSizeForVariant(ShadcnRadioGroupSize.large), equals(10.0));
      });

      test('falls back to defaults when inner circle sizes not specified', () {
        const theme = ShadcnRadioGroupTheme();

        expect(theme.resolveInnerCircleSizeForVariant(ShadcnRadioGroupSize.small), equals(6.0));
        expect(theme.resolveInnerCircleSizeForVariant(ShadcnRadioGroupSize.medium), equals(8.0));
        expect(theme.resolveInnerCircleSizeForVariant(ShadcnRadioGroupSize.large), equals(10.0));
      });
    });

    group('theme extension behavior', () {
      testWidgets('integrates with Material theme system', (tester) async {
        const customTheme = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.purple,
          size: 32.0,
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              extensions: const [customTheme],
            ),
            home: Builder(
              builder: (context) {
                final resolvedTheme = Theme.of(context).extension<ShadcnRadioGroupTheme>();
                expect(resolvedTheme?.selectedInnerCircle, equals(Colors.purple));
                expect(resolvedTheme?.size, equals(32.0));
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('edge cases', () {
      test('handles null values in lerp correctly', () {
        const theme = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.red,
        );

        final lerped = theme.lerp(theme, 0.5);
        expect(lerped.selectedInnerCircle, equals(Colors.red));
      });

      test('copyWith handles all nullable properties', () {
        const theme = ShadcnRadioGroupTheme();
        
        final updated = theme.copyWith(
          selectedBackground: Colors.red,
          selectedForeground: Colors.white,
          selectedBorder: Colors.blue,
          selectedInnerCircle: Colors.green,
          unselectedBackground: Colors.transparent,
          unselectedForeground: Colors.grey,
          unselectedBorder: Colors.black,
          hoverOverlay: Colors.purple,
          pressedOverlay: Colors.pink,
          focusedOverlay: Colors.teal,
          size: 25.0,
          smallSize: 15.0,
          largeSize: 35.0,
          innerCircleSize: 10.0,
          smallInnerCircleSize: 8.0,
          largeInnerCircleSize: 12.0,
          borderWidth: 2.0,
          itemSpacing: 20.0,
          labelSpacing: 10.0,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          direction: Axis.horizontal,
          animationDuration: const Duration(milliseconds: 200),
        );

        expect(updated.selectedBackground, equals(Colors.red));
        expect(updated.selectedForeground, equals(Colors.white));
        expect(updated.size, equals(25.0));
        expect(updated.borderWidth, equals(2.0));
        expect(updated.direction, equals(Axis.horizontal));
      });

      test('handles layout properties correctly', () {
        const theme = ShadcnRadioGroupTheme(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.end,
          wrapAlignment: WrapAlignment.spaceBetween,
          wrapCrossAlignment: WrapCrossAlignment.start,
        );

        final updated = theme.copyWith(
          mainAxisAlignment: MainAxisAlignment.center,
          wrapAlignment: WrapAlignment.center,
        );

        expect(updated.mainAxisAlignment, equals(MainAxisAlignment.center));
        expect(updated.crossAxisAlignment, equals(CrossAxisAlignment.end)); // Unchanged
        expect(updated.wrapAlignment, equals(WrapAlignment.center));
        expect(updated.wrapCrossAlignment, equals(WrapCrossAlignment.start)); // Unchanged
      });
    });
  });
}