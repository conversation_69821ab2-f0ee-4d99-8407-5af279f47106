import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/components/radio_group/shadcn_radio_group.dart';
import 'package:shadcn/src/theme/extensions/shadcn_radio_group_theme.dart';

import '../../test_helpers.dart';

void main() {
  group('ShadcnRadioGroup', () {
    final testItems = [
      const ShadcnRadioGroupItem<String>(value: 'option1', label: 'Option 1'),
      const ShadcnRadioGroupItem<String>(value: 'option2', label: 'Option 2'),
      const ShadcnRadioGroupItem<String>(value: 'option3', label: 'Option 3'),
    ];

    testWidgets('displays correctly with basic properties', (tester) async {
      String? selectedValue;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: selectedValue,
            onChanged: (value) => selectedValue = value,
            items: testItems,
          ),
        ),
      );

      expect(find.text('Option 1'), findsOneWidget);
      expect(find.text('Option 2'), findsOneWidget);
      expect(find.text('Option 3'), findsOneWidget);
      expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
    });

    testWidgets('handles selection correctly', (tester) async {
      String? selectedValue;
      bool callbackTriggered = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: selectedValue,
            onChanged: (value) {
              selectedValue = value;
              callbackTriggered = true;
            },
            items: testItems,
          ),
        ),
      );

      // Tap on the first option
      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(callbackTriggered, isTrue);
      expect(selectedValue, equals('option1'));
    });

    testWidgets('shows selected state correctly', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: 'option2',
            onChanged: (value) {},
            items: testItems,
          ),
        ),
      );

      // The component should render without errors
      expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
    });

    testWidgets('displays helper text when provided', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: null,
            onChanged: (value) {},
            items: testItems,
            helperText: 'Please select an option',
          ),
        ),
      );

      expect(find.text('Please select an option'), findsOneWidget);
    });

    testWidgets('displays error text when provided', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: null,
            onChanged: (value) {},
            items: testItems,
            errorText: 'Selection is required',
          ),
        ),
      );

      expect(find.text('Selection is required'), findsOneWidget);
    });

    testWidgets('error text overrides helper text', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: null,
            onChanged: (value) {},
            items: testItems,
            helperText: 'Helper text',
            errorText: 'Error text',
          ),
        ),
      );

      expect(find.text('Error text'), findsOneWidget);
      expect(find.text('Helper text'), findsNothing);
    });

    testWidgets('respects enabled/disabled state', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: null,
            onChanged: null, // Disabled
            items: testItems,
          ),
        ),
      );

      await tester.tap(find.text('Option 1'));
      await tester.pump();

      // Should not trigger any changes when disabled
      expect(find.text('Option 1'), findsOneWidget);
    });

    testWidgets('supports different sizes', (tester) async {
      for (final size in ShadcnRadioGroupSize.values) {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: null,
              onChanged: (value) {},
              items: testItems,
              size: size,
            ),
          ),
        );

        expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
        await tester.pumpWidget(Container()); // Clear between tests
      }
    });

    testWidgets('supports different directions', (tester) async {
      for (final direction in ShadcnRadioGroupDirection.values) {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: null,
              onChanged: (value) {},
              items: testItems,
              direction: direction,
            ),
          ),
        );

        expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
        await tester.pumpWidget(Container()); // Clear between tests
      }
    });

    testWidgets('supports custom child widgets in items', (tester) async {
      final customItems = [
        const ShadcnRadioGroupItem<String>(
          value: 'custom1',
          child: Row(
            children: [
              Icon(Icons.star),
              SizedBox(width: 8),
              Text('Custom Item 1'),
            ],
          ),
        ),
        const ShadcnRadioGroupItem<String>(
          value: 'custom2',
          label: 'Regular Item',
        ),
      ];

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: null,
            onChanged: (value) {},
            items: customItems,
          ),
        ),
      );

      expect(find.byIcon(Icons.star), findsOneWidget);
      expect(find.text('Custom Item 1'), findsOneWidget);
      expect(find.text('Regular Item'), findsOneWidget);
    });

    testWidgets('handles disabled items correctly', (tester) async {
      final mixedItems = [
        const ShadcnRadioGroupItem<String>(value: 'enabled', label: 'Enabled', enabled: true),
        const ShadcnRadioGroupItem<String>(value: 'disabled', label: 'Disabled', enabled: false),
      ];

      String? selectedValue;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: selectedValue,
            onChanged: (value) => selectedValue = value,
            items: mixedItems,
          ),
        ),
      );

      // Should be able to tap enabled item
      await tester.tap(find.text('Enabled'));
      await tester.pump();
      expect(selectedValue, equals('enabled'));

      // Reset selection
      selectedValue = null;

      // Should not be able to tap disabled item
      await tester.tap(find.text('Disabled'));
      await tester.pump();
      expect(selectedValue, isNull);
    });

    testWidgets('supports tooltips on items', (tester) async {
      final itemsWithTooltips = [
        const ShadcnRadioGroupItem<String>(
          value: 'option1',
          label: 'Option 1',
          tooltip: 'This is option 1',
        ),
        const ShadcnRadioGroupItem<String>(
          value: 'option2',
          label: 'Option 2',
        ),
      ];

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: null,
            onChanged: (value) {},
            items: itemsWithTooltips,
          ),
        ),
      );

      expect(find.byTooltip('This is option 1'), findsOneWidget);
    });

    testWidgets('has correct semantics for group', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: 'option1',
            onChanged: (value) {},
            items: testItems,
            semanticLabel: 'Select an option',
          ),
        ),
      );

      expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
    });

    testWidgets('supports group tooltip', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnRadioGroup<String>(
            value: null,
            onChanged: (value) {},
            items: testItems,
            tooltip: 'This is a radio group',
          ),
        ),
      );

      expect(find.byTooltip('This is a radio group'), findsOneWidget);
    });

    group('Form Field Integration', () {
      testWidgets('works with form validation', (tester) async {
        final formKey = GlobalKey<FormState>();

        await tester.pumpWidget(
          createTestApp(
            child: Form(
              key: formKey,
              child: ShadcnRadioGroup<String>.formField(
                initialValue: null,
                onChanged: (value) {},
                items: testItems,
                validator: (value) {
                  if (value == null) {
                    return 'Please select an option';
                  }
                  return null;
                },
              ),
            ),
          ),
        );

        // Trigger validation
        expect(formKey.currentState!.validate(), isFalse);
        await tester.pump();

        expect(find.text('Please select an option'), findsOneWidget);
      });

      testWidgets('form field updates correctly', (tester) async {
        final formKey = GlobalKey<FormState>();
        String? formValue;

        await tester.pumpWidget(
          createTestApp(
            child: Form(
              key: formKey,
              onChanged: () {
                formKey.currentState!.save();
              },
              child: ShadcnRadioGroup<String>.formField(
                initialValue: null,
                onChanged: (value) => formValue = value,
                onSaved: (value) => formValue = value,
                items: testItems,
              ),
            ),
          ),
        );

        await tester.tap(find.text('Option 2'));
        await tester.pump();

        formKey.currentState!.save();
        expect(formValue, equals('option2'));
      });
    });

    group('Theme Integration', () {
      testWidgets('applies custom theme correctly', (tester) async {
        const customTheme = ShadcnRadioGroupTheme(
          selectedInnerCircle: Colors.red,
          size: 24.0,
        );

        await tester.pumpWidget(
          createTestApp(
            theme: ThemeData(
              extensions: const [customTheme],
            ),
            child: ShadcnRadioGroup<String>(
              value: 'option1',
              onChanged: (value) {},
              items: testItems,
            ),
          ),
        );

        expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
      });

      testWidgets('falls back to default theme when no custom theme provided', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: 'option1',
              onChanged: (value) {},
              items: testItems,
            ),
          ),
        );

        expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('supports focus', (tester) async {
        final focusNode = FocusNode();

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: null,
              onChanged: (value) {},
              items: testItems,
              focusNode: focusNode,
            ),
          ),
        );

        focusNode.requestFocus();
        await tester.pump();

        expect(focusNode.hasFocus, isTrue);
      });

      testWidgets('can be excluded from semantics', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: null,
              onChanged: (value) {},
              items: testItems,
              excludeFromSemantics: true,
            ),
          ),
        );

        expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
      });
    });

    group('Haptic Feedback', () {
      testWidgets('triggers haptic feedback when enabled', (tester) async {
        final List<MethodCall> log = <MethodCall>[];
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, (methodCall) async {
          log.add(methodCall);
          return null;
        });

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: null,
              onChanged: (value) {},
              items: testItems,
              enableFeedback: true,
            ),
          ),
        );

        await tester.tap(find.text('Option 1'));
        await tester.pump();

        expect(
          log,
          contains(isMethodCall('HapticFeedback.vibrate', arguments: 'HapticFeedbackType.lightImpact')),
        );

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, null);
      });

      testWidgets('does not trigger haptic feedback when disabled', (tester) async {
        final List<MethodCall> log = <MethodCall>[];
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, (methodCall) async {
          log.add(methodCall);
          return null;
        });

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: null,
              onChanged: (value) {},
              items: testItems,
              enableFeedback: false,
            ),
          ),
        );

        await tester.tap(find.text('Option 1'));
        await tester.pump();

        expect(
          log,
          isNot(contains(isMethodCall('HapticFeedback.vibrate', arguments: 'HapticFeedbackType.lightImpact'))),
        );

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, null);
      });
    });

    group('Different Value Types', () {
      testWidgets('works with int values', (tester) async {
        final intItems = [
          const ShadcnRadioGroupItem<int>(value: 1, label: 'One'),
          const ShadcnRadioGroupItem<int>(value: 2, label: 'Two'),
        ];

        int? selectedValue;

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<int>(
              value: selectedValue,
              onChanged: (value) => selectedValue = value,
              items: intItems,
            ),
          ),
        );

        await tester.tap(find.text('Two'));
        await tester.pump();

        expect(selectedValue, equals(2));
      });

      testWidgets('works with enum values', (tester) async {
        final enumItems = [
          const ShadcnRadioGroupItem<TestEnum>(value: TestEnum.first, label: 'First'),
          const ShadcnRadioGroupItem<TestEnum>(value: TestEnum.second, label: 'Second'),
        ];

        TestEnum? selectedValue;

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<TestEnum>(
              value: selectedValue,
              onChanged: (value) => selectedValue = value,
              items: enumItems,
            ),
          ),
        );

        await tester.tap(find.text('First'));
        await tester.pump();

        expect(selectedValue, equals(TestEnum.first));
      });
    });

    group('Edge Cases', () {
      testWidgets('handles empty items list assertion', (tester) async {
        expect(
          () => ShadcnRadioGroup<String>(
            value: null,
            onChanged: (value) {},
            items: const [],
          ),
          throwsA(isA<AssertionError>()),
        );
      });

      testWidgets('handles custom spacing', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: null,
              onChanged: (value) {},
              items: testItems,
              itemSpacing: 20.0,
            ),
          ),
        );

        expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
      });

      testWidgets('handles wrap direction with custom alignment', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnRadioGroup<String>(
              value: null,
              onChanged: (value) {},
              items: testItems,
              direction: ShadcnRadioGroupDirection.wrap,
              wrapAlignment: WrapAlignment.center,
              wrapCrossAlignment: WrapCrossAlignment.start,
              runSpacing: 10.0,
            ),
          ),
        );

        expect(find.byType(ShadcnRadioGroup<String>), findsOneWidget);
      });
    });
  });
}

// Test enum for testing with enum values
enum TestEnum {
  first,
  second,
}