import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnCardTheme', () {
    late ColorScheme lightColorScheme;
    late ColorScheme darkColorScheme;

    setUp(() {
      lightColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      darkColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      );
    });

    group('Construction and Defaults', () {
      test('should create instance with null properties', () {
        const theme = ShadcnCardTheme();
        
        expect(theme.backgroundColor, isNull);
        expect(theme.borderColor, isNull);
        expect(theme.foregroundColor, isNull);
        expect(theme.borderWidth, isNull);
        expect(theme.borderRadius, isNull);
        expect(theme.padding, isNull);
        expect(theme.margin, isNull);
        expect(theme.shadowColor, isNull);
        expect(theme.elevation, isNull);
        expect(theme.boxShadow, isNull);
      });

      test('should create default theme with proper values', () {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        expect(theme.backgroundColor, lightColorScheme.surface);
        expect(theme.borderColor, lightColorScheme.outline);
        expect(theme.foregroundColor, lightColorScheme.onSurface);
        expect(theme.borderWidth, ShadcnTokens.borderWidth);
        expect(theme.borderRadius, ShadcnTokens.borderRadius(ShadcnTokens.radiusLg));
        expect(theme.padding, ShadcnTokens.cardPadding);
        expect(theme.margin, EdgeInsets.zero);
        expect(theme.shadowColor, lightColorScheme.shadow);
        expect(theme.elevation, ShadcnTokens.elevationNone);
      });

      test('should create light theme with optimized colors', () {
        final theme = ShadcnCardTheme.lightTheme(lightColorScheme);
        
        expect(theme.backgroundColor, lightColorScheme.surface);
        expect(theme.borderColor, lightColorScheme.outline.withOpacity(0.2));
        expect(theme.foregroundColor, lightColorScheme.onSurface);
        expect(theme.shadowColor, Colors.black.withOpacity(0.1));
        expect(theme.elevation, ShadcnTokens.elevationSm);
      });

      test('should create dark theme with optimized colors', () {
        final theme = ShadcnCardTheme.darkTheme(darkColorScheme);
        
        expect(theme.backgroundColor, darkColorScheme.surface);
        expect(theme.borderColor, darkColorScheme.outline.withOpacity(0.3));
        expect(theme.foregroundColor, darkColorScheme.onSurface);
        expect(theme.shadowColor, Colors.black.withOpacity(0.3));
        expect(theme.elevation, ShadcnTokens.elevationSm);
      });
    });

    group('Color Resolution', () {
      testWidgets('should resolve background color correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedColor = theme.resolveBackgroundColor(context);
                expect(resolvedColor, lightColorScheme.surface);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should resolve border color correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedColor = theme.resolveBorderColor(context);
                expect(resolvedColor, lightColorScheme.outline);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should resolve foreground color correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedColor = theme.resolveForegroundColor(context);
                expect(resolvedColor, lightColorScheme.onSurface);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should resolve shadow color correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedColor = theme.resolveShadowColor(context);
                expect(resolvedColor, lightColorScheme.shadow);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should use custom color when provided', (tester) async {
        const customColor = Colors.red;
        const theme = ShadcnCardTheme(backgroundColor: customColor);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedColor = theme.resolveBackgroundColor(context);
                expect(resolvedColor, customColor);
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('Property Resolution', () {
      testWidgets('should resolve border radius correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedRadius = theme.resolveCardBorderRadius(context);
                expect(resolvedRadius, ShadcnTokens.borderRadius(ShadcnTokens.radiusLg));
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should resolve padding correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedPadding = theme.resolvePadding(context);
                expect(resolvedPadding, ShadcnTokens.cardPadding);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should resolve margin correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedMargin = theme.resolveMargin(context);
                expect(resolvedMargin, EdgeInsets.zero);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should resolve elevation correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedElevation = theme.resolveElevation(context);
                expect(resolvedElevation, ShadcnTokens.elevationNone);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should resolve border width correctly', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedWidth = theme.resolveBorderWidth(context);
                expect(resolvedWidth, ShadcnTokens.borderWidth);
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('Box Shadow Resolution', () {
      testWidgets('should return null for zero elevation', (tester) async {
        const theme = ShadcnCardTheme(elevation: 0);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedShadow = theme.resolveBoxShadow(context);
                expect(resolvedShadow, isNull);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should generate shadows for positive elevation', (tester) async {
        const theme = ShadcnCardTheme(elevation: 4.0);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedShadow = theme.resolveBoxShadow(context);
                expect(resolvedShadow, isNotNull);
                expect(resolvedShadow!.length, 2);
                expect(resolvedShadow[0].blurRadius, 8.0); // elevation * 2
                expect(resolvedShadow[0].offset.dy, 2.0); // elevation / 2
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should use custom box shadow when provided', (tester) async {
        const customShadow = [BoxShadow(color: Colors.red, blurRadius: 10)];
        const theme = ShadcnCardTheme(boxShadow: customShadow);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final resolvedShadow = theme.resolveBoxShadow(context);
                expect(resolvedShadow, customShadow);
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('Constraints Resolution', () {
      testWidgets('should return null when no constraints set', (tester) async {
        final theme = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final constraints = theme.resolveConstraints(context);
                expect(constraints, isNull);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('should create constraints from dimensions', (tester) async {
        const theme = ShadcnCardTheme(
          minWidth: 100,
          maxWidth: 200,
          minHeight: 150,
          maxHeight: 300,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(colorScheme: lightColorScheme),
            home: Builder(
              builder: (context) {
                final constraints = theme.resolveConstraints(context);
                expect(constraints, isNotNull);
                expect(constraints!.minWidth, 100);
                expect(constraints.maxWidth, 200);
                expect(constraints.minHeight, 150);
                expect(constraints.maxHeight, 300);
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('copyWith', () {
      test('should copy theme with new values', () {
        final original = ShadcnCardTheme.defaultTheme(lightColorScheme);
        const newColor = Colors.red;
        const newElevation = 8.0;
        
        final copied = original.copyWith(
          backgroundColor: newColor,
          elevation: newElevation,
        );
        
        expect(copied.backgroundColor, newColor);
        expect(copied.elevation, newElevation);
        expect(copied.borderColor, original.borderColor);
        expect(copied.borderWidth, original.borderWidth);
      });

      test('should preserve original values when null passed', () {
        final original = ShadcnCardTheme.defaultTheme(lightColorScheme);
        
        final copied = original.copyWith();
        
        expect(copied.backgroundColor, original.backgroundColor);
        expect(copied.borderColor, original.borderColor);
        expect(copied.elevation, original.elevation);
      });
    });

    group('lerp', () {
      test('should interpolate between themes correctly', () {
        final theme1 = ShadcnCardTheme(
          backgroundColor: Colors.red,
          elevation: 0.0,
          borderWidth: 1.0,
        );
        final theme2 = ShadcnCardTheme(
          backgroundColor: Colors.blue,
          elevation: 8.0,
          borderWidth: 3.0,
        );
        
        final lerped = theme1.lerp(theme2, 0.5);
        
        expect(lerped.backgroundColor, Color.lerp(Colors.red, Colors.blue, 0.5));
        expect(lerped.elevation, 4.0);
        expect(lerped.borderWidth, 2.0);
      });

      test('should return original theme when other is not ShadcnCardTheme', () {
        const theme1 = ShadcnCardTheme(backgroundColor: Colors.red);
        // Use null instead of incompatible type
        const theme2 = null;
        
        final result = theme1.lerp(theme2, 0.5);
        
        expect(result, theme1);
      });

      test('should handle null values correctly in lerp', () {
        const theme1 = ShadcnCardTheme(elevation: 4.0);
        const theme2 = ShadcnCardTheme();
        
        final lerped = theme1.lerp(theme2, 0.5);
        
        expect(lerped.elevation, 2.0);
      });
    });

    group('Validation', () {
      test('should validate successfully with valid values', () {
        const theme = ShadcnCardTheme(
          minWidth: 100,
          maxWidth: 200,
          minHeight: 50,
          maxHeight: 150,
          borderWidth: 1.0,
          elevation: 4.0,
        );
        
        expect(theme.validate(), isTrue);
      });

      test('should fail validation with negative dimensions', () {
        const theme = ShadcnCardTheme(minWidth: -10);
        
        expect(theme.validate(), isFalse);
      });

      test('should fail validation when min > max', () {
        const theme = ShadcnCardTheme(minWidth: 200, maxWidth: 100);
        
        expect(theme.validate(), isFalse);
      });

      test('should fail validation with negative border width', () {
        const theme = ShadcnCardTheme(borderWidth: -1.0);
        
        expect(theme.validate(), isFalse);
      });

      test('should fail validation with negative elevation', () {
        const theme = ShadcnCardTheme(elevation: -2.0);
        
        expect(theme.validate(), isFalse);
      });

      test('should throw when validation fails with throwOnError', () {
        const theme = ShadcnCardTheme(elevation: -1.0);
        
        expect(
          () => theme.validate(throwOnError: true),
          throwsArgumentError,
        );
      });
    });

    group('Equality and Hash', () {
      test('should be equal when all properties match', () {
        const theme1 = ShadcnCardTheme(
          backgroundColor: Colors.red,
          elevation: 4.0,
        );
        const theme2 = ShadcnCardTheme(
          backgroundColor: Colors.red,
          elevation: 4.0,
        );
        
        expect(theme1, theme2);
        expect(theme1.hashCode, theme2.hashCode);
      });

      test('should not be equal when properties differ', () {
        const theme1 = ShadcnCardTheme(backgroundColor: Colors.red);
        const theme2 = ShadcnCardTheme(backgroundColor: Colors.blue);
        
        expect(theme1, isNot(theme2));
      });

      test('should be equal to itself', () {
        const theme = ShadcnCardTheme();
        
        expect(theme, theme);
      });
    });

    group('toString', () {
      test('should provide meaningful string representation', () {
        const theme = ShadcnCardTheme(
          backgroundColor: Colors.red,
          elevation: 4.0,
        );
        
        final string = theme.toString();
        
        expect(string, contains('ShadcnCardTheme'));
        expect(string, contains('backgroundColor: ${Colors.red}'));
        expect(string, contains('elevation: 4.0'));
      });
    });
  });
}