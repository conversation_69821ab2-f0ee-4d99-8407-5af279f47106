import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnCard Material Integration', () {
    late ColorScheme lightColorScheme;
    late ColorScheme darkColorScheme;

    setUp(() {
      lightColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      darkColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      );
    });

    Widget buildMaterialApp({
      required Widget child,
      ThemeData? theme,
      ThemeData? darkTheme,
    }) {
      return MaterialApp(
        theme: theme,
        darkTheme: darkTheme,
        home: Scaffold(
          body: child,
        ),
      );
    }

    group('Theme Integration', () {
      testWidgets('should integrate with Material ColorScheme', (tester) async {
        final theme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: const ShadcnCard(
            child: Text('Material Integration'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('Material Integration'), findsOneWidget);
      });

      testWidgets('should work with Material 3 themes', (tester) async {
        final theme = ThemeData(
          useMaterial3: true,
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.lightTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: const ShadcnCard(
            variant: ShadcnCardVariant.elevated,
            child: Text('Material 3 Card'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.byType(Material), findsWidgets); // Should use Material elevation
      });

      testWidgets('should inherit from Material theme when no extension', (tester) async {
        final theme = ThemeData(colorScheme: lightColorScheme);

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: const ShadcnCard(
            child: Text('No Extension'),
          ),
        ));

        // Should still render by falling back to default theme
        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('No Extension'), findsOneWidget);
      });
    });

    group('Light/Dark Theme Switching', () {
      testWidgets('should adapt to theme brightness changes', (tester) async {
        final lightTheme = ThemeData(
          brightness: Brightness.light,
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.lightTheme(lightColorScheme)],
        );

        final darkTheme = ThemeData(
          brightness: Brightness.dark,
          colorScheme: darkColorScheme,
          extensions: [ShadcnCardTheme.darkTheme(darkColorScheme)],
        );

        // Build with light theme
        await tester.pumpWidget(buildMaterialApp(
          theme: lightTheme,
          darkTheme: darkTheme,
          child: const ShadcnCard(
            child: Text('Adaptive Card'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Switch to dark theme by binding to MediaQuery
        await tester.binding.platformDispatcher.clearAllTestValues();
        await tester.pumpWidget(
          MediaQuery(
            data: const MediaQueryData(platformBrightness: Brightness.dark),
            child: buildMaterialApp(
              theme: lightTheme,
              darkTheme: darkTheme,
              child: const ShadcnCard(
                child: Text('Dark Theme Card'),
              ),
            ),
          ),
        );

        expect(find.byType(ShadcnCard), findsOneWidget);
      });

      testWidgets('should use appropriate colors for light theme', (tester) async {
        final lightTheme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.lightTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: lightTheme,
          child: const ShadcnCard(
            variant: ShadcnCardVariant.outlined,
            child: Text('Light Theme Card'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Verify that text inherits proper light theme color
        final textWidget = find.text('Light Theme Card');
        expect(textWidget, findsOneWidget);
      });

      testWidgets('should use appropriate colors for dark theme', (tester) async {
        final darkTheme = ThemeData(
          colorScheme: darkColorScheme,
          extensions: [ShadcnCardTheme.darkTheme(darkColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: darkTheme,
          child: const ShadcnCard(
            variant: ShadcnCardVariant.outlined,
            child: Text('Dark Theme Card'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Verify that text inherits proper dark theme color
        final textWidget = find.text('Dark Theme Card');
        expect(textWidget, findsOneWidget);
      });
    });

    group('Elevation System Integration', () {
      testWidgets('should use Material elevation for elevated cards', (tester) async {
        final theme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: const ShadcnCard(
            variant: ShadcnCardVariant.elevated,
            elevation: 8.0,
            child: Text('Elevated Card'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Should use Material widget for elevation
        final materialWidgets = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(Material),
        );

        expect(materialWidgets, findsWidgets);

        // Verify elevation is applied
        final materials = tester.widgetList<Material>(materialWidgets);
        final elevatedMaterial = materials.firstWhere(
          (material) => material.elevation > 0,
          orElse: () => materials.first,
        );

        expect(elevatedMaterial.elevation, greaterThan(0));
      });

      testWidgets('should respect Material shadow color', (tester) async {
        final customShadowColor = Colors.red.withOpacity(0.5);
        final theme = ThemeData(
          colorScheme: lightColorScheme.copyWith(shadow: customShadowColor),
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: const ShadcnCard(
            variant: ShadcnCardVariant.elevated,
            child: Text('Custom Shadow'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Verify shadow color is used in Material widget
        final materials = tester.widgetList<Material>(
          find.descendant(
            of: find.byType(ShadcnCard),
            matching: find.byType(Material),
          ),
        );

        // Find Material with elevation > 0
        final elevatedMaterial = materials.firstWhere(
          (material) => material.elevation > 0,
          orElse: () => materials.first,
        );

        expect(elevatedMaterial.shadowColor, isNotNull);
      });

      testWidgets('should work with different Material versions', (tester) async {
        final m2Theme = ThemeData(
          useMaterial3: false,
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        final m3Theme = ThemeData(
          useMaterial3: true,
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        // Test Material 2
        await tester.pumpWidget(buildMaterialApp(
          theme: m2Theme,
          child: const ShadcnCard(
            variant: ShadcnCardVariant.elevated,
            child: Text('Material 2 Card'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Test Material 3
        await tester.pumpWidget(buildMaterialApp(
          theme: m3Theme,
          child: const ShadcnCard(
            variant: ShadcnCardVariant.elevated,
            child: Text('Material 3 Card'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);
      });
    });

    group('Visual Density Integration', () {
      testWidgets('should respect visual density changes', (tester) async {
        final theme = ThemeData(
          colorScheme: lightColorScheme,
          visualDensity: VisualDensity.compact,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: const ShadcnCard(
            child: Text('Compact Density'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Test comfortable density
        final comfortableTheme = theme.copyWith(
          visualDensity: VisualDensity.comfortable,
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: comfortableTheme,
          child: const ShadcnCard(
            child: Text('Comfortable Density'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);
      });

      testWidgets('should adapt padding to visual density', (tester) async {
        final compactTheme = ThemeData(
          colorScheme: lightColorScheme,
          visualDensity: VisualDensity.compact,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: compactTheme,
          child: const ShadcnCard(
            child: Text('Density Adaptive'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Padding should be adjusted for visual density
        final paddingWidgets = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(Padding),
        );

        expect(paddingWidgets, findsWidgets);
      });
    });

    group('Accessibility Integration', () {
      testWidgets('should work with Material accessibility features', (tester) async {
        final theme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: const ShadcnCard(
            semanticLabel: 'Information Card',
            child: Text('Accessible Content'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Should have semantic information
        expect(
          find.byWidgetPredicate((widget) =>
            widget is Semantics && 
            widget.properties.label == 'Information Card'
          ),
          findsOneWidget,
        );
      });

      testWidgets('should respect high contrast themes', (tester) async {
        final highContrastTheme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(
          MediaQuery(
            data: const MediaQueryData(
              highContrast: true,
              accessibleNavigation: true,
            ),
            child: buildMaterialApp(
              theme: highContrastTheme,
              child: const ShadcnCard(
                variant: ShadcnCardVariant.outlined,
                child: Text('High Contrast Card'),
              ),
            ),
          ),
        );

        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('High Contrast Card'), findsOneWidget);
      });
    });

    group('Platform Integration', () {
      testWidgets('should adapt to different platforms', (tester) async {
        final theme = ThemeData(
          platform: TargetPlatform.iOS,
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: const ShadcnCard(
            child: Text('iOS Platform'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);

        // Test Android platform
        final androidTheme = theme.copyWith(platform: TargetPlatform.android);

        await tester.pumpWidget(buildMaterialApp(
          theme: androidTheme,
          child: const ShadcnCard(
            child: Text('Android Platform'),
          ),
        ));

        expect(find.byType(ShadcnCard), findsOneWidget);
      });
    });

    group('Complex Material Scenarios', () {
      testWidgets('should work within Material navigation', (tester) async {
        final theme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(MaterialApp(
          theme: theme,
          home: Scaffold(
            appBar: AppBar(title: const Text('Cards Demo')),
            body: const Column(
              children: [
                ShadcnCard(
                  child: Text('Card in AppBar context'),
                ),
                ShadcnCard(
                  variant: ShadcnCardVariant.elevated,
                  child: Text('Elevated card'),
                ),
              ],
            ),
            bottomNavigationBar: BottomNavigationBar(
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.home),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.settings),
                  label: 'Settings',
                ),
              ],
            ),
          ),
        ));

        expect(find.byType(ShadcnCard), findsNWidgets(2));
        expect(find.text('Card in AppBar context'), findsOneWidget);
        expect(find.text('Elevated card'), findsOneWidget);
      });

      testWidgets('should work with Material dialogs and overlays', (tester) async {
        final theme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: Builder(
            builder: (context) => ElevatedButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => const AlertDialog(
                    content: ShadcnCard(
                      child: Text('Card in Dialog'),
                    ),
                  ),
                );
              },
              child: const Text('Show Dialog'),
            ),
          ),
        ));

        // Tap to show dialog
        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Verify card in dialog
        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('Card in Dialog'), findsOneWidget);
      });

      testWidgets('should integrate with Material lists and grids', (tester) async {
        final theme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [ShadcnCardTheme.defaultTheme(lightColorScheme)],
        );

        await tester.pumpWidget(buildMaterialApp(
          theme: theme,
          child: ListView.builder(
            itemCount: 3,
            itemBuilder: (context, index) => ShadcnCard(
              margin: const EdgeInsets.all(8),
              child: ListTile(
                title: Text('Card Item $index'),
                subtitle: const Text('In ListView'),
              ),
            ),
          ),
        ));

        expect(find.byType(ShadcnCard), findsNWidgets(3));
        expect(find.text('Card Item 0'), findsOneWidget);
        expect(find.text('Card Item 1'), findsOneWidget);
        expect(find.text('Card Item 2'), findsOneWidget);
      });
    });
  });
}