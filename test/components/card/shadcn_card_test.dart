import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnCard', () {
    late ColorScheme lightColorScheme;
    late ThemeData lightTheme;
    late ShadcnCardTheme cardTheme;

    setUp(() {
      lightColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      cardTheme = ShadcnCardTheme.defaultTheme(lightColorScheme);
      lightTheme = ThemeData(
        colorScheme: lightColorScheme,
        extensions: [cardTheme],
      );
    });

    Widget buildTestCard({
      Widget? child,
      ShadcnCardVariant? variant,
      VoidCallback? onTap,
      ThemeData? theme,
    }) {
      return MaterialApp(
        theme: theme ?? lightTheme,
        home: Scaffold(
          body: ShadcnCard(
            variant: variant ?? ShadcnCardVariant.defaultCard,
            onTap: onTap,
            child: child ?? const Text('Card Content'),
          ),
        ),
      );
    }

    group('Widget Construction', () {
      testWidgets('should render with required child', (tester) async {
        await tester.pumpWidget(buildTestCard());
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('Card Content'), findsOneWidget);
      });

      testWidgets('should render all variant constructors', (tester) async {
        const child = Text('Test Content');

        // Test default constructor
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard.defaultCard(child: child),
        ));
        expect(find.byType(ShadcnCard), findsOneWidget);

        // Test outlined constructor
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard.outlined(child: child),
        ));
        expect(find.byType(ShadcnCard), findsOneWidget);

        // Test elevated constructor
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard.elevated(child: child),
        ));
        expect(find.byType(ShadcnCard), findsOneWidget);
      });

      testWidgets('should accept custom properties', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: ShadcnCard(
            backgroundColor: Colors.red,
            borderColor: Colors.blue,
            borderWidth: 2.0,
            elevation: 8.0,
            padding: const EdgeInsets.all(32),
            onTap: () {},
            child: const Text('Custom Card'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('Custom Card'), findsOneWidget);
      });
    });

    group('Theme Integration', () {
      testWidgets('should use theme extension when available', (tester) async {
        final customTheme = lightTheme.copyWith(
          extensions: [
            cardTheme.copyWith(
              backgroundColor: Colors.red,
              elevation: 8.0,
            ),
          ],
        );

        await tester.pumpWidget(buildTestCard(theme: customTheme));
        
        // Widget should render without throwing
        expect(find.byType(ShadcnCard), findsOneWidget);
      });

      testWidgets('should fallback to default theme when extension missing', (tester) async {
        final themeWithoutExtension = ThemeData(colorScheme: lightColorScheme);

        await tester.pumpWidget(buildTestCard(theme: themeWithoutExtension));
        
        // Widget should render with default theme
        expect(find.byType(ShadcnCard), findsOneWidget);
      });

      testWidgets('should inherit text style for child content', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            foregroundColor: Colors.red,
            child: Text('Colored Text'),
          ),
        ));
        
        // Find the text widget and verify it inherits color
        final textWidget = tester.widget<Text>(find.text('Colored Text'));
        final defaultTextStyle = DefaultTextStyle.of(
          tester.element(find.text('Colored Text')),
        );
        
        expect(defaultTextStyle.style.color, Colors.red);
      });
    });

    group('Variant Styling', () {
      testWidgets('should render default variant correctly', (tester) async {
        await tester.pumpWidget(buildTestCard(
          variant: ShadcnCardVariant.defaultCard,
        ));
        
        // Should find Material or Container for card implementation
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Verify card renders without elevation (default behavior)
        final containerFinder = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(Container),
        );
        
        // Check that a container exists (non-elevated card)
        expect(containerFinder, findsWidgets);
      });

      testWidgets('should render outlined variant with border', (tester) async {
        await tester.pumpWidget(buildTestCard(
          variant: ShadcnCardVariant.outlined,
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Outlined variant should use Container with border decoration
        final containerFinder = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(Container),
        );
        
        expect(containerFinder, findsWidgets);
      });

      testWidgets('should render elevated variant with Material elevation', (tester) async {
        await tester.pumpWidget(buildTestCard(
          variant: ShadcnCardVariant.elevated,
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Elevated variant should use Material widget for elevation
        final materialFinder = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(Material),
        );
        
        expect(materialFinder, findsWidgets);
      });
    });

    group('Custom Properties Override', () {
      testWidgets('should use custom colors over theme values', (tester) async {
        const customBg = Colors.red;
        const customBorder = Colors.blue;
        const customForeground = Colors.green;

        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            backgroundColor: customBg,
            borderColor: customBorder,
            foregroundColor: customForeground,
            variant: ShadcnCardVariant.outlined,
            child: Text('Custom Colors'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Verify text color inheritance
        final defaultTextStyle = DefaultTextStyle.of(
          tester.element(find.text('Custom Colors')),
        );
        expect(defaultTextStyle.style.color, customForeground);
      });

      testWidgets('should use custom dimensions', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            width: 200,
            height: 150,
            child: Text('Sized Card'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Find ConstrainedBox or Container with size constraints
        final constrainedBox = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(ConstrainedBox),
        );
        
        if (constrainedBox.evaluate().isNotEmpty) {
          final widget = tester.widget<ConstrainedBox>(constrainedBox.first);
          expect(widget.constraints.minWidth, 200);
          expect(widget.constraints.maxWidth, 200);
          expect(widget.constraints.minHeight, 150);
          expect(widget.constraints.maxHeight, 150);
        }
      });

      testWidgets('should use custom padding', (tester) async {
        const customPadding = EdgeInsets.all(32);

        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            padding: customPadding,
            child: Text('Padded Content'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Find the Padding widget that wraps the content
        final paddingFinder = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(Padding),
        );
        
        expect(paddingFinder, findsWidgets);
      });

      testWidgets('should use custom margin', (tester) async {
        const customMargin = EdgeInsets.all(16);

        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            margin: customMargin,
            child: Text('Margined Card'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Card with margin should have Padding wrapper for margin
        final marginPaddingFinder = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(Padding),
        );
        
        expect(marginPaddingFinder, findsWidgets);
      });
    });

    group('Interaction Handling', () {
      testWidgets('should handle tap interactions', (tester) async {
        bool tapped = false;

        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: ShadcnCard(
            onTap: () => tapped = true,
            child: const Text('Tappable Card'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Tap the card
        await tester.tap(find.byType(ShadcnCard));
        await tester.pumpAndSettle();
        
        expect(tapped, isTrue);
      });

      testWidgets('should handle long press interactions', (tester) async {
        bool longPressed = false;

        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: ShadcnCard(
            onLongPress: () => longPressed = true,
            child: const Text('Long Press Card'),
          ),
        ));
        
        // Long press the card
        await tester.longPress(find.byType(ShadcnCard));
        await tester.pumpAndSettle();
        
        expect(longPressed, isTrue);
      });

      testWidgets('should show proper cursor for interactive cards', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: ShadcnCard(
            onTap: () {},
            mouseCursor: SystemMouseCursors.click,
            child: const Text('Clickable Card'),
          ),
        ));
        
        // Find MouseRegion for cursor handling
        final mouseRegionFinder = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(MouseRegion),
        );
        
        expect(mouseRegionFinder, findsOneWidget);
      });

      testWidgets('should not be interactive when no callbacks provided', (tester) async {
        await tester.pumpWidget(buildTestCard());
        
        // Card should not have GestureDetector when not interactive
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Should not have mouse region for non-interactive card
        final mouseRegionFinder = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(MouseRegion),
        );
        
        // Non-interactive card might not have MouseRegion
        expect(mouseRegionFinder, findsNothing);
      });
    });

    group('Accessibility', () {
      testWidgets('should add semantic label when provided', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            semanticLabel: 'Information Card',
            child: Text('Card with label'),
          ),
        ));
        
        // Find semantics with the label
        final semanticsFinder = find.byWidgetPredicate((widget) =>
          widget is Semantics && widget.properties.label == 'Information Card');
        
        expect(semanticsFinder, findsOneWidget);
      });

      testWidgets('should add tooltip when provided', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            tooltip: 'This is a card tooltip',
            child: Text('Card with tooltip'),
          ),
        ));
        
        // Find tooltip widget
        final tooltipFinder = find.byWidgetPredicate((widget) =>
          widget is Tooltip && widget.message == 'This is a card tooltip');
        
        expect(tooltipFinder, findsOneWidget);
      });
    });

    group('Material Integration', () {
      testWidgets('should integrate with Material elevation system', (tester) async {
        await tester.pumpWidget(buildTestCard(
          variant: ShadcnCardVariant.elevated,
        ));
        
        // Elevated cards should use Material widget
        final materialFinder = find.descendant(
          of: find.byType(ShadcnCard),
          matching: find.byType(Material),
        );
        
        expect(materialFinder, findsWidgets);
        
        // Get the Material widget and verify elevation
        final materials = tester.widgetList<Material>(materialFinder);
        final cardMaterial = materials.firstWhere((material) => 
          material.elevation > 0,
          orElse: () => materials.first,
        );
        
        expect(cardMaterial.elevation, greaterThan(0));
      });

      testWidgets('should work without Material ancestor', (tester) async {
        await tester.pumpWidget(
          Directionality(
            textDirection: TextDirection.ltr,
            child: ShadcnCard(
              child: const Text('No Material Ancestor'),
            ),
          ),
        );
        
        // Should render without throwing even without Material ancestor
        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('No Material Ancestor'), findsOneWidget);
      });

      testWidgets('should respect theme brightness changes', (tester) async {
        // Test light theme
        await tester.pumpWidget(buildTestCard(theme: lightTheme));
        expect(find.byType(ShadcnCard), findsOneWidget);
        
        // Test dark theme
        final darkColorScheme = ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        );
        final darkTheme = ThemeData(
          colorScheme: darkColorScheme,
          extensions: [ShadcnCardTheme.darkTheme(darkColorScheme)],
        );
        
        await tester.pumpWidget(buildTestCard(theme: darkTheme));
        expect(find.byType(ShadcnCard), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle missing theme gracefully', (tester) async {
        final themeWithoutExtensions = ThemeData(
          colorScheme: lightColorScheme,
        );

        await tester.pumpWidget(buildTestCard(theme: themeWithoutExtensions));
        
        // Should render with default theme values
        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('Card Content'), findsOneWidget);
      });

      testWidgets('should validate properties in debug mode', (tester) async {
        // This test ensures the validation runs without throwing
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            width: 200,
            height: 150,
            semanticLabel: 'Valid Card',
            child: Text('Valid Content'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle zero dimensions gracefully', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            width: 0,
            height: 0,
            child: Text('Zero Size'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
      });

      testWidgets('should handle very large dimensions', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: const ShadcnCard(
            width: 1000,
            height: 800,
            child: Text('Large Card'),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
      });

      testWidgets('should handle complex child widgets', (tester) async {
        await tester.pumpWidget(MaterialApp(
          theme: lightTheme,
          home: ShadcnCard(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Title'),
                const SizedBox(height: 8),
                const Text('Description'),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {},
                      child: const Text('Action'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
        
        expect(find.byType(ShadcnCard), findsOneWidget);
        expect(find.text('Title'), findsOneWidget);
        expect(find.text('Description'), findsOneWidget);
        expect(find.text('Action'), findsOneWidget);
      });
    });
  });
}