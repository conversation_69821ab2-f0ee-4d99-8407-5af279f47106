import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnCard Basic Tests', () {
    testWidgets('should render with basic setup', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnCard(
              child: Text('Test Card'),
            ),
          ),
        ),
      );
      
      expect(find.byType(ShadcnCard), findsOneWidget);
      expect(find.text('Test Card'), findsOneWidget);
    });

    testWidgets('should render different variants', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ShadcnCard.defaultCard(
                  child: Text('Default'),
                ),
                ShadcnCard.outlined(
                  child: Text('Outlined'),
                ),
                ShadcnCard.elevated(
                  child: Text('Elevated'),
                ),
              ],
            ),
          ),
        ),
      );
      
      expect(find.byType(ShadcnCard), findsNWidgets(3));
      expect(find.text('Default'), findsOneWidget);
      expect(find.text('Outlined'), findsOneWidget);
      expect(find.text('Elevated'), findsOneWidget);
    });

    testWidgets('should handle custom properties', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnCard(
              backgroundColor: Colors.red,
              borderColor: Colors.blue,
              elevation: 8.0,
              child: Text('Custom Card'),
            ),
          ),
        ),
      );
      
      expect(find.byType(ShadcnCard), findsOneWidget);
      expect(find.text('Custom Card'), findsOneWidget);
    });

    testWidgets('should work with theme', (tester) async {
      final theme = ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        extensions: [
          ShadcnCardTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue)
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: theme,
          home: Scaffold(
            body: ShadcnCard(
              child: Text('Themed Card'),
            ),
          ),
        ),
      );
      
      expect(find.byType(ShadcnCard), findsOneWidget);
      expect(find.text('Themed Card'), findsOneWidget);
    });

    testWidgets('should work without theme extensions', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          ),
          home: Scaffold(
            body: ShadcnCard(
              child: Text('No Theme Extension'),
            ),
          ),
        ),
      );
      
      expect(find.byType(ShadcnCard), findsOneWidget);
      expect(find.text('No Theme Extension'), findsOneWidget);
    });
  });
}