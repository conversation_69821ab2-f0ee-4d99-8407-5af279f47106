import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnInput Basic Tests', () {
    testWidgets('creates and renders successfully', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnInput(
              placeholder: 'Test input',
            ),
          ),
        ),
      );

      expect(find.byType(ShadcnInput), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget);
    });

    testWidgets('displays placeholder text', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnInput(
              placeholder: 'Enter your name',
            ),
          ),
        ),
      );

      expect(find.text('Enter your name'), findsOneWidget);
    });

    testWidgets('accepts text input', (tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnInput(
              controller: controller,
              placeholder: 'Test input',
            ),
          ),
        ),
      );

      await tester.enterText(find.byType(TextFormField), 'Hello World');
      expect(controller.text, equals('Hello World'));

      controller.dispose();
    });

    testWidgets('calls onChanged callback', (tester) async {
      String? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnInput(
              placeholder: 'Test input',
              onChanged: (value) {
                changedValue = value;
              },
            ),
          ),
        ),
      );

      await tester.enterText(find.byType(TextFormField), 'Test');
      expect(changedValue, equals('Test'));
    });

    testWidgets('shows error text', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnInput(
              placeholder: 'Test input',
              errorText: 'This is an error',
            ),
          ),
        ),
      );

      expect(find.text('This is an error'), findsOneWidget);
    });

    testWidgets('hasError returns correct value', (tester) async {
      // Test with error
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnInput(
              placeholder: 'Test input',
              errorText: 'Error message',
            ),
          ),
        ),
      );

      final inputWithError = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
      expect(inputWithError.hasError, isTrue);

      // Test without error
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnInput(
              placeholder: 'Test input',
            ),
          ),
        ),
      );

      final inputWithoutError = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
      expect(inputWithoutError.hasError, isFalse);
    });

    testWidgets('factory constructors work', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ShadcnInput.small(placeholder: 'Small'),
                ShadcnInput.medium(placeholder: 'Medium'),
                ShadcnInput.large(placeholder: 'Large'),
                ShadcnInput.password(),
                ShadcnInput.email(),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(ShadcnInput), findsNWidgets(5));
    });

    testWidgets('size variants have correct enum values', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ShadcnInput.small(placeholder: 'Small'),
                ShadcnInput(
                  placeholder: 'Medium',
                  size: ShadcnInputSize.medium,
                ),
                ShadcnInput.large(placeholder: 'Large'),
              ],
            ),
          ),
        ),
      );

      final inputs = tester.widgetList<ShadcnInput>(find.byType(ShadcnInput)).toList();
      expect(inputs[0].size, equals(ShadcnInputSize.small));
      expect(inputs[1].size, equals(ShadcnInputSize.medium));
      expect(inputs[2].size, equals(ShadcnInputSize.large));
    });
  });
}