import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnInput Material Integration', () {
    late ThemeData materialTheme;

    setUp(() {
      materialTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
      );
    });

    Widget createTestWidget({
      Widget? child,
      ThemeData? theme,
    }) {
      return MaterialApp(
        theme: theme ?? materialTheme,
        home: Scaffold(
          body: child ?? const SizedBox(),
        ),
      );
    }

    group('Form Integration', () {
      testWidgets('works with Form widget', (tester) async {
        final formKey = GlobalKey<FormState>();
        String? submittedValue;

        await tester.pumpWidget(
          createTestWidget(
            child: Form(
              key: formKey,
              child: Column(
                children: [
                  ShadcnInput(
                    placeholder: 'Test input',
                    validator: (value) {
                      if (value?.isEmpty == true) {
                        return 'Field is required';
                      }
                      return null;
                    },
                    onSubmitted: (value) {
                      submittedValue = value;
                    },
                  ),
                  ElevatedButton(
                    onPressed: () {
                      if (formKey.currentState?.validate() == true) {
                        formKey.currentState?.save();
                      }
                    },
                    child: const Text('Submit'),
                  ),
                ],
              ),
            ),
          ),
        );

        // Test validation with empty field
        await tester.tap(find.byType(ElevatedButton));
        await tester.pumpAndSettle();
        expect(find.text('Field is required'), findsOneWidget);

        // Test validation with valid input
        await tester.enterText(find.byType(TextFormField), 'Valid input');
        await tester.tap(find.byType(ElevatedButton));
        await tester.pumpAndSettle();
        expect(find.text('Field is required'), findsNothing);
      });

      testWidgets('integrates with FormField behavior', (tester) async {
        final controller = TextEditingController();

        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              controller: controller,
              placeholder: 'Test input',
              autovalidateMode: AutovalidateMode.onUserInteraction,
              validator: (value) {
                if (value?.length != null && value!.length < 3) {
                  return 'Must be at least 3 characters';
                }
                return null;
              },
            ),
          ),
        );

        // Enter text that's too short
        await tester.enterText(find.byType(TextFormField), 'Hi');
        await tester.pump();
        expect(find.text('Must be at least 3 characters'), findsOneWidget);

        // Enter valid text
        await tester.enterText(find.byType(TextFormField), 'Hello');
        await tester.pump();
        expect(find.text('Must be at least 3 characters'), findsNothing);

        controller.dispose();
      });
    });

    group('Material Theme Compatibility', () {
      testWidgets('respects Material color scheme', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
            ),
          ),
        );

        // Should render without errors and use theme colors
        expect(find.byType(ShadcnInput), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('works with Material Design 3', (tester) async {
        final m3Theme = ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.purple,
            brightness: Brightness.light,
          ),
          useMaterial3: true,
        );

        await tester.pumpWidget(
          createTestWidget(
            theme: m3Theme,
            child: const ShadcnInput(
              placeholder: 'M3 input',
            ),
          ),
        );

        // Should work with Material Design 3
        expect(find.byType(ShadcnInput), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('adapts to dark theme', (tester) async {
        final darkTheme = ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
            brightness: Brightness.dark,
          ),
          useMaterial3: true,
        );

        await tester.pumpWidget(
          createTestWidget(
            theme: darkTheme,
            child: const ShadcnInput(
              placeholder: 'Dark theme input',
            ),
          ),
        );

        // Should adapt to dark theme
        expect(find.byType(ShadcnInput), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });
    });

    group('TextField Compatibility', () {
      testWidgets('supports all TextField properties', (tester) async {
        final controller = TextEditingController();
        final focusNode = FocusNode();

        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              controller: controller,
              focusNode: focusNode,
              placeholder: 'Test input',
              maxLength: 10,
              maxLines: 2,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.next,
              obscureText: false,
              readOnly: false,
              enabled: true,
              autofocus: false,
            ),
          ),
        );

        // Verify the input component was created with the properties
        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.controller, equals(controller));
        expect(input.focusNode, equals(focusNode));
        expect(input.maxLength, equals(10));
        expect(input.maxLines, equals(2));
        expect(input.keyboardType, equals(TextInputType.number));
        expect(input.textInputAction, equals(TextInputAction.next));
        expect(input.obscureText, isFalse);
        expect(input.readOnly, isFalse);
        expect(input.enabled, isTrue);
        expect(input.autofocus, isFalse);

        controller.dispose();
        focusNode.dispose();
      });

      testWidgets('maintains TextField focus behavior', (tester) async {
        final focusNode = FocusNode();

        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              focusNode: focusNode,
              placeholder: 'Test input',
            ),
          ),
        );

        expect(focusNode.hasFocus, isFalse);

        // Tap to focus
        await tester.tap(find.byType(TextFormField));
        await tester.pump();
        expect(focusNode.hasFocus, isTrue);

        // Tap elsewhere to unfocus
        await tester.tapAt(const Offset(10, 10));
        await tester.pump();
        expect(focusNode.hasFocus, isFalse);

        focusNode.dispose();
      });

      testWidgets('supports text selection and editing', (tester) async {
        final controller = TextEditingController(text: 'Initial text');

        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              controller: controller,
              placeholder: 'Test input',
            ),
          ),
        );

        // Verify initial text
        expect(find.text('Initial text'), findsOneWidget);

        // Select all and replace
        await tester.tap(find.byType(TextFormField));
        await tester.pump();
        
        // Simulate text replacement
        controller.selection = TextSelection(
          baseOffset: 0,
          extentOffset: controller.text.length,
        );
        await tester.enterText(find.byType(TextFormField), 'New text');
        expect(controller.text, equals('New text'));

        controller.dispose();
      });
    });

    group('InputDecoration Compatibility', () {
      testWidgets('respects custom InputDecoration', (tester) async {
        const customDecoration = InputDecoration(
          border: UnderlineInputBorder(),
          filled: false,
          hintText: 'Custom hint',
          labelText: 'Custom label',
        );

        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              decoration: customDecoration,
            ),
          ),
        );

        // Verify the input component was created with custom decoration
        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.decoration, equals(customDecoration));
      });

      testWidgets('builds proper decoration from theme', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              helperText: 'Helper text',
              errorText: 'Error text',
              label: 'Label',
              prefixIcon: Icon(Icons.search),
              suffixIcon: Icon(Icons.clear),
            ),
          ),
        );

        // Verify the input component was created with the properties
        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.placeholder, equals('Test input'));
        expect(input.helperText, equals('Helper text'));
        expect(input.errorText, equals('Error text'));
        expect(input.label, equals('Label'));
        expect(input.prefixIcon, isA<Icon>());
        expect(input.suffixIcon, isA<Icon>());
      });
    });

    group('Accessibility Integration', () {
      testWidgets('maintains Material accessibility features', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Accessible input',
              label: 'Username',
              semanticLabel: 'Enter your username',
            ),
          ),
        );

        // Find semantic information
        final semantics = tester.widget<Semantics>(
          find.descendant(
            of: find.byType(ShadcnInput),
            matching: find.byType(Semantics),
          ),
        );

        expect(semantics.properties.label, equals('Enter your username'));
        expect(semantics.properties.textField, isTrue);
      });

      testWidgets('supports screen readers', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Email address',
              helperText: 'We will never share your email',
              errorText: null,
            ),
          ),
        );

        // Verify helper text is accessible
        expect(find.text('We will never share your email'), findsOneWidget);
      });
    });

    group('Performance Integration', () {
      testWidgets('does not cause unnecessary rebuilds', (tester) async {
        int buildCount = 0;
        final controller = TextEditingController();

        await tester.pumpWidget(
          createTestWidget(
            child: Builder(
              builder: (context) {
                buildCount++;
                return ShadcnInput(
                  controller: controller,
                  placeholder: 'Performance test',
                );
              },
            ),
          ),
        );

        final initialBuildCount = buildCount;

        // Type text - should not cause parent rebuilds
        await tester.enterText(find.byType(TextFormField), 'Test');
        await tester.pump();

        expect(buildCount, equals(initialBuildCount));

        controller.dispose();
      });
    });

    group('Edge Cases', () {
      testWidgets('handles null theme extension gracefully', (tester) async {
        final themeWithoutExtension = ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.red),
          // No ShadcnInputTheme extension
        );

        await tester.pumpWidget(
          createTestWidget(
            theme: themeWithoutExtension,
            child: const ShadcnInput(
              placeholder: 'No theme extension',
            ),
          ),
        );

        // Should still render without errors
        expect(find.byType(ShadcnInput), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('handles extreme theme values', (tester) async {
        final extremeTheme = materialTheme.copyWith(
          extensions: [
            ShadcnInputTheme(
              backgroundColor: Colors.transparent,
              borderColor: Colors.transparent,
              textColor: Colors.transparent,
              smallHeight: 0.0,
              mediumHeight: 1000.0,
              largeHeight: 0.0,
            ),
          ],
        );

        await tester.pumpWidget(
          createTestWidget(
            theme: extremeTheme,
            child: const ShadcnInput(
              placeholder: 'Extreme theme',
            ),
          ),
        );

        // Should render without throwing exceptions
        expect(find.byType(ShadcnInput), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });
    });
  });
}