import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnInput', () {
    late ThemeData lightTheme;
    late ThemeData darkTheme;

    setUp(() {
      lightTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        extensions: [
          ShadcnInputTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.light),
          ),
        ],
      );
      
      darkTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        extensions: [
          ShadcnInputTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.dark),
          ),
        ],
      );
    });

    Widget createTestWidget({
      Widget? child,
      ThemeData? theme,
    }) {
      return MaterialApp(
        theme: theme ?? lightTheme,
        home: Scaffold(
          body: child ?? const SizedBox(),
        ),
      );
    }

    group('Basic Construction', () {
      testWidgets('creates with minimal parameters', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
            ),
          ),
        );

        expect(find.byType(ShadcnInput), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('creates with all parameters', (tester) async {
        final controller = TextEditingController();
        final focusNode = FocusNode();

        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              controller: controller,
              focusNode: focusNode,
              placeholder: 'Test input',
              helperText: 'Helper text',
              errorText: 'Error text',
              label: 'Label',
              size: ShadcnInputSize.large,
              enabled: true,
              obscureText: false,
              readOnly: false,
              autofocus: false,
              maxLength: 100,
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.done,
              semanticLabel: 'Test input field',
              onChanged: (value) {},
              onSubmitted: (value) {},
              onFocusChange: (hasFocus) {},
            ),
          ),
        );

        expect(find.byType(ShadcnInput), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);

        controller.dispose();
        focusNode.dispose();
      });
    });

    group('Factory Constructors', () {
      testWidgets('ShadcnInput.small() creates small input', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput.small(
              placeholder: 'Small input',
            ),
          ),
        );

        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.size, equals(ShadcnInputSize.small));
      });

      testWidgets('ShadcnInput.medium() creates medium input', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput.medium(
              placeholder: 'Medium input',
            ),
          ),
        );

        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.size, equals(ShadcnInputSize.medium));
      });

      testWidgets('ShadcnInput.large() creates large input', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput.large(
              placeholder: 'Large input',
            ),
          ),
        );

        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.size, equals(ShadcnInputSize.large));
      });

      testWidgets('ShadcnInput.password() creates password input', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput.password(),
          ),
        );

        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.obscureText, isTrue);
        expect(input.keyboardType, equals(TextInputType.visiblePassword));
        expect(input.textInputAction, equals(TextInputAction.done));
      });

      testWidgets('ShadcnInput.email() creates email input', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput.email(),
          ),
        );

        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.keyboardType, equals(TextInputType.emailAddress));
        expect(input.textInputAction, equals(TextInputAction.next));
        expect(input.textCapitalization, equals(TextCapitalization.none));
      });
    });

    group('Size Variants', () {
      testWidgets('small size has correct dimensions', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput.small(
              placeholder: 'Small input',
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify the input uses small height
        final sizedBox = tester.widget<SizedBox?>(
          find.descendant(
            of: find.byType(ShadcnInput),
            matching: find.byType(SizedBox),
          ).first,
        );
        
        expect(sizedBox?.height, equals(ShadcnTokens.inputHeightSm));
      });

      testWidgets('medium size has correct dimensions', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Medium input',
              size: ShadcnInputSize.medium,
            ),
          ),
        );

        await tester.pumpAndSettle();

        final sizedBox = tester.widget<SizedBox?>(
          find.descendant(
            of: find.byType(ShadcnInput),
            matching: find.byType(SizedBox),
          ).first,
        );
        
        expect(sizedBox?.height, equals(ShadcnTokens.inputHeightMd));
      });

      testWidgets('large size has correct dimensions', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput.large(
              placeholder: 'Large input',
            ),
          ),
        );

        await tester.pumpAndSettle();

        final sizedBox = tester.widget<SizedBox?>(
          find.descendant(
            of: find.byType(ShadcnInput),
            matching: find.byType(SizedBox),
          ).first,
        );
        
        expect(sizedBox?.height, equals(ShadcnTokens.inputHeightLg));
      });
    });

    group('Input Behavior', () {
      testWidgets('accepts text input', (tester) async {
        final controller = TextEditingController();

        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              controller: controller,
              placeholder: 'Test input',
            ),
          ),
        );

        await tester.enterText(find.byType(TextFormField), 'Hello World');
        expect(controller.text, equals('Hello World'));

        controller.dispose();
      });

      testWidgets('calls onChanged callback', (tester) async {
        String? changedValue;
        
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              placeholder: 'Test input',
              onChanged: (value) {
                changedValue = value;
              },
            ),
          ),
        );

        await tester.enterText(find.byType(TextFormField), 'Test');
        expect(changedValue, equals('Test'));
      });

      testWidgets('calls onSubmitted callback', (tester) async {
        String? submittedValue;
        
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              placeholder: 'Test input',
              onSubmitted: (value) {
                submittedValue = value;
              },
            ),
          ),
        );

        await tester.enterText(find.byType(TextFormField), 'Test');
        await tester.testTextInput.receiveAction(TextInputAction.done);
        expect(submittedValue, equals('Test'));
      });

      testWidgets('handles focus changes', (tester) async {
        final focusNode = FocusNode();
        bool? hasFocus;
        
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              focusNode: focusNode,
              placeholder: 'Test input',
              onFocusChange: (focus) {
                hasFocus = focus;
              },
            ),
          ),
        );

        focusNode.requestFocus();
        await tester.pump();
        expect(hasFocus, isTrue);

        focusNode.unfocus();
        await tester.pump();
        expect(hasFocus, isFalse);

        focusNode.dispose();
      });
    });

    group('States', () {
      testWidgets('shows placeholder text', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Enter text here',
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.decoration?.hintText, equals('Enter text here'));
      });

      testWidgets('shows helper text', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              helperText: 'This is helper text',
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.decoration?.helperText, equals('This is helper text'));
      });

      testWidgets('shows error text', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              errorText: 'This is an error',
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.decoration?.errorText, equals('This is an error'));
      });

      testWidgets('shows label text', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              label: 'Input Label',
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.decoration?.labelText, equals('Input Label'));
      });

      testWidgets('handles disabled state', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              enabled: false,
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.enabled, isFalse);
      });

      testWidgets('handles read-only state', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              readOnly: true,
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.readOnly, isTrue);
      });

      testWidgets('handles obscured text', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Password',
              obscureText: true,
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.obscureText, isTrue);
      });
    });

    group('Icons and Decoration', () {
      testWidgets('shows prefix icon', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              prefixIcon: Icon(Icons.search),
            ),
          ),
        );

        expect(find.byIcon(Icons.search), findsOneWidget);
        
        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.decoration?.prefixIcon, isNotNull);
      });

      testWidgets('shows suffix icon', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              suffixIcon: Icon(Icons.clear),
            ),
          ),
        );

        expect(find.byIcon(Icons.clear), findsOneWidget);
        
        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.decoration?.suffixIcon, isNotNull);
      });
    });

    group('Validation', () {
      testWidgets('runs validator function', (tester) async {
        String? validatorResult;
        
        await tester.pumpWidget(
          createTestWidget(
            child: ShadcnInput(
              placeholder: 'Test input',
              validator: (value) {
                if (value?.isEmpty == true) {
                  return 'Field is required';
                }
                return null;
              },
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        validatorResult = textFormField.validator?.call('');
        expect(validatorResult, equals('Field is required'));

        validatorResult = textFormField.validator?.call('Valid');
        expect(validatorResult, isNull);
      });
    });

    group('Theme Integration', () {
      testWidgets('inherits theme colors in light mode', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            theme: lightTheme,
            child: const ShadcnInput(
              placeholder: 'Test input',
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        
        // Verify the input uses theme colors
        expect(textFormField.decoration?.fillColor, equals(lightTheme.colorScheme.surface));
        expect(textFormField.cursorColor, equals(lightTheme.colorScheme.primary));
      });

      testWidgets('inherits theme colors in dark mode', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            theme: darkTheme,
            child: const ShadcnInput(
              placeholder: 'Test input',
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        
        // Verify the input uses dark theme colors
        expect(textFormField.decoration?.fillColor, equals(darkTheme.colorScheme.surface));
        expect(textFormField.cursorColor, equals(darkTheme.colorScheme.primary));
      });

      testWidgets('applies custom theme extension', (tester) async {
        final customTheme = lightTheme.copyWith(
          extensions: [
            ShadcnInputTheme(
              backgroundColor: Colors.red,
              borderColor: Colors.green,
              focusedBorderColor: Colors.blue,
            ),
          ],
        );

        await tester.pumpWidget(
          createTestWidget(
            theme: customTheme,
            child: const ShadcnInput(
              placeholder: 'Test input',
            ),
          ),
        );

        final textFormField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textFormField.decoration?.fillColor, equals(Colors.red));
      });
    });

    group('Accessibility', () {
      testWidgets('has proper semantic label', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              semanticLabel: 'Username input field',
            ),
          ),
        );

        final semantics = tester.widget<Semantics>(
          find.descendant(
            of: find.byType(ShadcnInput),
            matching: find.byType(Semantics),
          ),
        );
        expect(semantics.properties.label, equals('Username input field'));
      });

      testWidgets('can be excluded from semantics', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              excludeFromSemantics: true,
            ),
          ),
        );

        // Should not add semantic label when excluded
        expect(
          find.descendant(
            of: find.byType(ShadcnInput),
            matching: find.byType(Semantics),
          ),
          findsNothing,
        );
      });
    });

    group('Error Handling', () {
      testWidgets('hasError returns true when errorText is provided', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              errorText: 'Error message',
            ),
          ),
        );

        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.hasError, isTrue);
      });

      testWidgets('hasError returns false when errorText is null', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
            ),
          ),
        );

        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.hasError, isFalse);
      });

      testWidgets('hasError returns false when errorText is empty', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const ShadcnInput(
              placeholder: 'Test input',
              errorText: '',
            ),
          ),
        );

        final input = tester.widget<ShadcnInput>(find.byType(ShadcnInput));
        expect(input.hasError, isFalse);
      });
    });
  });
}