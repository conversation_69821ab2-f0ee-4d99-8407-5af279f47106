import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/theme/extensions/shadcn_input_theme.dart';
import 'package:shadcn/src/constants/shadcn_tokens.dart';

void main() {
  group('ShadcnInputTheme', () {
    late ColorScheme lightColorScheme;
    late ColorScheme darkColorScheme;

    setUp(() {
      lightColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      darkColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      );
    });

    group('defaultTheme()', () {
      test('creates correct light theme', () {
        final theme = ShadcnInputTheme.defaultTheme(lightColorScheme);

        expect(theme.backgroundColor, equals(lightColorScheme.surface));
        expect(theme.borderColor, equals(lightColorScheme.outline));
        expect(theme.focusedBorderColor, equals(lightColorScheme.primary));
        expect(theme.textColor, equals(lightColorScheme.onSurface));
        expect(theme.placeholderColor, equals(lightColorScheme.onSurface.withOpacity(0.6)));
        expect(theme.errorBorderColor, equals(lightColorScheme.error));
        expect(theme.errorMessageColor, equals(lightColorScheme.error));
      });

      test('creates correct dark theme', () {
        final theme = ShadcnInputTheme.defaultTheme(darkColorScheme);

        expect(theme.backgroundColor, equals(darkColorScheme.surface));
        expect(theme.disabledBackgroundColor, equals(darkColorScheme.surface.withOpacity(0.12)));
        expect(theme.borderColor, equals(darkColorScheme.outline));
        expect(theme.focusedBorderColor, equals(darkColorScheme.primary));
        expect(theme.textColor, equals(darkColorScheme.onSurface));
        expect(theme.placeholderColor, equals(darkColorScheme.onSurface.withOpacity(0.6)));
      });

      test('uses shadcn-standard dimensions', () {
        final theme = ShadcnInputTheme.defaultTheme(lightColorScheme);

        expect(theme.smallHeight, equals(ShadcnTokens.inputHeightSm));
        expect(theme.mediumHeight, equals(ShadcnTokens.inputHeightMd));
        expect(theme.largeHeight, equals(ShadcnTokens.inputHeightLg));
        expect(theme.borderWidth, equals(ShadcnTokens.borderWidth));
        expect(theme.borderRadius, equals(BorderRadius.circular(ShadcnTokens.radiusMd)));
      });

      test('includes proper text styling', () {
        final theme = ShadcnInputTheme.defaultTheme(lightColorScheme);

        expect(theme.textStyle?.fontSize, equals(ShadcnTokens.fontSizeMd));
        expect(theme.textStyle?.fontWeight, equals(ShadcnTokens.fontWeightNormal));
        expect(theme.textStyle?.height, equals(ShadcnTokens.lineHeightNormal));
        expect(theme.placeholderStyle?.fontSize, equals(ShadcnTokens.fontSizeMd));
        expect(theme.errorTextStyle?.fontSize, equals(ShadcnTokens.fontSizeSm));
      });

      test('includes icon sizing', () {
        final theme = ShadcnInputTheme.defaultTheme(lightColorScheme);

        expect(theme.smallIconSize, equals(ShadcnTokens.iconSizeSm));
        expect(theme.mediumIconSize, equals(ShadcnTokens.iconSizeMd));
        expect(theme.largeIconSize, equals(ShadcnTokens.iconSizeLg));
        expect(theme.iconColor, equals(lightColorScheme.onSurface.withOpacity(0.6)));
        expect(theme.disabledIconColor, equals(lightColorScheme.onSurface.withOpacity(0.38)));
      });

      test('includes animation properties', () {
        final theme = ShadcnInputTheme.defaultTheme(lightColorScheme);

        expect(theme.animationDuration, equals(ShadcnTokens.durationFast));
        expect(theme.animationCurve, equals(Curves.easeInOut));
      });
    });

    group('copyWith()', () {
      test('copies all properties correctly', () {
        final original = ShadcnInputTheme.defaultTheme(lightColorScheme);
        final copy = original.copyWith(
          backgroundColor: Colors.red,
          borderColor: Colors.green,
          textColor: Colors.blue,
          smallHeight: 30.0,
          borderRadius: BorderRadius.circular(10),
        );

        expect(copy.backgroundColor, equals(Colors.red));
        expect(copy.borderColor, equals(Colors.green));
        expect(copy.textColor, equals(Colors.blue));
        expect(copy.smallHeight, equals(30.0));
        expect(copy.borderRadius, equals(BorderRadius.circular(10)));

        // Unchanged properties should remain the same
        expect(copy.focusedBorderColor, equals(original.focusedBorderColor));
        expect(copy.mediumHeight, equals(original.mediumHeight));
        expect(copy.largeHeight, equals(original.largeHeight));
      });

      test('handles null values correctly', () {
        final original = ShadcnInputTheme.defaultTheme(lightColorScheme);
        final copy = original.copyWith();

        expect(copy.backgroundColor, equals(original.backgroundColor));
        expect(copy.borderColor, equals(original.borderColor));
        expect(copy.textColor, equals(original.textColor));
        expect(copy.smallHeight, equals(original.smallHeight));
        expect(copy.mediumHeight, equals(original.mediumHeight));
        expect(copy.largeHeight, equals(original.largeHeight));
      });
    });

    group('lerp()', () {
      test('interpolates colors correctly', () {
        final theme1 = ShadcnInputTheme(
          backgroundColor: Colors.red,
          borderColor: Colors.blue,
          textColor: Colors.green,
        );
        final theme2 = ShadcnInputTheme(
          backgroundColor: Colors.yellow,
          borderColor: Colors.purple,
          textColor: Colors.orange,
        );

        final lerped = theme1.lerp(theme2, 0.5);

        expect(lerped.backgroundColor, equals(Color.lerp(Colors.red, Colors.yellow, 0.5)));
        expect(lerped.borderColor, equals(Color.lerp(Colors.blue, Colors.purple, 0.5)));
        expect(lerped.textColor, equals(Color.lerp(Colors.green, Colors.orange, 0.5)));
      });

      test('interpolates dimensions correctly', () {
        final theme1 = ShadcnInputTheme(
          smallHeight: 20.0,
          mediumHeight: 30.0,
          borderWidth: 1.0,
        );
        final theme2 = ShadcnInputTheme(
          smallHeight: 40.0,
          mediumHeight: 50.0,
          borderWidth: 3.0,
        );

        final lerped = theme1.lerp(theme2, 0.5);

        expect(lerped.smallHeight, equals(30.0));
        expect(lerped.mediumHeight, equals(40.0));
        expect(lerped.borderWidth, equals(2.0));
      });

      test('interpolates edge insets correctly', () {
        final theme1 = ShadcnInputTheme(
          smallPadding: const EdgeInsets.all(4),
          mediumPadding: const EdgeInsets.all(8),
        );
        final theme2 = ShadcnInputTheme(
          smallPadding: const EdgeInsets.all(12),
          mediumPadding: const EdgeInsets.all(16),
        );

        final lerped = theme1.lerp(theme2, 0.5);

        expect(lerped.smallPadding, equals(const EdgeInsets.all(8)));
        expect(lerped.mediumPadding, equals(const EdgeInsets.all(12)));
      });

      test('handles null other theme', () {
        final theme = ShadcnInputTheme.defaultTheme(lightColorScheme);
        final lerped = theme.lerp(null, 0.5);

        expect(lerped, equals(theme));
      });
    });

    group('equality and hashCode', () {
      test('equals returns true for identical themes', () {
        final theme1 = ShadcnInputTheme.defaultTheme(lightColorScheme);
        final theme2 = ShadcnInputTheme.defaultTheme(lightColorScheme);

        expect(theme1, equals(theme2));
        expect(theme1.hashCode, equals(theme2.hashCode));
      });

      test('equals returns false for different themes', () {
        final theme1 = ShadcnInputTheme.defaultTheme(lightColorScheme);
        final theme2 = theme1.copyWith(backgroundColor: Colors.red);

        expect(theme1, isNot(equals(theme2)));
        expect(theme1.hashCode, isNot(equals(theme2.hashCode)));
      });
    });

    group('ShadcnInputSize enum', () {
      test('has all required values', () {
        expect(ShadcnInputSize.values.length, equals(3));
        expect(ShadcnInputSize.values, contains(ShadcnInputSize.small));
        expect(ShadcnInputSize.values, contains(ShadcnInputSize.medium));
        expect(ShadcnInputSize.values, contains(ShadcnInputSize.large));
      });
    });
  });
}