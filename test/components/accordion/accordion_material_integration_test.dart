import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnAccordion Material Integration Tests', () {
    testWidgets('adapts to light and dark themes', (WidgetTester tester) async {
      const accordionWidget = ShadcnAccordion(
        expandedValue: 'item1',
        items: [
          ShadcnAccordionItem(
            value: 'item1',
            header: Text('Header 1'),
            content: Text('Content 1'),
          ),
        ],
      );

      // Test light theme
      final lightTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        extensions: [
          ShadcnAccordionTheme.lightTheme(
            ColorScheme.fromSeed(
              seedColor: Colors.blue,
              brightness: Brightness.light,
            ),
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme,
          home: const Scaffold(body: accordionWidget),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Header 1'), findsOneWidget);
      expect(find.text('Content 1'), findsOneWidget);

      // Test dark theme
      final darkTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        extensions: [
          ShadcnAccordionTheme.darkTheme(
            ColorScheme.fromSeed(
              seedColor: Colors.blue,
              brightness: Brightness.dark,
            ),
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: darkTheme,
          home: const Scaffold(body: accordionWidget),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Header 1'), findsOneWidget);
      expect(find.text('Content 1'), findsOneWidget);
    });

    testWidgets('uses Material theme colors when no extension provided', (WidgetTester tester) async {
      final theme = ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.purple),
        useMaterial3: true,
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: theme,
          home: const Scaffold(
            body: ShadcnAccordion(
              expandedValue: 'item1',
              items: [
                ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should render without errors even without theme extension
      expect(find.text('Header 1'), findsOneWidget);
      expect(find.text('Content 1'), findsOneWidget);
    });

    testWidgets('responds to theme changes', (WidgetTester tester) async {
      String? currentExpanded = 'item1';
      
      Widget buildApp(ThemeData theme) {
        return MaterialApp(
          theme: theme,
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        );
      }

      // Start with light theme
      final lightTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        extensions: [
          ShadcnAccordionTheme.lightTheme(
            ColorScheme.fromSeed(
              seedColor: Colors.blue,
              brightness: Brightness.light,
            ),
          ),
        ],
      );

      await tester.pumpWidget(buildApp(lightTheme));
      await tester.pumpAndSettle();

      expect(find.text('Content 1'), findsOneWidget);

      // Switch to dark theme
      final darkTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        extensions: [
          ShadcnAccordionTheme.darkTheme(
            ColorScheme.fromSeed(
              seedColor: Colors.blue,
              brightness: Brightness.dark,
            ),
          ),
        ],
      );

      await tester.pumpWidget(buildApp(darkTheme));
      await tester.pumpAndSettle();

      // Should still be expanded and visible
      expect(find.text('Content 1'), findsOneWidget);
    });

    testWidgets('maintains focus state across theme changes', (WidgetTester tester) async {
      final ValueNotifier<bool> isDarkTheme = ValueNotifier(false);
      
      await tester.pumpWidget(
        ValueListenableBuilder<bool>(
          valueListenable: isDarkTheme,
          builder: (context, isDark, _) {
            final colorScheme = ColorScheme.fromSeed(
              seedColor: Colors.blue,
              brightness: isDark ? Brightness.dark : Brightness.light,
            );
            
            return MaterialApp(
              theme: ThemeData(
                colorScheme: colorScheme,
                extensions: [
                  isDark 
                    ? ShadcnAccordionTheme.darkTheme(colorScheme)
                    : ShadcnAccordionTheme.lightTheme(colorScheme),
                ],
              ),
              home: const Scaffold(
                body: ShadcnAccordion(
                  items: [
                    ShadcnAccordionItem(
                      value: 'item1',
                      header: Text('Header 1'),
                      content: Text('Content 1'),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );

      // Focus on the accordion header
      await tester.tap(find.text('Header 1'));
      await tester.pump();

      // Switch theme
      isDarkTheme.value = true;
      await tester.pump();

      // Header should still be there and focusable
      expect(find.text('Header 1'), findsOneWidget);
      
      // Should be able to interact with it
      await tester.tap(find.text('Header 1'));
      await tester.pumpAndSettle();
    });

    testWidgets('works with Material 3 theming', (WidgetTester tester) async {
      final theme = ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.green,
          brightness: Brightness.light,
        ),
        extensions: [
          ShadcnAccordionTheme.defaultTheme(
            ColorScheme.fromSeed(
              seedColor: Colors.green,
              brightness: Brightness.light,
            ),
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: theme,
          home: const Scaffold(
            body: ShadcnAccordion(
              expandedValue: 'item1',
              items: [
                ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Header 1'), findsOneWidget);
      expect(find.text('Content 1'), findsOneWidget);
    });

    testWidgets('preserves visual density settings', (WidgetTester tester) async {
      final theme = ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        visualDensity: VisualDensity.compact,
        extensions: [
          ShadcnAccordionTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue),
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: theme,
          home: const Scaffold(
            body: ShadcnAccordion(
              expandedValue: 'item1',
              items: [
                ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should render without issues with compact density
      expect(find.text('Header 1'), findsOneWidget);
      expect(find.text('Content 1'), findsOneWidget);
    });
  });
}