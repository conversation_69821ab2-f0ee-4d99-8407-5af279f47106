import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnAccordion Widget Tests', () {
    late ThemeData testTheme;
    late ShadcnAccordionTheme accordionTheme;

    setUp(() {
      testTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      );
      accordionTheme = ShadcnAccordionTheme.defaultTheme(testTheme.colorScheme);
    });

    testWidgets('renders accordion with basic items', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
                const ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Header 2'),
                  content: Text('Content 2'),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Header 1'), findsOneWidget);
      expect(find.text('Header 2'), findsOneWidget);
      expect(find.text('Content 1'), findsNothing); // Content should be hidden initially
      expect(find.text('Content 2'), findsNothing);
    });

    testWidgets('expands and collapses items in single mode', (WidgetTester tester) async {
      String? currentExpanded;

      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
                const ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Header 2'),
                  content: Text('Content 2'),
                ),
              ],
            ),
          ),
        ),
      );

      // Initially no content visible
      expect(find.text('Content 1'), findsNothing);
      expect(find.text('Content 2'), findsNothing);

      // Tap first header to expand
      await tester.tap(find.text('Header 1'));
      await tester.pump();

      // Update widget with new expanded value
      currentExpanded = 'item1';
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
                const ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Header 2'),
                  content: Text('Content 2'),
                ),
              ],
            ),
          ),
        ),
      );

      // Advance animations
      await tester.pumpAndSettle();

      // Now first content should be visible
      expect(find.text('Content 1'), findsOneWidget);
      expect(find.text('Content 2'), findsNothing);
    });

    testWidgets('supports multiple expansion mode', (WidgetTester tester) async {
      Set<String> currentExpanded = {};

      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              allowMultipleExpanded: true,
              expandedValues: currentExpanded,
              onExpandedValuesChanged: (values) {
                currentExpanded = values;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
                const ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Header 2'),
                  content: Text('Content 2'),
                ),
              ],
            ),
          ),
        ),
      );

      // Tap first header
      await tester.tap(find.text('Header 1'));
      await tester.pump();

      // Update with first item expanded
      currentExpanded = {'item1'};
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              allowMultipleExpanded: true,
              expandedValues: currentExpanded,
              onExpandedValuesChanged: (values) {
                currentExpanded = values;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
                const ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Header 2'),
                  content: Text('Content 2'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap second header
      await tester.tap(find.text('Header 2'));
      await tester.pump();

      // Update with both items expanded
      currentExpanded = {'item1', 'item2'};
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              allowMultipleExpanded: true,
              expandedValues: currentExpanded,
              onExpandedValuesChanged: (values) {
                currentExpanded = values;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
                const ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Header 2'),
                  content: Text('Content 2'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Both contents should be visible
      expect(find.text('Content 1'), findsOneWidget);
      expect(find.text('Content 2'), findsOneWidget);
    });

    testWidgets('handles keyboard navigation', (WidgetTester tester) async {
      String? currentExpanded;

      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      // Focus on the first item
      await tester.tap(find.text('Header 1'));
      await tester.pump();

      // Send Enter key event
      await tester.sendKeyEvent(LogicalKeyboardKey.enter);
      await tester.pump();

      // Update widget with expanded state
      currentExpanded = 'item1';
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Content should be visible after keyboard activation
      expect(find.text('Content 1'), findsOneWidget);
    });

    testWidgets('respects disabled state', (WidgetTester tester) async {
      String? currentExpanded;

      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              disabled: true,
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      // Try to tap disabled accordion
      await tester.tap(find.text('Header 1'));
      await tester.pumpAndSettle();

      // Content should not be visible
      expect(find.text('Content 1'), findsNothing);
      expect(currentExpanded, isNull);
    });

    testWidgets('respects individual item disabled state', (WidgetTester tester) async {
      String? currentExpanded;

      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                  disabled: true,
                ),
                const ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Header 2'),
                  content: Text('Content 2'),
                ),
              ],
            ),
          ),
        ),
      );

      // Try to tap disabled item
      await tester.tap(find.text('Header 1'));
      await tester.pumpAndSettle();

      // Content should not be visible
      expect(find.text('Content 1'), findsNothing);
      expect(currentExpanded, isNull);

      // But enabled item should work
      await tester.tap(find.text('Header 2'));
      await tester.pump();

      currentExpanded = 'item2';
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                  disabled: true,
                ),
                const ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Header 2'),
                  content: Text('Content 2'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Content 2'), findsOneWidget);
    });

    testWidgets('displays custom trigger icons', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              items: [
                ShadcnAccordionItem(
                  value: 'item1',
                  header: const Text('Header 1'),
                  content: const Text('Content 1'),
                  triggerIcon: const Icon(Icons.add, key: Key('custom-icon')),
                ),
              ],
            ),
          ),
        ),
      );

      // Custom trigger icon should be present
      expect(find.byKey(const Key('custom-icon')), findsOneWidget);
    });

    testWidgets('supports collapsible mode', (WidgetTester tester) async {
      String? currentExpanded = 'item1';

      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              collapsible: true,
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Content should be visible initially
      expect(find.text('Content 1'), findsOneWidget);

      // Tap to collapse the already expanded item
      await tester.tap(find.text('Header 1'));
      await tester.pump();

      // Update with collapsed state
      currentExpanded = null;
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              collapsible: true,
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Content should be hidden now
      expect(find.text('Content 1'), findsNothing);
    });

    testWidgets('applies custom theme properties', (WidgetTester tester) async {
      final customTheme = ShadcnAccordionTheme(
        headerBackground: Colors.red,
        contentBackground: Colors.green,
        borderColor: Colors.blue,
        borderWidth: 3.0,
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnAccordion(
              theme: customTheme,
              expandedValue: 'item1',
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find containers and verify colors
      final headerContainer = tester.widget<Container>(find.descendant(
        of: find.ancestor(
          of: find.text('Header 1'),
          matching: find.byType(Container),
        ),
        matching: find.byType(Container),
      ).last);

      expect((headerContainer.decoration as BoxDecoration?)?.color, Colors.red);
    });

    testWidgets('has proper semantics', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              semanticLabel: 'Test Accordion',
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                  semanticLabel: 'First Item',
                ),
              ],
            ),
          ),
        ),
      );

      final semanticsFinder = find.bySemanticsLabel('Test Accordion');
      expect(semanticsFinder, findsOneWidget);

      final itemSemanticsFinder = find.bySemanticsLabel('First Item');
      expect(itemSemanticsFinder, findsOneWidget);
    });

    testWidgets('animates expansion and collapse', (WidgetTester tester) async {
      String? currentExpanded;

      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      // Tap to expand
      await tester.tap(find.text('Header 1'));
      await tester.pump();

      // Update with expanded state
      currentExpanded = 'item1';
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme.copyWith(
            extensions: [accordionTheme],
          ),
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: currentExpanded,
              onExpandedValueChanged: (value) {
                currentExpanded = value;
              },
              items: [
                const ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Header 1'),
                  content: Text('Content 1'),
                ),
              ],
            ),
          ),
        ),
      );

      // Pump a few frames to see animation in progress
      await tester.pump(const Duration(milliseconds: 50));
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pump(const Duration(milliseconds: 150));

      // Animation should complete
      await tester.pumpAndSettle();

      // Content should be fully visible
      expect(find.text('Content 1'), findsOneWidget);
    });
  });
}