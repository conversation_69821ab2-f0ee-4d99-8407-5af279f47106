import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('Basic ShadcnAccordion Tests', () {
    testWidgets('creates accordion without errors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnAccordion(
              items: [
                ShadcnAccordionItem(
                  value: 'test',
                  header: Text('Test Header'),
                  content: Text('Test Content'),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(ShadcnAccordion), findsOneWidget);
      expect(find.text('Test Header'), findsOneWidget);
    });

    testWidgets('accordion item has required properties', (WidgetTester tester) async {
      const item = ShadcnAccordionItem(
        value: 'test-item',
        header: Text('Header'),
        content: Text('Content'),
      );

      expect(item.value, 'test-item');
      expect(item.header, isA<Text>());
      expect(item.content, isA<Text>());
      expect(item.disabled, false);
      expect(item.triggerIcon, isNull);
      expect(item.expandedTriggerIcon, isNull);
      expect(item.semanticLabel, isNull);
    });

    testWidgets('renders in minimal setup', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ShadcnAccordion(
            items: [
              ShadcnAccordionItem(
                value: '1',
                header: Text('Item 1'),
                content: Text('Content 1'),
              ),
            ],
          ),
        ),
      );

      expect(find.text('Item 1'), findsOneWidget);
    });
  });
}