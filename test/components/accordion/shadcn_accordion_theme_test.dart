import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnAccordionTheme Tests', () {
    late ColorScheme lightColorScheme;
    late ColorScheme darkColorScheme;

    setUp(() {
      lightColorScheme = ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.light);
      darkColorScheme = ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.dark);
    });

    group('defaultTheme', () {
      test('creates valid light theme', () {
        final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        
        expect(theme.headerBackground, lightColorScheme.surface);
        expect(theme.headerForeground, lightColorScheme.onSurface);
        expect(theme.contentBackground, lightColorScheme.surface);
        expect(theme.contentForeground, lightColorScheme.onSurface.withOpacity(0.8));
        expect(theme.borderColor, lightColorScheme.outline);
        expect(theme.triggerIconColor, lightColorScheme.onSurface.withOpacity(0.6));
        expect(theme.triggerIconHoverColor, lightColorScheme.onSurface);
        
        expect(theme.headerPadding, isNotNull);
        expect(theme.contentPadding, isNotNull);
        expect(theme.borderRadius, isNotNull);
        expect(theme.borderWidth, ShadcnTokens.borderWidth);
        expect(theme.triggerIconSize, ShadcnTokens.iconSizeMd);
        expect(theme.animationDuration, ShadcnTokens.durationNormal);
        expect(theme.animationCurve, Curves.easeInOut);
        expect(theme.elevation, ShadcnTokens.elevationNone);
      });

      test('creates valid dark theme', () {
        final theme = ShadcnAccordionTheme.defaultTheme(darkColorScheme);
        
        expect(theme.headerBackground, darkColorScheme.surface);
        expect(theme.headerForeground, darkColorScheme.onSurface);
        expect(theme.contentBackground, darkColorScheme.surface);
        expect(theme.contentForeground, darkColorScheme.onSurface.withOpacity(0.8));
        expect(theme.borderColor, darkColorScheme.outline);
        expect(theme.triggerIconColor, darkColorScheme.onSurface.withOpacity(0.6));
        
        // Dark theme should have different hover background opacity
        expect(theme.headerHoverBackground, 
               darkColorScheme.surfaceVariant.withOpacity(0.1));
      });

      test('has proper text styles', () {
        final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        
        expect(theme.headerTextStyle?.fontSize, ShadcnTokens.fontSizeMd);
        expect(theme.headerTextStyle?.fontWeight, ShadcnTokens.fontWeightMedium);
        expect(theme.headerTextStyle?.color, lightColorScheme.onSurface);
        expect(theme.headerTextStyle?.height, ShadcnTokens.lineHeightNormal);
        
        expect(theme.contentTextStyle?.fontSize, ShadcnTokens.fontSizeMd);
        expect(theme.contentTextStyle?.fontWeight, ShadcnTokens.fontWeightNormal);
        expect(theme.contentTextStyle?.color, lightColorScheme.onSurface.withOpacity(0.8));
        expect(theme.contentTextStyle?.height, ShadcnTokens.lineHeightNormal);
      });
    });

    group('lightTheme', () {
      test('creates optimized light theme', () {
        final theme = ShadcnAccordionTheme.lightTheme(lightColorScheme);
        
        expect(theme.headerBackground, lightColorScheme.surface);
        expect(theme.headerHoverBackground, 
               lightColorScheme.surfaceVariant.withOpacity(0.05));
        expect(theme.borderColor, lightColorScheme.outline.withOpacity(0.2));
      });
    });

    group('darkTheme', () {
      test('creates optimized dark theme', () {
        final theme = ShadcnAccordionTheme.darkTheme(darkColorScheme);
        
        expect(theme.headerBackground, darkColorScheme.surface);
        expect(theme.headerHoverBackground, 
               darkColorScheme.surfaceVariant.withOpacity(0.1));
        expect(theme.borderColor, darkColorScheme.outline.withOpacity(0.3));
      });
    });

    group('copyWith', () {
      test('copies with new values', () {
        final originalTheme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        final newTheme = originalTheme.copyWith(
          headerBackground: Colors.red,
          contentBackground: Colors.green,
          borderWidth: 3.0,
          triggerIconSize: 30.0,
          animationDuration: const Duration(milliseconds: 500),
        );
        
        expect(newTheme.headerBackground, Colors.red);
        expect(newTheme.contentBackground, Colors.green);
        expect(newTheme.borderWidth, 3.0);
        expect(newTheme.triggerIconSize, 30.0);
        expect(newTheme.animationDuration, const Duration(milliseconds: 500));
        
        // Other values should remain the same
        expect(newTheme.headerForeground, originalTheme.headerForeground);
        expect(newTheme.contentForeground, originalTheme.contentForeground);
        expect(newTheme.borderColor, originalTheme.borderColor);
      });

      test('preserves original values when not overridden', () {
        final originalTheme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        final newTheme = originalTheme.copyWith();
        
        expect(newTheme.headerBackground, originalTheme.headerBackground);
        expect(newTheme.headerForeground, originalTheme.headerForeground);
        expect(newTheme.contentBackground, originalTheme.contentBackground);
        expect(newTheme.contentForeground, originalTheme.contentForeground);
        expect(newTheme.borderColor, originalTheme.borderColor);
        expect(newTheme.triggerIconColor, originalTheme.triggerIconColor);
        expect(newTheme.borderWidth, originalTheme.borderWidth);
        expect(newTheme.triggerIconSize, originalTheme.triggerIconSize);
        expect(newTheme.animationDuration, originalTheme.animationDuration);
        expect(newTheme.animationCurve, originalTheme.animationCurve);
      });
    });

    group('lerp', () {
      test('interpolates colors correctly', () {
        final theme1 = ShadcnAccordionTheme(
          headerBackground: Colors.red,
          headerForeground: Colors.white,
          borderWidth: 1.0,
          triggerIconSize: 16.0,
        );
        
        final theme2 = ShadcnAccordionTheme(
          headerBackground: Colors.blue,
          headerForeground: Colors.black,
          borderWidth: 3.0,
          triggerIconSize: 24.0,
        );
        
        final lerpedTheme = theme1.lerp(theme2, 0.5);
        
        expect(lerpedTheme.headerBackground, 
               Color.lerp(Colors.red, Colors.blue, 0.5));
        expect(lerpedTheme.headerForeground, 
               Color.lerp(Colors.white, Colors.black, 0.5));
        expect(lerpedTheme.borderWidth, 2.0); // midpoint between 1.0 and 3.0
        expect(lerpedTheme.triggerIconSize, 20.0); // midpoint between 16.0 and 24.0
      });

      test('handles null other theme', () {
        final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        final lerpedTheme = theme.lerp(null, 0.5);
        
        expect(lerpedTheme, theme);
      });

      test('handles discrete values correctly', () {
        final theme1 = ShadcnAccordionTheme(
          animationDuration: const Duration(milliseconds: 200),
          animationCurve: Curves.linear,
        );
        
        final theme2 = ShadcnAccordionTheme(
          animationDuration: const Duration(milliseconds: 400),
          animationCurve: Curves.bounceIn,
        );
        
        // For t < 0.5, should use theme1 values
        final lerpedTheme1 = theme1.lerp(theme2, 0.3);
        expect(lerpedTheme1.animationDuration, const Duration(milliseconds: 200));
        expect(lerpedTheme1.animationCurve, Curves.linear);
        
        // For t >= 0.5, should use theme2 values
        final lerpedTheme2 = theme1.lerp(theme2, 0.7);
        expect(lerpedTheme2.animationDuration, const Duration(milliseconds: 400));
        expect(lerpedTheme2.animationCurve, Curves.bounceIn);
      });
    });

    group('validation', () {
      test('validates positive values', () {
        const theme = ShadcnAccordionTheme(
          borderWidth: 2.0,
          triggerIconSize: 20.0,
          elevation: 4.0,
          animationDuration: Duration(milliseconds: 300),
        );
        
        expect(theme.validate(), isTrue);
        expect(() => theme.validate(throwOnError: true), returnsNormally);
      });

      test('rejects negative border width', () {
        const theme = ShadcnAccordionTheme(borderWidth: -1.0);
        
        expect(theme.validate(), isFalse);
        expect(() => theme.validate(throwOnError: true), throwsException);
      });

      test('rejects non-positive trigger icon size', () {
        const theme = ShadcnAccordionTheme(triggerIconSize: 0.0);
        
        expect(theme.validate(), isFalse);
        expect(() => theme.validate(throwOnError: true), throwsException);
      });

      test('rejects negative elevation', () {
        const theme = ShadcnAccordionTheme(elevation: -1.0);
        
        expect(theme.validate(), isFalse);
        expect(() => theme.validate(throwOnError: true), throwsException);
      });

      test('rejects negative animation duration', () {
        const theme = ShadcnAccordionTheme(
          animationDuration: Duration(milliseconds: -100),
        );
        
        expect(theme.validate(), isFalse);
        expect(() => theme.validate(throwOnError: true), throwsException);
      });
    });

    group('equality and hashCode', () {
      test('equals with same properties', () {
        const theme1 = ShadcnAccordionTheme(
          headerBackground: Colors.red,
          borderWidth: 2.0,
          animationDuration: Duration(milliseconds: 300),
        );
        
        const theme2 = ShadcnAccordionTheme(
          headerBackground: Colors.red,
          borderWidth: 2.0,
          animationDuration: Duration(milliseconds: 300),
        );
        
        expect(theme1, equals(theme2));
        expect(theme1.hashCode, equals(theme2.hashCode));
      });

      test('not equals with different properties', () {
        const theme1 = ShadcnAccordionTheme(
          headerBackground: Colors.red,
          borderWidth: 2.0,
        );
        
        const theme2 = ShadcnAccordionTheme(
          headerBackground: Colors.blue,
          borderWidth: 2.0,
        );
        
        expect(theme1, isNot(equals(theme2)));
        expect(theme1.hashCode, isNot(equals(theme2.hashCode)));
      });

      test('identical objects are equal', () {
        const theme = ShadcnAccordionTheme(headerBackground: Colors.red);
        
        expect(theme, equals(theme));
        expect(theme.hashCode, equals(theme.hashCode));
      });
    });

    group('theme integration', () {
      testWidgets('integrates with Flutter theme system', (WidgetTester tester) async {
        final accordionTheme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        final theme = ThemeData(
          colorScheme: lightColorScheme,
          extensions: [accordionTheme],
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: theme,
            home: Builder(
              builder: (context) {
                final resolved = Theme.of(context).extension<ShadcnAccordionTheme>();
                expect(resolved, isNotNull);
                expect(resolved, accordionTheme);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('falls back to default when not in theme', (WidgetTester tester) async {
        final theme = ThemeData(colorScheme: lightColorScheme);

        await tester.pumpWidget(
          MaterialApp(
            theme: theme,
            home: Builder(
              builder: (context) {
                final resolved = Theme.of(context).extension<ShadcnAccordionTheme>();
                expect(resolved, isNull);
                
                // Component should create default theme
                final defaultTheme = ShadcnAccordionTheme.defaultTheme(
                  Theme.of(context).colorScheme,
                );
                expect(defaultTheme, isNotNull);
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('token consistency', () {
      test('uses consistent spacing tokens', () {
        final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        
        expect(theme.headerPadding, 
               const EdgeInsets.symmetric(
                 horizontal: ShadcnTokens.spacing4,
                 vertical: ShadcnTokens.spacing3,
               ));
        
        expect(theme.contentPadding, 
               const EdgeInsets.only(
                 left: ShadcnTokens.spacing4,
                 right: ShadcnTokens.spacing4,
                 bottom: ShadcnTokens.spacing4,
                 top: 0,
               ));
      });

      test('uses consistent border radius tokens', () {
        final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        
        expect(theme.borderRadius, 
               BorderRadius.circular(ShadcnTokens.radiusMd));
      });

      test('uses consistent sizing tokens', () {
        final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        
        expect(theme.borderWidth, ShadcnTokens.borderWidth);
        expect(theme.triggerIconSize, ShadcnTokens.iconSizeMd);
      });

      test('uses consistent animation tokens', () {
        final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
        
        expect(theme.animationDuration, ShadcnTokens.durationNormal);
        expect(theme.animationCurve, Curves.easeInOut);
      });
    });
  });
}