import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/components/shadcn_component.dart';

/// Simple mock component for testing the base class
class SimpleMockComponent extends ShadcnComponent {
  final String label;
  
  const SimpleMockComponent({super.key, required this.label});
  
  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    return Container(
      color: resolveColor(
        context,
        null,
        (theme) => theme.colorScheme.primary,
      ),
      child: Text(label),
    );
  }
}

/// Mock component with validation for testing
class MockValidationComponent with ShadcnComponentValidation {
  const MockValidationComponent();
}

void main() {
  group('ShadcnComponent Base Class Tests', () {
    test('ShadcnComponent can be extended', () {
      const component = SimpleMockComponent(label: 'Test');
      expect(component, isA<ShadcnComponent>());
      expect(component, isA<StatelessWidget>());
      expect(component.label, equals('Test'));
    });
    
    testWidgets('ShadcnComponent builds correctly with theme', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: SimpleMockComponent(label: 'Test Component'),
          ),
        ),
      );
      
      expect(find.text('Test Component'), findsOneWidget);
      expect(find.byType(SimpleMockComponent), findsOneWidget);
    });
    
    testWidgets('ShadcnComponent resolves colors correctly', (tester) async {
      final testTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
      );
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: SimpleMockComponent(label: 'Green Component'),
          ),
        ),
      );
      
      final container = tester.widget<Container>(find.byType(Container));
      expect(container.color, equals(testTheme.colorScheme.primary));
    });
  });
  
  group('ShadcnComponentValidation Mixin Tests', () {
    late MockValidationComponent validator;
    
    setUp(() {
      validator = const MockValidationComponent();
    });
    
    test('validation mixin can be applied', () {
      expect(validator, isA<ShadcnComponentValidation>());
    });
    
    test('validation methods are available', () {
      expect(validator.validateRequiredProperties, isA<Function>());
      expect(validator.debugThemeResolution, isA<Function>());
      expect(validator.validateThemeProperties, isA<Function>());
      expect(validator.validateVariant, isA<Function>());
      expect(validator.validateSize, isA<Function>());
      expect(validator.validateCallbacks, isA<Function>());
      expect(validator.debugComponentState, isA<Function>());
      expect(validator.validateAccessibility, isA<Function>());
    });
    
    testWidgets('validation works with real BuildContext', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Test that validation methods can be called
              validator.validateRequiredProperties({
                'testProp': 'value',
                'numberProp': 42,
              });
              
              validator.debugThemeResolution(context, 'TestComponent');
              validator.debugComponentState('TestComponent', {'test': 'value'});
              validator.validateThemeProperties(context);
              
              return const SizedBox();
            },
          ),
        ),
      );
      
      // Test passes if no exceptions are thrown
      expect(tester.takeException(), isNull);
    });
  });
  
  group('ShadcnComponentBuilder Tests', () {
    test('ShadcnComponentBuilder methods are available', () {
      expect(ShadcnComponentBuilder.styledContainer, isA<Function>());
      expect(ShadcnComponentBuilder.interactiveWidget, isA<Function>());
      expect(ShadcnComponentBuilder.focusableWidget, isA<Function>());
      expect(ShadcnComponentBuilder.animatedWidget, isA<Function>());
      expect(ShadcnComponentBuilder.stateAwareWidget, isA<Function>());
      expect(ShadcnComponentBuilder.loadingStateWidget, isA<Function>());
      expect(ShadcnComponentBuilder.errorStateWidget, isA<Function>());
      expect(ShadcnComponentBuilder.responsiveWidget, isA<Function>());
      expect(ShadcnComponentBuilder.shadcnSurface, isA<Function>());
    });
    
    testWidgets('styledContainer creates proper widget', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => ShadcnComponentBuilder.styledContainer(
              context: context,
              child: const Text('Styled'),
              backgroundColor: Colors.blue,
              width: 100,
              height: 50,
            ),
          ),
        ),
      );
      
      expect(find.text('Styled'), findsOneWidget);
      expect(find.byType(Container), findsOneWidget);
    });
    
    testWidgets('interactiveWidget handles taps', (tester) async {
      bool wasTapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => ShadcnComponentBuilder.interactiveWidget(
              context: context,
              child: const Text('Tap Me'),
              onTap: () => wasTapped = true,
            ),
          ),
        ),
      );
      
      await tester.tap(find.text('Tap Me'));
      expect(wasTapped, isTrue);
    });
    
    testWidgets('focusableWidget creates Focus widget', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => ShadcnComponentBuilder.focusableWidget(
              context: context,
              child: const Text('Focusable'),
            ),
          ),
        ),
      );
      
      expect(find.byType(Focus), findsOneWidget);
      expect(find.text('Focusable'), findsOneWidget);
    });
    
    testWidgets('loadingStateWidget shows loading indicator', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => ShadcnComponentBuilder.loadingStateWidget(
              context: context,
              child: const Text('Content'),
              isLoading: true,
              loadingText: 'Loading...',
            ),
          ),
        ),
      );
      
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading...'), findsOneWidget);
      expect(find.text('Content'), findsOneWidget);
    });
    
    testWidgets('errorStateWidget shows error message', (tester) async {
      bool retryPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => ShadcnComponentBuilder.errorStateWidget(
              context: context,
              child: const Text('Content'),
              hasError: true,
              errorMessage: 'Something went wrong',
              onRetry: () => retryPressed = true,
            ),
          ),
        ),
      );
      
      expect(find.text('Something went wrong'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      
      await tester.tap(find.text('Retry'));
      expect(retryPressed, isTrue);
    });
    
    testWidgets('shadcnSurface creates Material widget', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => ShadcnComponentBuilder.shadcnSurface(
              context: context,
              child: const Text('Surface Content'),
              elevation: 4,
            ),
          ),
        ),
      );
      
      expect(find.byType(Material), findsOneWidget);
      expect(find.text('Surface Content'), findsOneWidget);
      
      final material = tester.widget<Material>(find.byType(Material));
      expect(material.elevation, equals(4));
    });
  });
}