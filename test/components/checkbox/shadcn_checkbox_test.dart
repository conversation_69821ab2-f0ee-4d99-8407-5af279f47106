import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/components/checkbox/shadcn_checkbox.dart';
import 'package:shadcn/src/theme/extensions/shadcn_checkbox_theme.dart';

import '../../test_helpers.dart';

void main() {
  group('ShadcnCheckbox', () {
    testWidgets('displays correctly with basic properties', (tester) async {
      bool? checkboxValue = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: checkboxValue,
            onChanged: (value) => checkboxValue = value,
            label: 'Test Checkbox',
          ),
        ),
      );

      expect(find.text('Test Checkbox'), findsOneWidget);
      expect(find.byType(ShadcnCheckbox), findsOneWidget);
    });

    testWidgets('handles tap interactions correctly', (tester) async {
      bool? checkboxValue = false;
      bool callbackTriggered = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: checkboxValue,
            onChanged: (value) {
              checkboxValue = value;
              callbackTriggered = true;
            },
            label: 'Test Checkbox',
          ),
        ),
      );

      await tester.tap(find.byType(ShadcnCheckbox));
      await tester.pump();

      expect(callbackTriggered, isTrue);
    });

    testWidgets('supports tristate functionality', (tester) async {
      bool? checkboxValue = null;
      final List<bool?> valueChanges = [];

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: checkboxValue,
            tristate: true,
            onChanged: (value) {
              checkboxValue = value;
              valueChanges.add(value);
            },
            label: 'Tristate Checkbox',
          ),
        ),
      );

      // First tap: null -> false
      await tester.tap(find.byType(ShadcnCheckbox));
      await tester.pump();

      // Second tap: false -> true  
      await tester.tap(find.byType(ShadcnCheckbox));
      await tester.pump();

      // Third tap: true -> null
      await tester.tap(find.byType(ShadcnCheckbox));
      await tester.pump();

      expect(valueChanges, equals([false, true, null]));
    });

    testWidgets('displays helper text when provided', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: false,
            onChanged: (value) {},
            label: 'Test Checkbox',
            helperText: 'This is helper text',
          ),
        ),
      );

      expect(find.text('This is helper text'), findsOneWidget);
    });

    testWidgets('displays error text when provided', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: false,
            onChanged: (value) {},
            label: 'Test Checkbox',
            errorText: 'This is an error',
          ),
        ),
      );

      expect(find.text('This is an error'), findsOneWidget);
    });

    testWidgets('error text overrides helper text', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: false,
            onChanged: (value) {},
            label: 'Test Checkbox',
            helperText: 'Helper text',
            errorText: 'Error text',
          ),
        ),
      );

      expect(find.text('Error text'), findsOneWidget);
      expect(find.text('Helper text'), findsNothing);
    });

    testWidgets('respects enabled/disabled state', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: const ShadcnCheckbox(
            value: false,
            onChanged: null, // Disabled
            label: 'Disabled Checkbox',
          ),
        ),
      );

      await tester.tap(find.byType(ShadcnCheckbox));
      await tester.pump();

      // Should not trigger any changes when disabled
      expect(find.text('Disabled Checkbox'), findsOneWidget);
    });

    testWidgets('supports different sizes', (tester) async {
      for (final size in ShadcnCheckboxSize.values) {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnCheckbox(
              value: false,
              onChanged: (value) {},
              label: 'Size Test',
              size: size,
            ),
          ),
        );

        expect(find.byType(ShadcnCheckbox), findsOneWidget);
        await tester.pumpWidget(Container()); // Clear between tests
      }
    });

    testWidgets('supports custom child widget', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: false,
            onChanged: (value) {},
            child: const Row(
              children: [
                Icon(Icons.star),
                SizedBox(width: 8),
                Text('Custom Child'),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.star), findsOneWidget);
      expect(find.text('Custom Child'), findsOneWidget);
    });

    testWidgets('label click toggles checkbox when labelClickable is true', (tester) async {
      bool checkboxValue = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: checkboxValue,
            onChanged: (value) => checkboxValue = value ?? false,
            label: 'Clickable Label',
            labelClickable: true,
          ),
        ),
      );

      await tester.tap(find.text('Clickable Label'));
      await tester.pump();

      expect(checkboxValue, isTrue);
    });

    testWidgets('has correct semantics', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: true,
            onChanged: (value) {},
            label: 'Semantic Checkbox',
            semanticLabel: 'Custom semantic label',
          ),
        ),
      );

      final semantics = tester.getSemantics(find.byType(ShadcnCheckbox));
      expect(semantics.label, equals('Custom semantic label'));
      expect(semantics.hasFlag(SemanticsFlag.hasCheckedState), isTrue);
      expect(semantics.hasFlag(SemanticsFlag.isChecked), isTrue);
    });

    testWidgets('supports tooltip', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnCheckbox(
            value: false,
            onChanged: (value) {},
            label: 'Tooltip Checkbox',
            tooltip: 'This is a tooltip',
          ),
        ),
      );

      expect(find.byTooltip('This is a tooltip'), findsOneWidget);
    });

    group('Form Field Integration', () {
      testWidgets('works with form validation', (tester) async {
        final formKey = GlobalKey<FormState>();

        await tester.pumpWidget(
          createTestApp(
            child: Form(
              key: formKey,
              child: ShadcnCheckbox.formField(
                initialValue: false,
                onChanged: (value) {},
                validator: (value) {
                  if (value != true) {
                    return 'You must accept the terms';
                  }
                  return null;
                },
                label: 'Accept Terms',
              ),
            ),
          ),
        );

        // Trigger validation
        expect(formKey.currentState!.validate(), isFalse);
        await tester.pump();

        expect(find.text('You must accept the terms'), findsOneWidget);
      });

      testWidgets('form field updates correctly', (tester) async {
        final formKey = GlobalKey<FormState>();
        bool? formValue;

        await tester.pumpWidget(
          createTestApp(
            child: Form(
              key: formKey,
              onChanged: () {
                formKey.currentState!.save();
              },
              child: ShadcnCheckbox.formField(
                initialValue: false,
                onChanged: (value) => formValue = value,
                onSaved: (value) => formValue = value,
                label: 'Form Checkbox',
              ),
            ),
          ),
        );

        await tester.tap(find.byType(ShadcnCheckbox));
        await tester.pump();

        formKey.currentState!.save();
        expect(formValue, isTrue);
      });
    });

    group('Theme Integration', () {
      testWidgets('applies custom theme correctly', (tester) async {
        const customTheme = ShadcnCheckboxTheme(
          checkedBackground: Colors.red,
          checkedForeground: Colors.white,
          size: 24.0,
        );

        await tester.pumpWidget(
          createTestApp(
            theme: ThemeData(
              extensions: const [customTheme],
            ),
            child: ShadcnCheckbox(
              value: true,
              onChanged: (value) {},
              label: 'Custom Theme',
            ),
          ),
        );

        expect(find.byType(ShadcnCheckbox), findsOneWidget);
      });

      testWidgets('falls back to default theme when no custom theme provided', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnCheckbox(
              value: true,
              onChanged: (value) {},
              label: 'Default Theme',
            ),
          ),
        );

        expect(find.byType(ShadcnCheckbox), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('supports focus', (tester) async {
        final focusNode = FocusNode();

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnCheckbox(
              value: false,
              onChanged: (value) {},
              label: 'Focusable Checkbox',
              focusNode: focusNode,
            ),
          ),
        );

        focusNode.requestFocus();
        await tester.pump();

        expect(focusNode.hasFocus, isTrue);
      });

      testWidgets('can be excluded from semantics', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnCheckbox(
              value: false,
              onChanged: (value) {},
              label: 'Hidden from semantics',
              excludeFromSemantics: true,
            ),
          ),
        );

        expect(
          tester.getSemantics(find.byType(ShadcnCheckbox), skipOffstage: false),
          isA<SemanticsNode>(),
        );
      });
    });

    group('Haptic Feedback', () {
      testWidgets('triggers haptic feedback when enabled', (tester) async {
        final List<MethodCall> log = <MethodCall>[];
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, (methodCall) async {
          log.add(methodCall);
          return null;
        });

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnCheckbox(
              value: false,
              onChanged: (value) {},
              label: 'Haptic Checkbox',
              enableFeedback: true,
            ),
          ),
        );

        await tester.tap(find.byType(ShadcnCheckbox));
        await tester.pump();

        expect(
          log,
          contains(isMethodCall('HapticFeedback.vibrate', arguments: 'HapticFeedbackType.lightImpact')),
        );

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, null);
      });

      testWidgets('does not trigger haptic feedback when disabled', (tester) async {
        final List<MethodCall> log = <MethodCall>[];
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, (methodCall) async {
          log.add(methodCall);
          return null;
        });

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnCheckbox(
              value: false,
              onChanged: (value) {},
              label: 'No Haptic Checkbox',
              enableFeedback: false,
            ),
          ),
        );

        await tester.tap(find.byType(ShadcnCheckbox));
        await tester.pump();

        expect(
          log,
          isNot(contains(isMethodCall('HapticFeedback.vibrate', arguments: 'HapticFeedbackType.lightImpact'))),
        );

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, null);
      });
    });

    group('Custom Icons', () {
      testWidgets('supports custom check icon', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnCheckbox(
              value: true,
              onChanged: (value) {},
              label: 'Custom Check Icon',
              checkIcon: const Icon(Icons.star, key: Key('custom-check')),
            ),
          ),
        );

        expect(find.byKey(const Key('custom-check')), findsOneWidget);
      });

      testWidgets('supports custom indeterminate icon', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnCheckbox(
              value: null,
              tristate: true,
              onChanged: (value) {},
              label: 'Custom Indeterminate Icon',
              indeterminateIcon: const Icon(Icons.remove, key: Key('custom-indeterminate')),
            ),
          ),
        );

        expect(find.byKey(const Key('custom-indeterminate')), findsOneWidget);
      });
    });
  });
}