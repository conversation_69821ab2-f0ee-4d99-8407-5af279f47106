import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/theme/extensions/shadcn_checkbox_theme.dart';

void main() {
  group('ShadcnCheckboxTheme', () {
    group('defaultTheme', () {
      testWidgets('creates theme with light color scheme', (tester) async {
        const lightColorScheme = ColorScheme.light();
        final theme = ShadcnCheckboxTheme.defaultTheme(lightColorScheme);

        expect(theme.checkedBackground, equals(lightColorScheme.primary));
        expect(theme.checkedForeground, equals(lightColorScheme.onPrimary));
        expect(theme.uncheckedBackground, equals(Colors.transparent));
        expect(theme.size, equals(20.0));
      });

      testWidgets('creates theme with dark color scheme', (tester) async {
        const darkColorScheme = ColorScheme.dark();
        final theme = ShadcnCheckboxTheme.defaultTheme(darkColorScheme);

        expect(theme.checkedBackground, equals(darkColorScheme.primary));
        expect(theme.checkedForeground, equals(darkColorScheme.onPrimary));
        expect(theme.uncheckedBackground, equals(Colors.transparent));
        expect(theme.size, equals(20.0));
      });

      testWidgets('has proper default values', (tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnCheckboxTheme.defaultTheme(colorScheme);

        expect(theme.size, equals(20.0));
        expect(theme.smallSize, equals(16.0));
        expect(theme.largeSize, equals(24.0));
        expect(theme.borderWidth, equals(1.0));
        expect(theme.animationDuration, equals(const Duration(milliseconds: 150)));
      });
    });

    group('copyWith', () {
      test('creates new instance with updated values', () {
        const original = ShadcnCheckboxTheme(
          checkedBackground: Colors.red,
          size: 20.0,
        );

        final updated = original.copyWith(
          checkedBackground: Colors.blue,
          checkedForeground: Colors.white,
        );

        expect(updated.checkedBackground, equals(Colors.blue));
        expect(updated.checkedForeground, equals(Colors.white));
        expect(updated.size, equals(20.0)); // Unchanged
      });

      test('keeps original values when null provided', () {
        const original = ShadcnCheckboxTheme(
          checkedBackground: Colors.red,
          checkedForeground: Colors.white,
          size: 20.0,
        );

        final updated = original.copyWith();

        expect(updated.checkedBackground, equals(Colors.red));
        expect(updated.checkedForeground, equals(Colors.white));
        expect(updated.size, equals(20.0));
      });
    });

    group('lerp', () {
      test('interpolates between two themes correctly', () {
        const themeA = ShadcnCheckboxTheme(
          checkedBackground: Colors.red,
          size: 16.0,
        );

        const themeB = ShadcnCheckboxTheme(
          checkedBackground: Colors.blue,
          size: 24.0,
        );

        final lerped = themeA.lerp(themeB, 0.5);

        expect(lerped.size, equals(20.0)); // Midpoint between 16 and 24
        // Color lerping is more complex, but we can verify it's not the original colors
        expect(lerped.checkedBackground, isNot(equals(Colors.red)));
        expect(lerped.checkedBackground, isNot(equals(Colors.blue)));
      });

      test('returns original theme when other is null', () {
        const theme = ShadcnCheckboxTheme(
          checkedBackground: Colors.red,
          size: 20.0,
        );

        final lerped = theme.lerp(null, 0.5);

        expect(lerped.checkedBackground, equals(Colors.red));
        expect(lerped.size, equals(20.0));
      });

      test('handles t = 0.0 correctly', () {
        const themeA = ShadcnCheckboxTheme(
          checkedBackground: Colors.red,
          size: 16.0,
        );

        const themeB = ShadcnCheckboxTheme(
          checkedBackground: Colors.blue,
          size: 24.0,
        );

        final lerped = themeA.lerp(themeB, 0.0);

        expect(lerped.checkedBackground, equals(Colors.red));
        expect(lerped.size, equals(16.0));
      });

      test('handles t = 1.0 correctly', () {
        const themeA = ShadcnCheckboxTheme(
          checkedBackground: Colors.red,
          size: 16.0,
        );

        const themeB = ShadcnCheckboxTheme(
          checkedBackground: Colors.blue,
          size: 24.0,
        );

        final lerped = themeA.lerp(themeB, 1.0);

        expect(lerped.checkedBackground, equals(Colors.blue));
        expect(lerped.size, equals(24.0));
      });
    });

    group('validate', () {
      test('returns true for valid theme', () {
        const theme = ShadcnCheckboxTheme(
          checkedBackground: Colors.blue,
          checkedForeground: Colors.white,
          size: 20.0,
          borderWidth: 1.0,
        );

        expect(theme.validate(), isTrue);
      });

      test('validates size constraints', () {
        const invalidTheme = ShadcnCheckboxTheme(
          size: -5.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('validates border width constraints', () {
        const invalidTheme = ShadcnCheckboxTheme(
          borderWidth: -1.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('throws exception when throwOnError is true', () {
        const invalidTheme = ShadcnCheckboxTheme(
          size: -5.0,
        );

        expect(() => invalidTheme.validate(throwOnError: true), throwsA(isA<ThemeException>()));
      });
    });

    group('resolveSizeForVariant', () {
      test('returns correct size for each variant', () {
        const theme = ShadcnCheckboxTheme(
          smallSize: 16.0,
          size: 20.0,
          largeSize: 24.0,
        );

        expect(theme.resolveSizeForVariant(ShadcnCheckboxSize.small), equals(16.0));
        expect(theme.resolveSizeForVariant(ShadcnCheckboxSize.medium), equals(20.0));
        expect(theme.resolveSizeForVariant(ShadcnCheckboxSize.large), equals(24.0));
      });

      test('falls back to defaults when sizes not specified', () {
        const theme = ShadcnCheckboxTheme();

        expect(theme.resolveSizeForVariant(ShadcnCheckboxSize.small), equals(16.0));
        expect(theme.resolveSizeForVariant(ShadcnCheckboxSize.medium), equals(20.0));
        expect(theme.resolveSizeForVariant(ShadcnCheckboxSize.large), equals(24.0));
      });
    });

    group('theme extension behavior', () {
      testWidgets('integrates with Material theme system', (tester) async {
        const customTheme = ShadcnCheckboxTheme(
          checkedBackground: Colors.purple,
          size: 32.0,
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              extensions: const [customTheme],
            ),
            home: Builder(
              builder: (context) {
                final resolvedTheme = Theme.of(context).extension<ShadcnCheckboxTheme>();
                expect(resolvedTheme?.checkedBackground, equals(Colors.purple));
                expect(resolvedTheme?.size, equals(32.0));
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('edge cases', () {
      test('handles null values in lerp correctly', () {
        const theme = ShadcnCheckboxTheme(
          checkedBackground: Colors.red,
        );

        final lerped = theme.lerp(theme, 0.5);
        expect(lerped.checkedBackground, equals(Colors.red));
      });

      test('copyWith handles all nullable properties', () {
        const theme = ShadcnCheckboxTheme();
        
        final updated = theme.copyWith(
          checkedBackground: Colors.red,
          checkedForeground: Colors.white,
          checkedBorder: Colors.blue,
          uncheckedBackground: Colors.transparent,
          uncheckedForeground: Colors.grey,
          uncheckedBorder: Colors.black,
          intermediateBackground: Colors.orange,
          intermediateForeground: Colors.yellow,
          intermediateBorder: Colors.green,
          hoverOverlay: Colors.purple,
          pressedOverlay: Colors.pink,
          focusedOverlay: Colors.teal,
          size: 25.0,
          smallSize: 15.0,
          largeSize: 35.0,
          borderWidth: 2.0,
          animationDuration: const Duration(milliseconds: 200),
        );

        expect(updated.checkedBackground, equals(Colors.red));
        expect(updated.checkedForeground, equals(Colors.white));
        expect(updated.size, equals(25.0));
        expect(updated.borderWidth, equals(2.0));
      });
    });
  });
}