import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../../lib/src/theme/extensions/shadcn_navigation_menu_theme.dart';
import '../../../lib/src/constants/shadcn_tokens.dart';

void main() {
  group('ShadcnNavigationMenuTheme', () {
    testWidgets('creates with default values', (tester) async {
      const theme = ShadcnNavigationMenuTheme();
      
      expect(theme.backgroundColor, isNull);
      expect(theme.surfaceColor, isNull);
      expect(theme.itemColor, isNull);
      expect(theme.itemActiveColor, isNull);
      expect(theme.minHeight, isNull);
      expect(theme.dropdownBackgroundColor, isNull);
    });

    testWidgets('creates with all properties set', (tester) async {
      const theme = ShadcnNavigationMenuTheme(
        backgroundColor: Colors.white,
        surfaceColor: Colors.grey,
        containerPadding: EdgeInsets.all(8.0),
        borderRadius: BorderRadius.all(Radius.circular(8.0)),
        minHeight: 40.0,
        border: Border.fromBorderSide(BorderSide(color: Colors.black)),
        shadows: [BoxShadow(color: Colors.black, blurRadius: 4.0)],
        itemColor: Colors.red,
        itemActiveColor: Colors.blue,
        itemHoverColor: Colors.orange,
        itemFocusColor: Colors.purple,
        itemPressedColor: Colors.pink,
        itemDisabledColor: Colors.grey,
        itemBackgroundColor: Colors.transparent,
        itemActiveBackgroundColor: Colors.lightBlue,
        itemHoverBackgroundColor: Colors.lightGrey,
        itemFocusBackgroundColor: Colors.lightGreen,
        itemPressedBackgroundColor: Colors.lightCyan,
        itemDisabledBackgroundColor: Colors.transparent,
        itemPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        itemHeight: 36.0,
        itemSpacing: 4.0,
        itemBorderRadius: BorderRadius.circular(6.0),
        itemMinWidth: 64.0,
        itemTextStyle: TextStyle(fontSize: 14.0),
        activeItemTextStyle: TextStyle(fontSize: 14.0, fontWeight: FontWeight.bold),
        itemFontWeight: FontWeight.normal,
        itemFontSize: 14.0,
        iconSize: 16.0,
        iconSpacing: 8.0,
        iconColor: Colors.black,
        activeIconColor: Colors.blue,
        dropdownBackgroundColor: Colors.white,
        dropdownSurfaceColor: Colors.grey,
        dropdownPadding: EdgeInsets.all(4.0),
        dropdownBorderRadius: BorderRadius.circular(8.0),
        dropdownElevation: 4.0,
        dropdownShadows: [BoxShadow(color: Colors.black, blurRadius: 2.0)],
        dropdownBorder: Border.all(color: Colors.black),
        dropdownMinWidth: 200.0,
        dropdownMaxWidth: 400.0,
        dropdownOffset: 4.0,
        dropdownItemPadding: EdgeInsets.all(8.0),
        dropdownItemHeight: 32.0,
        dropdownItemSpacing: 2.0,
        dropdownItemBorderRadius: BorderRadius.circular(4.0),
        dividerColor: Colors.grey,
        dividerThickness: 1.0,
        dividerHeight: 20.0,
        dividerMargin: EdgeInsets.symmetric(vertical: 4.0),
        animationDuration: Duration(milliseconds: 250),
        animationCurve: Curves.easeInOut,
        hoverAnimationDuration: Duration(milliseconds: 150),
        dropdownAnimationDuration: Duration(milliseconds: 200),
        focusColor: Colors.blue,
        focusWidth: 2.0,
        focusOffset: 1.0,
        indicatorColor: Colors.blue,
        indicatorWidth: 24.0,
        indicatorHeight: 2.0,
        indicatorBorderRadius: BorderRadius.circular(1.0),
      );
      
      expect(theme.backgroundColor, equals(Colors.white));
      expect(theme.surfaceColor, equals(Colors.grey));
      expect(theme.containerPadding, equals(const EdgeInsets.all(8.0)));
      expect(theme.borderRadius, equals(const BorderRadius.all(Radius.circular(8.0))));
      expect(theme.minHeight, equals(40.0));
      expect(theme.border, isA<Border>());
      expect(theme.shadows, hasLength(1));
      expect(theme.itemColor, equals(Colors.red));
      expect(theme.itemActiveColor, equals(Colors.blue));
      expect(theme.itemHoverColor, equals(Colors.orange));
      expect(theme.itemFocusColor, equals(Colors.purple));
      expect(theme.itemPressedColor, equals(Colors.pink));
      expect(theme.itemDisabledColor, equals(Colors.grey));
      expect(theme.itemBackgroundColor, equals(Colors.transparent));
      expect(theme.itemActiveBackgroundColor, equals(Colors.lightBlue));
      expect(theme.itemHoverBackgroundColor, equals(Colors.lightGrey));
      expect(theme.itemFocusBackgroundColor, equals(Colors.lightGreen));
      expect(theme.itemPressedBackgroundColor, equals(Colors.lightCyan));
      expect(theme.itemDisabledBackgroundColor, equals(Colors.transparent));
      expect(theme.itemPadding, equals(const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0)));
      expect(theme.itemHeight, equals(36.0));
      expect(theme.itemSpacing, equals(4.0));
      expect(theme.itemBorderRadius, equals(BorderRadius.circular(6.0)));
      expect(theme.itemMinWidth, equals(64.0));
      expect(theme.itemTextStyle, equals(const TextStyle(fontSize: 14.0)));
      expect(theme.activeItemTextStyle, equals(const TextStyle(fontSize: 14.0, fontWeight: FontWeight.bold)));
      expect(theme.itemFontWeight, equals(FontWeight.normal));
      expect(theme.itemFontSize, equals(14.0));
      expect(theme.iconSize, equals(16.0));
      expect(theme.iconSpacing, equals(8.0));
      expect(theme.iconColor, equals(Colors.black));
      expect(theme.activeIconColor, equals(Colors.blue));
      expect(theme.dropdownBackgroundColor, equals(Colors.white));
      expect(theme.dropdownSurfaceColor, equals(Colors.grey));
      expect(theme.dropdownPadding, equals(const EdgeInsets.all(4.0)));
      expect(theme.dropdownBorderRadius, equals(BorderRadius.circular(8.0)));
      expect(theme.dropdownElevation, equals(4.0));
      expect(theme.dropdownShadows, hasLength(1));
      expect(theme.dropdownBorder, isA<Border>());
      expect(theme.dropdownMinWidth, equals(200.0));
      expect(theme.dropdownMaxWidth, equals(400.0));
      expect(theme.dropdownOffset, equals(4.0));
      expect(theme.dropdownItemPadding, equals(const EdgeInsets.all(8.0)));
      expect(theme.dropdownItemHeight, equals(32.0));
      expect(theme.dropdownItemSpacing, equals(2.0));
      expect(theme.dropdownItemBorderRadius, equals(BorderRadius.circular(4.0)));
      expect(theme.dividerColor, equals(Colors.grey));
      expect(theme.dividerThickness, equals(1.0));
      expect(theme.dividerHeight, equals(20.0));
      expect(theme.dividerMargin, equals(const EdgeInsets.symmetric(vertical: 4.0)));
      expect(theme.animationDuration, equals(const Duration(milliseconds: 250)));
      expect(theme.animationCurve, equals(Curves.easeInOut));
      expect(theme.hoverAnimationDuration, equals(const Duration(milliseconds: 150)));
      expect(theme.dropdownAnimationDuration, equals(const Duration(milliseconds: 200)));
      expect(theme.focusColor, equals(Colors.blue));
      expect(theme.focusWidth, equals(2.0));
      expect(theme.focusOffset, equals(1.0));
      expect(theme.indicatorColor, equals(Colors.blue));
      expect(theme.indicatorWidth, equals(24.0));
      expect(theme.indicatorHeight, equals(2.0));
      expect(theme.indicatorBorderRadius, equals(BorderRadius.circular(1.0)));
    });

    group('defaultTheme', () {
      testWidgets('creates proper default theme for light mode', (tester) async {
        final colorScheme = ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        );
        final theme = ShadcnNavigationMenuTheme.defaultTheme(colorScheme);
        
        expect(theme.backgroundColor, equals(Colors.transparent));
        expect(theme.surfaceColor, isNotNull);
        expect(theme.itemColor, isNotNull);
        expect(theme.itemActiveColor, isNotNull);
        expect(theme.minHeight, equals(40.0)); // ShadcnTokens.buttonHeightMd
        expect(theme.dropdownBackgroundColor, isNotNull);
        expect(theme.dropdownElevation, equals(4.0)); // ShadcnTokens.elevationMd
      });

      testWidgets('creates proper default theme for dark mode', (tester) async {
        final colorScheme = ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        );
        final theme = ShadcnNavigationMenuTheme.defaultTheme(colorScheme);
        
        expect(theme.itemColor, isNotNull);
        expect(theme.itemActiveColor, isNotNull);
        expect(theme.dropdownBackgroundColor, isNotNull);
      });

      testWidgets('sets proper text styles', (tester) async {
        final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
        final theme = ShadcnNavigationMenuTheme.defaultTheme(colorScheme);
        
        expect(theme.itemTextStyle, isNotNull);
        expect(theme.activeItemTextStyle, isNotNull);
        expect(theme.itemTextStyle!.fontSize, equals(14.0)); // ShadcnTokens.fontSizeMd
        expect(theme.activeItemTextStyle!.fontWeight, equals(FontWeight.w600)); // ShadcnTokens.fontWeightSemibold
      });

      testWidgets('sets proper dropdown properties', (tester) async {
        final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
        final theme = ShadcnNavigationMenuTheme.defaultTheme(colorScheme);
        
        expect(theme.dropdownMinWidth, equals(200.0));
        expect(theme.dropdownMaxWidth, equals(400.0));
        expect(theme.dropdownOffset, equals(4.0));
        expect(theme.dropdownBorderRadius, isNotNull);
        expect(theme.dropdownShadows, isNotNull);
        expect(theme.dropdownShadows!.length, equals(1));
        expect(theme.dropdownBorder, isNotNull);
      });

      testWidgets('sets proper animation durations', (tester) async {
        final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
        final theme = ShadcnNavigationMenuTheme.defaultTheme(colorScheme);
        
        expect(theme.animationDuration, equals(const Duration(milliseconds: 250))); // ShadcnTokens.durationNormal
        expect(theme.hoverAnimationDuration, equals(const Duration(milliseconds: 150))); // ShadcnTokens.durationFast
        expect(theme.dropdownAnimationDuration, equals(const Duration(milliseconds: 250))); // ShadcnTokens.durationNormal
      });
    });

    group('copyWith', () {
      testWidgets('copies with new values', (tester) async {
        final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
        final original = ShadcnNavigationMenuTheme.defaultTheme(colorScheme);
        
        final copied = original.copyWith(
          itemColor: Colors.red,
          itemActiveColor: Colors.blue,
          minHeight: 50.0,
          backgroundColor: Colors.yellow,
          dropdownElevation: 8.0,
        );
        
        expect(copied.itemColor, equals(Colors.red));
        expect(copied.itemActiveColor, equals(Colors.blue));
        expect(copied.minHeight, equals(50.0));
        expect(copied.backgroundColor, equals(Colors.yellow));
        expect(copied.dropdownElevation, equals(8.0));
        
        // Other values should remain the same
        expect(copied.itemTextStyle, equals(original.itemTextStyle));
        expect(copied.dropdownBackgroundColor, equals(original.dropdownBackgroundColor));
      });

      testWidgets('preserves original values when null passed', (tester) async {
        const original = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          minHeight: 40.0,
          dropdownElevation: 4.0,
        );
        
        final copied = original.copyWith(
          itemActiveColor: Colors.blue,
          // itemColor, minHeight, and dropdownElevation not specified, should be preserved
        );
        
        expect(copied.itemColor, equals(Colors.red));
        expect(copied.minHeight, equals(40.0));
        expect(copied.dropdownElevation, equals(4.0));
        expect(copied.itemActiveColor, equals(Colors.blue));
      });
    });

    group('lerp', () {
      testWidgets('interpolates colors correctly', (tester) async {
        const theme1 = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          itemActiveColor: Colors.blue,
          dropdownBackgroundColor: Colors.white,
        );
        const theme2 = ShadcnNavigationMenuTheme(
          itemColor: Colors.green,
          itemActiveColor: Colors.purple,
          dropdownBackgroundColor: Colors.black,
        );
        
        final lerped = theme1.lerp(theme2, 0.5);
        
        // Colors should be interpolated
        expect(lerped.itemColor, isNotNull);
        expect(lerped.itemActiveColor, isNotNull);
        expect(lerped.dropdownBackgroundColor, isNotNull);
        expect(lerped.itemColor, isNot(equals(Colors.red)));
        expect(lerped.itemColor, isNot(equals(Colors.green)));
      });

      testWidgets('uses threshold logic for non-color properties', (tester) async {
        const theme1 = ShadcnNavigationMenuTheme(
          minHeight: 30.0,
          itemHeight: 32.0,
          dropdownElevation: 2.0,
        );
        const theme2 = ShadcnNavigationMenuTheme(
          minHeight: 50.0,
          itemHeight: 40.0,
          dropdownElevation: 8.0,
        );
        
        final lerped1 = theme1.lerp(theme2, 0.3); // t < 0.5
        final lerped2 = theme1.lerp(theme2, 0.7); // t >= 0.5
        
        expect(lerped1.minHeight, equals(30.0)); // First theme values
        expect(lerped1.itemHeight, equals(32.0));
        expect(lerped1.dropdownElevation, equals(2.0));
        
        expect(lerped2.minHeight, equals(50.0)); // Second theme values
        expect(lerped2.itemHeight, equals(40.0));
        expect(lerped2.dropdownElevation, equals(8.0));
      });

      testWidgets('handles EdgeInsets lerp correctly', (tester) async {
        const theme1 = ShadcnNavigationMenuTheme(
          containerPadding: EdgeInsets.all(8.0),
          itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
          dropdownPadding: EdgeInsets.all(2.0),
        );
        const theme2 = ShadcnNavigationMenuTheme(
          containerPadding: EdgeInsets.all(16.0),
          itemPadding: EdgeInsets.symmetric(horizontal: 8.0),
          dropdownPadding: EdgeInsets.all(6.0),
        );
        
        final lerped = theme1.lerp(theme2, 0.5);
        
        expect(lerped.containerPadding, isNotNull);
        expect(lerped.itemPadding, isNotNull);
        expect(lerped.dropdownPadding, isNotNull);
        // EdgeInsets.lerp should be used
        expect(lerped.containerPadding, equals(const EdgeInsets.all(12.0)));
        expect(lerped.itemPadding, equals(const EdgeInsets.symmetric(horizontal: 6.0)));
        expect(lerped.dropdownPadding, equals(const EdgeInsets.all(4.0)));
      });

      testWidgets('returns original theme when other is not ShadcnNavigationMenuTheme', (tester) async {
        const theme = ShadcnNavigationMenuTheme(itemColor: Colors.red);
        
        final result = theme.lerp(null, 0.5);
        
        expect(result, equals(theme));
      });
    });

    group('validate', () {
      testWidgets('validates successfully with required properties', (tester) async {
        const validTheme = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          itemActiveColor: Colors.blue,
          dropdownBackgroundColor: Colors.white,
          minHeight: 30.0,
          itemHeight: 32.0,
          dropdownElevation: 4.0,
        );
        
        expect(validTheme.validate(), isTrue);
      });

      testWidgets('fails validation with missing required colors', (tester) async {
        const invalidTheme = ShadcnNavigationMenuTheme(
          // Missing itemColor, itemActiveColor, dropdownBackgroundColor
          minHeight: 30.0,
        );
        
        expect(invalidTheme.validate(), isFalse);
      });

      testWidgets('fails validation with negative size values', (tester) async {
        const invalidTheme = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          itemActiveColor: Colors.blue,
          dropdownBackgroundColor: Colors.white,
          minHeight: -10.0, // Invalid negative height
        );
        
        expect(invalidTheme.validate(), isFalse);
      });

      testWidgets('allows zero elevation', (tester) async {
        const validTheme = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          itemActiveColor: Colors.blue,
          dropdownBackgroundColor: Colors.white,
          dropdownElevation: 0.0, // Zero is valid
        );
        
        expect(validTheme.validate(), isTrue);
      });

      testWidgets('throws error when throwOnError is true', (tester) async {
        const invalidTheme = ShadcnNavigationMenuTheme(
          minHeight: -10.0,
        );
        
        expect(
          () => invalidTheme.validate(throwOnError: true),
          throwsA(isA<FlutterError>()),
        );
      });
    });

    group('equality and hashCode', () {
      testWidgets('equal themes have same hashCode', (tester) async {
        const theme1 = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          itemActiveColor: Colors.blue,
          minHeight: 30.0,
          dropdownBackgroundColor: Colors.white,
          dropdownElevation: 4.0,
        );
        const theme2 = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          itemActiveColor: Colors.blue,
          minHeight: 30.0,
          dropdownBackgroundColor: Colors.white,
          dropdownElevation: 4.0,
        );
        
        expect(theme1, equals(theme2));
        expect(theme1.hashCode, equals(theme2.hashCode));
      });

      testWidgets('different themes are not equal', (tester) async {
        const theme1 = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          minHeight: 30.0,
        );
        const theme2 = ShadcnNavigationMenuTheme(
          itemColor: Colors.blue, // Different color
          minHeight: 30.0,
        );
        
        expect(theme1, isNot(equals(theme2)));
      });

      testWidgets('identical objects are equal', (tester) async {
        const theme = ShadcnNavigationMenuTheme(itemColor: Colors.red);
        
        expect(theme, equals(theme));
      });

      testWidgets('different types are not equal', (tester) async {
        const theme = ShadcnNavigationMenuTheme(itemColor: Colors.red);
        
        expect(theme, isNot(equals('not a theme')));
      });
    });

    group('toString', () {
      testWidgets('contains key information', (tester) async {
        const theme = ShadcnNavigationMenuTheme(
          itemColor: Colors.red,
          itemActiveColor: Colors.blue,
          dropdownBackgroundColor: Colors.white,
          minHeight: 40.0,
        );
        
        final string = theme.toString();
        
        expect(string, contains('ShadcnNavigationMenuTheme'));
        expect(string, contains('40.0'));
      });
    });
  });
}