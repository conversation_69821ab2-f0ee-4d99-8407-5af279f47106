import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../../lib/src/components/navigation_menu/shadcn_navigation_menu.dart';
import '../../../lib/src/theme/extensions/shadcn_navigation_menu_theme.dart';

void main() {
  group('ShadcnNavigationMenu', () {
    late ThemeData testTheme;
    
    setUp(() {
      testTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        extensions: [
          ShadcnNavigationMenuTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue),
          ),
        ],
      );
    });

    testWidgets('renders empty navigation menu when items list is empty', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(items: []),
          ),
        ),
      );

      expect(find.byType(ShadcnNavigationMenu), findsOneWidget);
      // Should render as empty widget
      expect(find.text('Home'), findsNothing);
      expect(find.text('About'), findsNothing);
    });

    testWidgets('renders single navigation item correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(text: 'Home'),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Home'), findsOneWidget);
    });

    testWidgets('renders multiple navigation items horizontally by default', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(text: 'Home'),
                ShadcnNavigationMenuItem(text: 'About'),
                ShadcnNavigationMenuItem(text: 'Contact'),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Home'), findsOneWidget);
      expect(find.text('About'), findsOneWidget);
      expect(find.text('Contact'), findsOneWidget);
      
      // Should be in a Row
      expect(find.byType(Row), findsOneWidget);
    });

    testWidgets('renders navigation items vertically when orientation is vertical', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              orientation: Axis.vertical,
              items: [
                ShadcnNavigationMenuItem(text: 'Home'),
                ShadcnNavigationMenuItem(text: 'About'),
              ],
            ),
          ),
        ),
      );

      // Should be in a Column
      expect(find.byType(Column), findsOneWidget);
    });

    testWidgets('handles tap callbacks correctly', (tester) async {
      var homeTapped = false;
      var aboutTapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Home',
                  onTap: () => homeTapped = true,
                ),
                ShadcnNavigationMenuItem(
                  text: 'About',
                  onTap: () => aboutTapped = true,
                ),
              ],
            ),
          ),
        ),
      );

      // Tap on Home
      await tester.tap(find.text('Home'));
      await tester.pumpAndSettle();
      expect(homeTapped, isTrue);
      
      // Tap on About
      await tester.tap(find.text('About'));
      await tester.pumpAndSettle();
      expect(aboutTapped, isTrue);
    });

    testWidgets('displays active item with different styling', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(text: 'Home'),
                ShadcnNavigationMenuItem(text: 'About', active: true),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Home'), findsOneWidget);
      expect(find.text('About'), findsOneWidget);
    });

    testWidgets('handles disabled items correctly', (tester) async {
      var tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Disabled Item',
                  disabled: true,
                  onTap: () => tapped = true,
                ),
              ],
            ),
          ),
        ),
      );

      // Try to tap disabled item
      await tester.tap(find.text('Disabled Item'));
      await tester.pumpAndSettle();
      expect(tapped, isFalse);
    });

    testWidgets('displays icons in navigation items correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Home',
                  icon: Icon(Icons.home),
                ),
                ShadcnNavigationMenuItem(
                  text: 'Settings',
                  leading: Icon(Icons.settings),
                ),
                ShadcnNavigationMenuItem(
                  text: 'Profile',
                  trailing: Icon(Icons.arrow_forward),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.home), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
    });

    testWidgets('handles dropdown menus with children', (tester) async {
      var productsTapped = false;
      var electronicsTapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(text: 'Home'),
                ShadcnNavigationMenuItem(
                  text: 'Products',
                  onTap: () => productsTapped = true,
                  children: [
                    ShadcnNavigationMenuItem(
                      text: 'Electronics',
                      onTap: () => electronicsTapped = true,
                    ),
                    ShadcnNavigationMenuItem(text: 'Clothing'),
                  ],
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Products'), findsOneWidget);
      expect(find.byType(PopupMenuButton<ShadcnNavigationMenuItem>), findsOneWidget);
      
      // Should show dropdown arrow
      expect(find.byIcon(Icons.keyboard_arrow_down), findsOneWidget);

      // Tap on Products to open dropdown
      await tester.tap(find.text('Products'));
      await tester.pumpAndSettle();
      
      // Should show dropdown items
      expect(find.text('Electronics'), findsOneWidget);
      expect(find.text('Clothing'), findsOneWidget);
      
      // Tap on Electronics
      await tester.tap(find.text('Electronics'));
      await tester.pumpAndSettle();
      expect(electronicsTapped, isTrue);
    });

    testWidgets('shows dividers when requested', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Home',
                  showDividerAfter: true,
                ),
                ShadcnNavigationMenuItem(text: 'About'),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Home'), findsOneWidget);
      expect(find.text('About'), findsOneWidget);
      // Divider should be present (as a Container)
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('calls onItemSelected callback with selected item', (tester) async {
      ShadcnNavigationMenuItem? selectedItem;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnNavigationMenu(
              onItemSelected: (item) => selectedItem = item,
              items: [
                ShadcnNavigationMenuItem(text: 'Home', onTap: () {}),
                ShadcnNavigationMenuItem(text: 'About', onTap: () {}),
              ],
            ),
          ),
        ),
      );

      await tester.tap(find.text('About'));
      await tester.pumpAndSettle();
      
      expect(selectedItem, isNotNull);
      expect(selectedItem!.text, equals('About'));
    });

    testWidgets('shows active indicator when showActiveIndicator is true', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              showActiveIndicator: true,
              items: [
                ShadcnNavigationMenuItem(text: 'Home'),
                ShadcnNavigationMenuItem(text: 'About', active: true),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Home'), findsOneWidget);
      expect(find.text('About'), findsOneWidget);
      // Active indicator should be present as a Container
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('respects disabled state for entire navigation menu', (tester) async {
      var homeTapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnNavigationMenu(
              disabled: true,
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Home',
                  onTap: () => homeTapped = true,
                ),
              ],
            ),
          ),
        ),
      );

      await tester.tap(find.text('Home'));
      await tester.pumpAndSettle();
      expect(homeTapped, isFalse);
    });

    testWidgets('applies theme correctly', (tester) async {
      const customTheme = ShadcnNavigationMenuTheme(
        itemColor: Colors.red,
        itemActiveColor: Colors.blue,
        backgroundColor: Colors.yellow,
      );
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              theme: customTheme,
              items: [
                ShadcnNavigationMenuItem(text: 'Home'),
                ShadcnNavigationMenuItem(text: 'About', active: true),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(ShadcnNavigationMenu), findsOneWidget);
    });

    testWidgets('provides proper semantics', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              semanticLabel: 'Main navigation menu',
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Home',
                  semanticLabel: 'Go to home page',
                ),
                ShadcnNavigationMenuItem(text: 'About'),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(ShadcnNavigationMenu), findsOneWidget);
    });

    testWidgets('shows tooltips when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnNavigationMenu(
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Home',
                  tooltip: 'Navigate to home page',
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(Tooltip), findsOneWidget);
    });
  });

  group('ShadcnNavigationMenuItem', () {
    testWidgets('creates item with required properties', (tester) async {
      const item = ShadcnNavigationMenuItem(text: 'Test Item');
      
      expect(item.text, equals('Test Item'));
      expect(item.icon, isNull);
      expect(item.leading, isNull);
      expect(item.trailing, isNull);
      expect(item.onTap, isNull);
      expect(item.children, isNull);
      expect(item.active, isFalse);
      expect(item.disabled, isFalse);
      expect(item.semanticLabel, isNull);
      expect(item.tooltip, isNull);
      expect(item.showDividerAfter, isFalse);
    });

    testWidgets('creates item with all properties', (tester) async {
      var tapped = false;
      final item = ShadcnNavigationMenuItem(
        text: 'Test Item',
        icon: const Icon(Icons.star),
        leading: const Icon(Icons.arrow_back),
        trailing: const Icon(Icons.arrow_forward),
        onTap: () => tapped = true,
        children: const [
          ShadcnNavigationMenuItem(text: 'Child 1'),
          ShadcnNavigationMenuItem(text: 'Child 2'),
        ],
        active: true,
        disabled: false,
        semanticLabel: 'Test semantic label',
        tooltip: 'Test tooltip',
        showDividerAfter: true,
      );
      
      expect(item.text, equals('Test Item'));
      expect(item.icon, isNotNull);
      expect(item.leading, isNotNull);
      expect(item.trailing, isNotNull);
      expect(item.onTap, isNotNull);
      expect(item.children, isNotNull);
      expect(item.children!.length, equals(2));
      expect(item.active, isTrue);
      expect(item.disabled, isFalse);
      expect(item.semanticLabel, equals('Test semantic label'));
      expect(item.tooltip, equals('Test tooltip'));
      expect(item.showDividerAfter, isTrue);
      
      // Test callback
      item.onTap!();
      expect(tapped, isTrue);
    });

    testWidgets('hasChildren getter works correctly', (tester) async {
      const itemWithChildren = ShadcnNavigationMenuItem(
        text: 'Parent',
        children: [
          ShadcnNavigationMenuItem(text: 'Child'),
        ],
      );
      const itemWithoutChildren = ShadcnNavigationMenuItem(text: 'Single');
      const itemWithEmptyChildren = ShadcnNavigationMenuItem(
        text: 'Empty Parent',
        children: [],
      );
      
      expect(itemWithChildren.hasChildren, isTrue);
      expect(itemWithoutChildren.hasChildren, isFalse);
      expect(itemWithEmptyChildren.hasChildren, isFalse);
    });

    testWidgets('isInteractive getter works correctly', (tester) async {
      final itemWithTap = ShadcnNavigationMenuItem(
        text: 'Tappable',
        onTap: () {},
      );
      const itemWithChildren = ShadcnNavigationMenuItem(
        text: 'Parent',
        children: [
          ShadcnNavigationMenuItem(text: 'Child'),
        ],
      );
      const itemNonInteractive = ShadcnNavigationMenuItem(text: 'Static');
      
      expect(itemWithTap.isInteractive, isTrue);
      expect(itemWithChildren.isInteractive, isTrue);
      expect(itemNonInteractive.isInteractive, isFalse);
    });

    testWidgets('equality works correctly', (tester) async {
      const item1 = ShadcnNavigationMenuItem(text: 'Test');
      const item2 = ShadcnNavigationMenuItem(text: 'Test');
      const item3 = ShadcnNavigationMenuItem(text: 'Different');
      
      expect(item1, equals(item2));
      expect(item1, isNot(equals(item3)));
    });

    testWidgets('toString works correctly', (tester) async {
      const item = ShadcnNavigationMenuItem(
        text: 'Test Item',
        active: true,
        children: [
          ShadcnNavigationMenuItem(text: 'Child'),
        ],
      );
      
      final string = item.toString();
      expect(string, contains('Test Item'));
      expect(string, contains('active: true'));
      expect(string, contains('hasChildren: true'));
      expect(string, contains('hasCallback: false'));
    });

    group('copyWith extension', () {
      testWidgets('copies with new values', (tester) async {
        const original = ShadcnNavigationMenuItem(
          text: 'Original',
          active: false,
          disabled: false,
        );
        
        final copied = original.copyWith(
          text: 'Modified',
          active: true,
          trailing: const Icon(Icons.star),
        );
        
        expect(copied.text, equals('Modified'));
        expect(copied.active, isTrue);
        expect(copied.trailing, isNotNull);
        expect(copied.disabled, isFalse); // Should preserve original
      });

      testWidgets('preserves original values when null passed', (tester) async {
        const original = ShadcnNavigationMenuItem(
          text: 'Original',
          active: true,
          disabled: false,
        );
        
        final copied = original.copyWith(
          // Only change disabled, keep everything else
          disabled: true,
        );
        
        expect(copied.text, equals('Original'));
        expect(copied.active, isTrue);
        expect(copied.disabled, isTrue);
      });
    });
  });

  group('NavigationIndicatorPosition', () {
    testWidgets('has all expected values', (tester) async {
      expect(NavigationIndicatorPosition.values, hasLength(4));
      expect(NavigationIndicatorPosition.values, contains(NavigationIndicatorPosition.top));
      expect(NavigationIndicatorPosition.values, contains(NavigationIndicatorPosition.bottom));
      expect(NavigationIndicatorPosition.values, contains(NavigationIndicatorPosition.left));
      expect(NavigationIndicatorPosition.values, contains(NavigationIndicatorPosition.right));
    });
  });
}