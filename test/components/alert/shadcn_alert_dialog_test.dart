import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnAlertDialog', () {
    late ThemeData testTheme;
    late ShadcnAlertTheme alertTheme;

    setUpAll(() {
      final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
      alertTheme = ShadcnAlertTheme.defaultTheme(colorScheme);
      testTheme = ThemeData.from(colorScheme: colorScheme).copyWith(
        extensions: [alertTheme],
      );
    });

    Widget buildTestWidget({required Widget child}) {
      return MaterialApp(
        theme: testTheme,
        home: Scaffold(
          body: child,
        ),
      );
    }

    group('constructor', () {
      test('creates dialog with required content', () {
        const dialog = ShadcnAlertDialog(
          content: 'Test content',
        );

        expect(dialog.variant, equals(ShadcnAlertVariant.defaultVariant));
        expect(dialog.content, equals('Test content'));
        expect(dialog.contentWidget, isNull);
        expect(dialog.title, isNull);
        expect(dialog.icon, isNull);
        expect(dialog.actions, isNull);
        expect(dialog.verticalActions, isFalse);
        expect(dialog.barrierDismissible, isTrue);
      });

      test('creates dialog with content widget', () {
        const contentWidget = Text('Content widget');
        const dialog = ShadcnAlertDialog(
          contentWidget: contentWidget,
        );

        expect(dialog.contentWidget, equals(contentWidget));
        expect(dialog.content, isNull);
      });

      test('throws assertion error when both content and contentWidget are null', () {
        expect(
          () => const ShadcnAlertDialog(),
          throwsA(isA<AssertionError>()),
        );
      });

      test('creates dialog with custom values', () {
        const title = 'Test Title';
        const content = 'Test Content';
        const icon = Icon(Icons.star);
        final actions = [TextButton(onPressed: () {}, child: const Text('OK'))];

        final dialog = ShadcnAlertDialog(
          variant: ShadcnAlertVariant.destructive,
          icon: icon,
          title: title,
          content: content,
          actions: actions,
          verticalActions: true,
          semanticLabel: 'Test Label',
          excludeFromSemantics: true,
          barrierDismissible: false,
        );

        expect(dialog.variant, equals(ShadcnAlertVariant.destructive));
        expect(dialog.icon, equals(icon));
        expect(dialog.title, equals(title));
        expect(dialog.content, equals(content));
        expect(dialog.actions, equals(actions));
        expect(dialog.verticalActions, isTrue);
        expect(dialog.semanticLabel, equals('Test Label'));
        expect(dialog.excludeFromSemantics, isTrue);
        expect(dialog.barrierDismissible, isFalse);
      });
    });

    group('named constructors', () {
      test('info constructor sets default variant', () {
        const dialog = ShadcnAlertDialog.info(content: 'Info');
        expect(dialog.variant, equals(ShadcnAlertVariant.defaultVariant));
      });

      test('destructive constructor sets destructive variant', () {
        const dialog = ShadcnAlertDialog.destructive(content: 'Warning');
        expect(dialog.variant, equals(ShadcnAlertVariant.destructive));
      });
    });

    group('rendering', () {
      testWidgets('renders title and content', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              title: 'Test Title',
              content: 'Test Content',
            ),
          ),
        );

        expect(find.text('Test Title'), findsOneWidget);
        expect(find.text('Test Content'), findsOneWidget);
      });

      testWidgets('renders custom content widget', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              title: 'Test Title',
              contentWidget: Text('Custom Content'),
            ),
          ),
        );

        expect(find.text('Test Title'), findsOneWidget);
        expect(find.text('Custom Content'), findsOneWidget);
      });

      testWidgets('renders actions', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: ShadcnAlertDialog(
              content: 'Test',
              actions: [
                TextButton(onPressed: () {}, child: const Text('Cancel')),
                ElevatedButton(onPressed: () {}, child: const Text('OK')),
              ],
            ),
          ),
        );

        expect(find.text('Cancel'), findsOneWidget);
        expect(find.text('OK'), findsOneWidget);
      });

      testWidgets('renders default icon for default variant', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              title: 'Test',
              content: 'Content',
            ),
          ),
        );

        expect(find.byIcon(Icons.info_outline), findsOneWidget);
      });

      testWidgets('renders default icon for destructive variant', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog.destructive(
              title: 'Test',
              content: 'Content',
            ),
          ),
        );

        expect(find.byIcon(Icons.warning_amber_outlined), findsOneWidget);
      });

      testWidgets('renders custom icon when provided', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              icon: Icon(Icons.star),
              title: 'Test',
              content: 'Content',
            ),
          ),
        );

        expect(find.byIcon(Icons.star), findsOneWidget);
        expect(find.byIcon(Icons.info_outline), findsNothing);
      });

      testWidgets('does not render icon when no title', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              content: 'Content without title',
            ),
          ),
        );

        expect(find.byType(Icon), findsNothing);
      });

      testWidgets('renders without actions when none provided', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              content: 'No actions',
            ),
          ),
        );

        expect(find.byType(TextButton), findsNothing);
        expect(find.byType(ElevatedButton), findsNothing);
      });
    });

    group('vertical actions', () {
      testWidgets('renders actions horizontally by default', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: ShadcnAlertDialog(
              content: 'Test',
              actions: [
                TextButton(onPressed: () {}, child: const Text('Cancel')),
                ElevatedButton(onPressed: () {}, child: const Text('OK')),
              ],
            ),
          ),
        );

        final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
        expect(alertDialog.actionsAlignment, equals(MainAxisAlignment.end));
      });

      testWidgets('renders actions vertically when specified', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: ShadcnAlertDialog(
              content: 'Test',
              verticalActions: true,
              actions: [
                TextButton(onPressed: () {}, child: const Text('Cancel')),
                ElevatedButton(onPressed: () {}, child: const Text('OK')),
              ],
            ),
          ),
        );

        final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
        expect(alertDialog.actionsAlignment, equals(MainAxisAlignment.spaceEvenly));
        
        // Check that actions are wrapped in SizedBox with full width
        final sizedBoxes = tester.widgetList<SizedBox>(
          find.descendant(
            of: find.byType(AlertDialog),
            matching: find.byType(SizedBox),
          ),
        ).where((sb) => sb.width == double.infinity);
        
        expect(sizedBoxes.length, equals(2)); // One for each action
      });
    });

    group('theming', () {
      testWidgets('applies default theme styling', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              content: 'Test',
            ),
          ),
        );

        final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
        expect(alertDialog.backgroundColor, isNotNull);
        expect(alertDialog.shape, isA<RoundedRectangleBorder>());
      });

      testWidgets('applies destructive variant colors', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog.destructive(
              content: 'Warning',
            ),
          ),
        );

        final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
        // Should have destructive background color (different from default)
        expect(alertDialog.backgroundColor, isNotNull);
        expect(alertDialog.backgroundColor, isNot(equals(alertTheme.defaultBackground)));
      });

      testWidgets('applies custom theme through Theme widget', (WidgetTester tester) async {
        const customTheme = ShadcnAlertTheme(
          defaultBackground: Colors.purple,
          borderRadius: BorderRadius.all(Radius.circular(20)),
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: testTheme.copyWith(extensions: [customTheme]),
            home: Scaffold(
              body: const ShadcnAlertDialog(
                content: 'Custom themed',
              ),
            ),
          ),
        );

        final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
        expect(alertDialog.backgroundColor, equals(Colors.purple));
        
        final shape = alertDialog.shape as RoundedRectangleBorder;
        expect(shape.borderRadius, equals(const BorderRadius.all(Radius.circular(20))));
      });
    });

    group('show method', () {
      testWidgets('shows dialog using show method', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  const ShadcnAlertDialog(
                    content: 'Dialog content',
                  ).show(context);
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        expect(find.text('Dialog content'), findsOneWidget);
        expect(find.byType(AlertDialog), findsOneWidget);
      });

      testWidgets('returns value when dialog is dismissed', (WidgetTester tester) async {
        String? result;

        await tester.pumpWidget(
          buildTestWidget(
            child: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () async {
                  result = await ShadcnAlertDialog(
                    content: 'Dialog content',
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop('dismissed'),
                        child: const Text('Dismiss'),
                      ),
                    ],
                  ).show<String>(context);
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        await tester.tap(find.text('Dismiss'));
        await tester.pumpAndSettle();

        expect(result, equals('dismissed'));
      });
    });

    group('extension methods', () {
      testWidgets('showInfo displays info dialog', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  ShadcnAlertDialogExtension.showInfo(
                    context,
                    title: 'Info',
                    content: 'Info content',
                  );
                },
                child: const Text('Show Info'),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Info'));
        await tester.pumpAndSettle();

        expect(find.text('Info'), findsOneWidget);
        expect(find.text('Info content'), findsOneWidget);
        expect(find.byIcon(Icons.info_outline), findsOneWidget);
      });

      testWidgets('showDestructive displays destructive dialog', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  ShadcnAlertDialogExtension.showDestructive(
                    context,
                    title: 'Warning',
                    content: 'Warning content',
                  );
                },
                child: const Text('Show Warning'),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Warning'));
        await tester.pumpAndSettle();

        expect(find.text('Warning'), findsOneWidget);
        expect(find.text('Warning content'), findsOneWidget);
        expect(find.byIcon(Icons.warning_amber_outlined), findsOneWidget);
      });

      testWidgets('showConfirmation displays confirmation dialog with Yes/No buttons', (WidgetTester tester) async {
        bool? result;

        await tester.pumpWidget(
          buildTestWidget(
            child: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () async {
                  result = await ShadcnAlertDialogExtension.showConfirmation(
                    context,
                    title: 'Confirm',
                    content: 'Are you sure?',
                  );
                },
                child: const Text('Show Confirmation'),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Confirmation'));
        await tester.pumpAndSettle();

        expect(find.text('Confirm'), findsOneWidget);
        expect(find.text('Are you sure?'), findsOneWidget);
        expect(find.text('Yes'), findsOneWidget);
        expect(find.text('No'), findsOneWidget);

        await tester.tap(find.text('Yes'));
        await tester.pumpAndSettle();

        expect(result, isTrue);
      });

      testWidgets('showDestructiveConfirmation displays delete confirmation', (WidgetTester tester) async {
        bool? result;

        await tester.pumpWidget(
          buildTestWidget(
            child: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () async {
                  result = await ShadcnAlertDialogExtension.showDestructiveConfirmation(
                    context,
                    title: 'Delete Item',
                    content: 'This cannot be undone.',
                  );
                },
                child: const Text('Show Delete'),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Delete'));
        await tester.pumpAndSettle();

        expect(find.text('Delete Item'), findsOneWidget);
        expect(find.text('This cannot be undone.'), findsOneWidget);
        expect(find.text('Delete'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);

        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        expect(result, isFalse);
      });
    });

    group('accessibility', () {
      testWidgets('includes semantic information', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              title: 'Accessible Dialog',
              content: 'This dialog is accessible',
              semanticLabel: 'Custom semantic label',
            ),
          ),
        );

        final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
        expect(alertDialog.semanticLabel, equals('Custom semantic label'));
      });

      testWidgets('builds semantic label from content when not provided', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              title: 'Dialog Title',
              content: 'Dialog content',
            ),
          ),
        );

        final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
        expect(alertDialog.semanticLabel, contains('Alert Dialog'));
        expect(alertDialog.semanticLabel, contains('Dialog Title'));
        expect(alertDialog.semanticLabel, contains('Dialog content'));
      });
    });

    group('edge cases', () {
      testWidgets('handles null theme gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.from(
              colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            ), // No alert theme extension
            home: Scaffold(
              body: const ShadcnAlertDialog(
                content: 'No theme',
              ),
            ),
          ),
        );

        // Should render without errors using fallback theme
        expect(find.text('No theme'), findsOneWidget);
        expect(find.byType(AlertDialog), findsOneWidget);
      });

      testWidgets('prefers contentWidget over content text', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlertDialog(
              content: 'Text content',
              contentWidget: Text('Widget content'),
            ),
          ),
        );

        expect(find.text('Widget content'), findsOneWidget);
        expect(find.text('Text content'), findsNothing);
      });
    });
  });
}