import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnAlert', () {
    late ThemeData testTheme;
    late ShadcnAlertTheme alertTheme;

    setUpAll(() {
      final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
      alertTheme = ShadcnAlertTheme.defaultTheme(colorScheme);
      testTheme = ThemeData.from(colorScheme: colorScheme).copyWith(
        extensions: [alertTheme],
      );
    });

    Widget buildTestWidget({required Widget child}) {
      return MaterialApp(
        theme: testTheme,
        home: Scaffold(
          body: child,
        ),
      );
    }

    group('constructor', () {
      test('creates alert with default values', () {
        const alert = ShadcnAlert();

        expect(alert.variant, equals(ShadcnAlertVariant.defaultVariant));
        expect(alert.icon, isNull);
        expect(alert.title, isNull);
        expect(alert.description, isNull);
        expect(alert.onTap, isNull);
        expect(alert.dismissible, isFalse);
        expect(alert.onDismiss, isNull);
        expect(alert.padding, isNull);
        expect(alert.borderRadius, isNull);
        expect(alert.semanticLabel, isNull);
        expect(alert.excludeFromSemantics, isFalse);
      });

      test('creates alert with custom values', () {
        const title = Text('Test Title');
        const description = Text('Test Description');
        const icon = Icon(Icons.star);
        void onTap() {}
        void onDismiss() {}

        final alert = ShadcnAlert(
          variant: ShadcnAlertVariant.destructive,
          icon: icon,
          title: title,
          description: description,
          onTap: onTap,
          dismissible: true,
          onDismiss: onDismiss,
          semanticLabel: 'Test Label',
          excludeFromSemantics: true,
        );

        expect(alert.variant, equals(ShadcnAlertVariant.destructive));
        expect(alert.icon, equals(icon));
        expect(alert.title, equals(title));
        expect(alert.description, equals(description));
        expect(alert.onTap, equals(onTap));
        expect(alert.dismissible, isTrue);
        expect(alert.onDismiss, equals(onDismiss));
        expect(alert.semanticLabel, equals('Test Label'));
        expect(alert.excludeFromSemantics, isTrue);
      });
    });

    group('named constructors', () {
      test('info constructor sets default variant', () {
        const alert = ShadcnAlert.info(title: Text('Info'));
        expect(alert.variant, equals(ShadcnAlertVariant.defaultVariant));
      });

      test('destructive constructor sets destructive variant', () {
        const alert = ShadcnAlert.destructive(title: Text('Warning'));
        expect(alert.variant, equals(ShadcnAlertVariant.destructive));
      });
    });

    group('rendering', () {
      testWidgets('renders title and description', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Test Title'),
              description: Text('Test Description'),
            ),
          ),
        );

        expect(find.text('Test Title'), findsOneWidget);
        expect(find.text('Test Description'), findsOneWidget);
      });

      testWidgets('renders default icon for default variant', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Test'),
            ),
          ),
        );

        expect(find.byIcon(Icons.info_outline), findsOneWidget);
      });

      testWidgets('renders default icon for destructive variant', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert.destructive(
              title: Text('Test'),
            ),
          ),
        );

        expect(find.byIcon(Icons.warning_amber_outlined), findsOneWidget);
      });

      testWidgets('renders custom icon when provided', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              icon: Icon(Icons.star),
              title: Text('Test'),
            ),
          ),
        );

        expect(find.byIcon(Icons.star), findsOneWidget);
        expect(find.byIcon(Icons.info_outline), findsNothing);
      });

      testWidgets('renders only title when description is null', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Only Title'),
            ),
          ),
        );

        expect(find.text('Only Title'), findsOneWidget);
        expect(find.byType(Text), findsOneWidget); // Only one Text widget (title)
      });

      testWidgets('renders only description when title is null', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              description: Text('Only Description'),
            ),
          ),
        );

        expect(find.text('Only Description'), findsOneWidget);
      });

      testWidgets('renders without content shows no text', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(),
          ),
        );

        expect(find.byType(Text), findsNothing);
      });
    });

    group('theming', () {
      testWidgets('applies default theme styling', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Test'),
            ),
          ),
        );

        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(ShadcnAlert),
            matching: find.byType(Container),
          ).first,
        );

        final decoration = container.decoration as BoxDecoration;
        expect(decoration.color, isNotNull); // Should have background color
        expect(decoration.border, isNotNull); // Should have border
        expect(decoration.borderRadius, isNotNull); // Should have border radius
      });

      testWidgets('applies custom theme through Theme widget', (WidgetTester tester) async {
        const customTheme = ShadcnAlertTheme(
          defaultBackground: Colors.red,
          padding: EdgeInsets.all(32),
          borderRadius: BorderRadius.all(Radius.circular(20)),
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: testTheme.copyWith(extensions: [customTheme]),
            home: Scaffold(
              body: const ShadcnAlert(
                title: Text('Test'),
              ),
            ),
          ),
        );

        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(ShadcnAlert),
            matching: find.byType(Container),
          ).first,
        );

        expect(container.padding, equals(const EdgeInsets.all(32)));
        
        final decoration = container.decoration as BoxDecoration;
        expect(decoration.color, equals(Colors.red));
        expect(decoration.borderRadius, equals(const BorderRadius.all(Radius.circular(20))));
      });
    });

    group('interactions', () {
      testWidgets('calls onTap when tapped', (WidgetTester tester) async {
        bool tapped = false;

        await tester.pumpWidget(
          buildTestWidget(
            child: ShadcnAlert(
              title: const Text('Tappable'),
              onTap: () {
                tapped = true;
              },
            ),
          ),
        );

        await tester.tap(find.byType(ShadcnAlert));
        expect(tapped, isTrue);
      });

      testWidgets('renders dismiss button when dismissible', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: ShadcnAlert(
              title: const Text('Dismissible'),
              dismissible: true,
              onDismiss: () {},
            ),
          ),
        );

        expect(find.byIcon(Icons.close), findsOneWidget);
      });

      testWidgets('calls onDismiss when dismiss button tapped', (WidgetTester tester) async {
        bool dismissed = false;

        await tester.pumpWidget(
          buildTestWidget(
            child: ShadcnAlert(
              title: const Text('Dismissible'),
              dismissible: true,
              onDismiss: () {
                dismissed = true;
              },
            ),
          ),
        );

        await tester.tap(find.byIcon(Icons.close));
        expect(dismissed, isTrue);
      });

      testWidgets('does not render dismiss button when not dismissible', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Not Dismissible'),
              dismissible: false,
            ),
          ),
        );

        expect(find.byIcon(Icons.close), findsNothing);
      });
    });

    group('variants', () {
      testWidgets('applies default variant styling', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              variant: ShadcnAlertVariant.defaultVariant,
              title: Text('Default'),
            ),
          ),
        );

        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(ShadcnAlert),
            matching: find.byType(Container),
          ).first,
        );

        final decoration = container.decoration as BoxDecoration;
        expect(decoration.color, equals(alertTheme.defaultBackground));
      });

      testWidgets('applies destructive variant styling', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              variant: ShadcnAlertVariant.destructive,
              title: Text('Destructive'),
            ),
          ),
        );

        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(ShadcnAlert),
            matching: find.byType(Container),
          ).first,
        );

        final decoration = container.decoration as BoxDecoration;
        expect(decoration.color, equals(alertTheme.destructiveBackground));
      });
    });

    group('accessibility', () {
      testWidgets('includes semantic information', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Accessible Alert'),
              description: Text('This alert is accessible'),
              semanticLabel: 'Custom semantic label',
            ),
          ),
        );

        final semantics = tester.getSemantics(find.byType(ShadcnAlert));
        expect(semantics.label, equals('Custom semantic label'));
      });

      testWidgets('builds semantic label from content when not provided', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Alert Title'),
              description: Text('Alert description'),
            ),
          ),
        );

        final semantics = tester.getSemantics(find.byType(ShadcnAlert));
        expect(semantics.label, contains('Information'));
        expect(semantics.label, contains('Alert Title'));
        expect(semantics.label, contains('Alert description'));
      });

      testWidgets('excludes from semantics when requested', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Hidden Alert'),
              excludeFromSemantics: true,
            ),
          ),
        );

        expect(find.bySemanticsLabel('Hidden Alert'), findsNothing);
      });

      testWidgets('marks destructive alerts as live region', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert.destructive(
              title: Text('Critical Alert'),
            ),
          ),
        );

        final semantics = tester.getSemantics(find.byType(ShadcnAlert));
        expect(semantics.hasFlag(SemanticsFlag.isLiveRegion), isTrue);
      });
    });

    group('layout and styling', () {
      testWidgets('respects custom padding', (WidgetTester tester) async {
        const customPadding = EdgeInsets.all(50);

        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Custom Padding'),
              padding: customPadding,
            ),
          ),
        );

        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(ShadcnAlert),
            matching: find.byType(Container),
          ).first,
        );

        expect(container.padding, equals(customPadding));
      });

      testWidgets('respects custom border radius', (WidgetTester tester) async {
        const customRadius = BorderRadius.all(Radius.circular(25));

        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(
              title: Text('Custom Radius'),
              borderRadius: customRadius,
            ),
          ),
        );

        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(ShadcnAlert),
            matching: find.byType(Container),
          ).first,
        );

        final decoration = container.decoration as BoxDecoration;
        expect(decoration.borderRadius, equals(customRadius));
      });
    });

    group('edge cases', () {
      testWidgets('handles null theme gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData.from(
              colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            ), // No alert theme extension
            home: Scaffold(
              body: const ShadcnAlert(
                title: Text('No Theme'),
              ),
            ),
          ),
        );

        // Should render without errors using fallback theme
        expect(find.text('No Theme'), findsOneWidget);
        expect(find.byType(ShadcnAlert), findsOneWidget);
      });

      testWidgets('handles empty content gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          buildTestWidget(
            child: const ShadcnAlert(),
          ),
        );

        // Should render without errors even with no content
        expect(find.byType(ShadcnAlert), findsOneWidget);
      });
    });
  });
}