import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnAlertTheme', () {
    late ColorScheme lightColorScheme;
    late ColorScheme darkColorScheme;

    setUpAll(() {
      lightColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      darkColorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      );
    });

    group('defaultTheme factory', () {
      test('creates theme with light color scheme', () {
        final theme = ShadcnAlertTheme.defaultTheme(lightColorScheme);

        expect(theme.defaultBackground, isNotNull);
        expect(theme.defaultForeground, equals(lightColorScheme.onSurface));
        expect(theme.defaultBorder, isNotNull);
        expect(theme.defaultIconColor, isNotNull);
        
        expect(theme.destructiveBackground, isNotNull);
        expect(theme.destructiveForeground, equals(lightColorScheme.error));
        expect(theme.destructiveBorder, isNotNull);
        expect(theme.destructiveIconColor, equals(lightColorScheme.error));
        
        expect(theme.padding, equals(ShadcnTokens.paddingAll(ShadcnTokens.spacing4)));
        expect(theme.borderRadius, equals(ShadcnTokens.borderRadius(ShadcnTokens.radiusMd)));
        expect(theme.borderWidth, equals(ShadcnTokens.borderWidth));
        expect(theme.iconGap, equals(ShadcnTokens.spacing3));
        expect(theme.contentGap, equals(ShadcnTokens.spacing1));
        expect(theme.iconSize, equals(ShadcnTokens.iconSizeMd));
      });

      test('creates theme with dark color scheme', () {
        final theme = ShadcnAlertTheme.defaultTheme(darkColorScheme);

        expect(theme.defaultBackground, isNotNull);
        expect(theme.defaultForeground, equals(darkColorScheme.onSurface));
        expect(theme.destructiveForeground, equals(darkColorScheme.error));
        
        // Dark theme should have different alpha values
        expect(theme.defaultBackground?.a, greaterThan(0.1));
        expect(theme.destructiveBackground?.a, greaterThan(0.1));
      });
    });

    group('copyWith', () {
      test('creates copy with new values', () {
        final original = ShadcnAlertTheme.defaultTheme(lightColorScheme);
        final copy = original.copyWith(
          defaultBackground: Colors.red,
          iconSize: 32.0,
          borderWidth: 3.0,
        );

        expect(copy.defaultBackground, equals(Colors.red));
        expect(copy.iconSize, equals(32.0));
        expect(copy.borderWidth, equals(3.0));
        
        // Original values should be preserved
        expect(copy.defaultForeground, equals(original.defaultForeground));
        expect(copy.padding, equals(original.padding));
      });

      test('preserves original values when null provided', () {
        final original = ShadcnAlertTheme.defaultTheme(lightColorScheme);
        final copy = original.copyWith();

        expect(copy.defaultBackground, equals(original.defaultBackground));
        expect(copy.defaultForeground, equals(original.defaultForeground));
        expect(copy.padding, equals(original.padding));
        expect(copy.iconSize, equals(original.iconSize));
      });
    });

    group('lerp', () {
      test('interpolates between themes correctly', () {
        final theme1 = ShadcnAlertTheme(
          defaultBackground: Colors.red,
          iconSize: 16.0,
          borderWidth: 1.0,
        );
        
        final theme2 = ShadcnAlertTheme(
          defaultBackground: Colors.blue,
          iconSize: 24.0,
          borderWidth: 3.0,
        );

        final lerped = theme1.lerp(theme2, 0.5);

        expect(lerped.defaultBackground, equals(Color.lerp(Colors.red, Colors.blue, 0.5)));
        expect(lerped.iconSize, equals(20.0)); // (16 + 24) / 2
        expect(lerped.borderWidth, equals(2.0)); // (1 + 3) / 2
      });

      test('returns original theme when other is null', () {
        final original = ShadcnAlertTheme.defaultTheme(lightColorScheme);
        final lerped = original.lerp(null, 0.5);

        expect(lerped, equals(original));
      });

      test('handles edge values correctly', () {
        final theme1 = ShadcnAlertTheme(defaultBackground: Colors.red);
        final theme2 = ShadcnAlertTheme(defaultBackground: Colors.blue);

        final lerped0 = theme1.lerp(theme2, 0.0);
        final lerped1 = theme1.lerp(theme2, 1.0);

        expect(lerped0.defaultBackground, equals(Colors.red));
        expect(lerped1.defaultBackground, equals(Colors.blue));
      });
    });

    group('validation', () {
      test('validates correct theme', () {
        final theme = ShadcnAlertTheme.defaultTheme(lightColorScheme);
        expect(theme.validate(), isTrue);
        expect(() => theme.validate(throwOnError: true), returnsNormally);
      });

      test('fails validation for negative border width', () {
        const theme = ShadcnAlertTheme(borderWidth: -1.0);
        expect(theme.validate(), isFalse);
        expect(() => theme.validate(throwOnError: true), throwsA(isA<FlutterError>()));
      });

      test('fails validation for negative icon gap', () {
        const theme = ShadcnAlertTheme(iconGap: -1.0);
        expect(theme.validate(), isFalse);
        expect(() => theme.validate(throwOnError: true), throwsA(isA<FlutterError>()));
      });

      test('fails validation for negative content gap', () {
        const theme = ShadcnAlertTheme(contentGap: -1.0);
        expect(theme.validate(), isFalse);
        expect(() => theme.validate(throwOnError: true), throwsA(isA<FlutterError>()));
      });

      test('fails validation for non-positive icon size', () {
        const theme = ShadcnAlertTheme(iconSize: 0.0);
        expect(theme.validate(), isFalse);
        expect(() => theme.validate(throwOnError: true), throwsA(isA<FlutterError>()));
      });
    });

    group('equality and hashCode', () {
      test('identical themes are equal', () {
        final theme1 = ShadcnAlertTheme.defaultTheme(lightColorScheme);
        final theme2 = ShadcnAlertTheme.defaultTheme(lightColorScheme);

        expect(theme1, equals(theme2));
        expect(theme1.hashCode, equals(theme2.hashCode));
      });

      test('different themes are not equal', () {
        final theme1 = ShadcnAlertTheme.defaultTheme(lightColorScheme);
        final theme2 = theme1.copyWith(defaultBackground: Colors.red);

        expect(theme1, isNot(equals(theme2)));
        expect(theme1.hashCode, isNot(equals(theme2.hashCode)));
      });

      test('same instance is identical', () {
        final theme = ShadcnAlertTheme.defaultTheme(lightColorScheme);
        expect(identical(theme, theme), isTrue);
      });
    });

    group('toString', () {
      test('provides meaningful string representation', () {
        const theme = ShadcnAlertTheme(
          defaultBackground: Colors.red,
          iconSize: 24.0,
        );

        final string = theme.toString();
        expect(string, contains('ShadcnAlertTheme'));
        expect(string, contains('defaultBackground'));
        expect(string, contains('iconSize'));
      });
    });
  });

  group('ShadcnAlertVariant', () {
    group('defaultIcon', () {
      test('returns correct icons for variants', () {
        expect(ShadcnAlertVariant.defaultVariant.defaultIcon, equals(Icons.info_outline));
        expect(ShadcnAlertVariant.destructive.defaultIcon, equals(Icons.warning_amber_outlined));
      });
    });

    group('displayName', () {
      test('returns correct display names', () {
        expect(ShadcnAlertVariant.defaultVariant.displayName, equals('Default'));
        expect(ShadcnAlertVariant.destructive.displayName, equals('Destructive'));
      });
    });

    group('isCritical', () {
      test('returns correct critical status', () {
        expect(ShadcnAlertVariant.defaultVariant.isCritical, isFalse);
        expect(ShadcnAlertVariant.destructive.isCritical, isTrue);
      });
    });
  });
}