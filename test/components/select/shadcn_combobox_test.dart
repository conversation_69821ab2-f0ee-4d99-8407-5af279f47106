import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnCombobox', () {
    late List<ShadcnSelectOption<String>> testOptions;
    
    setUp(() {
      testOptions = [
        const ShadcnSelectOption(value: 'apple', label: 'Apple'),
        const ShadcnSelectOption(value: 'banana', label: 'Banana'),
        const ShadcnSelectOption(value: 'cherry', label: 'Cherry'),
        const ShadcnSelectOption(
          value: 'date',
          label: 'Date',
          leading: Icon(Icons.calendar_today),
        ),
        const ShadcnSelectOption(
          value: 'elderberry',
          label: 'Elderberry (Disabled)',
          enabled: false,
        ),
      ];
    });

    Widget createComboboxWidget({
      String? value,
      ValueChanged<String?>? onChanged,
      bool enabled = true,
      List<ShadcnSelectOption<String>>? options,
      String? placeholder,
      String? searchPlaceholder,
      bool? showClearButton,
      bool? caseSensitiveSearch,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ShadcnCombobox<String>(
            options: options ?? testOptions,
            value: value,
            onChanged: onChanged,
            enabled: enabled,
            placeholder: placeholder,
            searchPlaceholder: searchPlaceholder,
            showClearButton: showClearButton,
            caseSensitiveSearch: caseSensitiveSearch,
          ),
        ),
      );
    }

    testWidgets('renders with placeholder when no value is selected', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget());

      expect(find.text('Search options...'), findsOneWidget);
      expect(find.byIcon(Icons.keyboard_arrow_down), findsOneWidget);
    });

    testWidgets('displays selected option label', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget(value: 'apple'));

      expect(find.text('Apple'), findsOneWidget);
      expect(find.text('Search options...'), findsNothing);
    });

    testWidgets('shows custom placeholder', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget(
        placeholder: 'Choose a fruit',
      ));

      expect(find.text('Choose a fruit'), findsOneWidget);
    });

    testWidgets('opens dropdown when tapped', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createComboboxWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Tap to open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Check that search input and options are visible
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Apple'), findsWidgets);
      expect(find.text('Banana'), findsOneWidget);
      expect(find.text('Cherry'), findsOneWidget);
    });

    testWidgets('filters options when typing in search', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createComboboxWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Type in search field
      await tester.enterText(find.byType(TextField), 'ap');
      await tester.pumpAndSettle();

      // Should only show Apple
      expect(find.text('Apple'), findsOneWidget);
      expect(find.text('Banana'), findsNothing);
      expect(find.text('Cherry'), findsNothing);
    });

    testWidgets('shows no results message when no matches', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget());

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Search for non-existent option
      await tester.enterText(find.byType(TextField), 'xyz');
      await tester.pumpAndSettle();

      expect(find.text('No results found'), findsOneWidget);
    });

    testWidgets('case sensitive search works', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget(
        caseSensitiveSearch: true,
      ));

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Search with lowercase
      await tester.enterText(find.byType(TextField), 'apple');
      await tester.pumpAndSettle();

      // Should not match 'Apple' (capital A)
      expect(find.text('No results found'), findsOneWidget);
      expect(find.text('Apple'), findsNothing);
    });

    testWidgets('case insensitive search works by default', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget());

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Search with lowercase
      await tester.enterText(find.byType(TextField), 'apple');
      await tester.pumpAndSettle();

      // Should match 'Apple'
      expect(find.text('Apple'), findsOneWidget);
      expect(find.text('No results found'), findsNothing);
    });

    testWidgets('selects option when clicked', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createComboboxWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Click on option
      await tester.tap(find.text('Banana').last);
      await tester.pumpAndSettle();

      expect(selectedValue, equals('banana'));
    });

    testWidgets('shows clear button when value is selected', (WidgetTester tester) async {
      String? selectedValue = 'apple';

      await tester.pumpWidget(createComboboxWidget(
        value: selectedValue,
        onChanged: (value) => selectedValue = value,
      ));

      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('clear button clears selection', (WidgetTester tester) async {
      String? selectedValue = 'apple';

      await tester.pumpWidget(createComboboxWidget(
        value: selectedValue,
        onChanged: (value) => selectedValue = value,
      ));

      // Tap clear button
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      expect(selectedValue, isNull);
    });

    testWidgets('hides clear button when showClearButton is false', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget(
        value: 'apple',
        showClearButton: false,
      ));

      expect(find.byIcon(Icons.close), findsNothing);
    });

    testWidgets('keyboard navigation works', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createComboboxWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Open dropdown and focus search
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      final textField = find.byType(TextField);
      await tester.tap(textField);
      await tester.pumpAndSettle();

      // Simulate arrow down key
      await tester.sendKeyEvent(LogicalKeyboardKey.arrowDown);
      await tester.pumpAndSettle();

      // Simulate enter key
      await tester.sendKeyEvent(LogicalKeyboardKey.enter);
      await tester.pumpAndSettle();

      // Should select the first option
      expect(selectedValue, equals('apple'));
    });

    testWidgets('escape key closes dropdown', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget());

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Verify dropdown is open
      expect(find.byType(TextField), findsOneWidget);

      // Press escape
      await tester.sendKeyEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();

      // Dropdown should be closed
      expect(find.byType(TextField), findsNothing);
    });

    testWidgets('displays leading icons in options', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget(value: 'date'));

      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
      expect(find.text('Date'), findsOneWidget);
    });

    testWidgets('handles disabled options', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createComboboxWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Try to click disabled option
      await tester.tap(find.text('Elderberry (Disabled)'));
      await tester.pumpAndSettle();

      // Should not select
      expect(selectedValue, isNull);
    });

    testWidgets('handles empty options list', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget(options: []));

      // Should render without errors
      expect(find.text('Search options...'), findsOneWidget);

      // Opening should show no results
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      expect(find.text('No results found'), findsOneWidget);
    });

    testWidgets('custom search placeholder works', (WidgetTester tester) async {
      await tester.pumpWidget(createComboboxWidget(
        searchPlaceholder: 'Find fruit...',
      ));

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Check search placeholder
      expect(find.text('Find fruit...'), findsOneWidget);
    });

    testWidgets('custom filter function works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnCombobox<String>(
              options: testOptions,
              filterFunction: (option, search) {
                // Only match if the option starts with the search term
                return option.label.toLowerCase().startsWith(search.toLowerCase());
              },
            ),
          ),
        ),
      );

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Search for 'a'
      await tester.enterText(find.byType(TextField), 'a');
      await tester.pumpAndSettle();

      // Should only show 'Apple' (starts with 'a'), not 'Banana' (contains 'a')
      expect(find.text('Apple'), findsOneWidget);
      expect(find.text('Banana'), findsNothing);
    });

    testWidgets('applies custom theme', (WidgetTester tester) async {
      final customTheme = ThemeData(
        extensions: [
          ShadcnSelectTheme.defaultTheme(ColorScheme.fromSeed(seedColor: Colors.green))
            .copyWith(searchHeight: 50.0),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: customTheme,
          home: Scaffold(
            body: ShadcnCombobox<String>(
              options: testOptions,
            ),
          ),
        ),
      );

      // Open dropdown to see search field
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Find search container
      final searchContainer = tester.widget<Container>(
        find.descendant(
          of: find.byType(TextField).first,
          matching: find.byType(Container),
        ).first,
      );

      expect(searchContainer.constraints?.maxHeight, equals(50.0));
    });

    testWidgets('provides proper semantics', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnCombobox<String>(
              options: testOptions,
              semanticLabel: 'Fruit Combobox',
            ),
          ),
        ),
      );

      expect(find.bySemanticsLabel('Fruit Combobox'), findsOneWidget);
    });

    testWidgets('shows tooltip when provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnCombobox<String>(
              options: testOptions,
              tooltip: 'Select a fruit',
            ),
          ),
        ),
      );

      // Long press to show tooltip
      await tester.longPress(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      expect(find.text('Select a fruit'), findsOneWidget);
    });

    testWidgets('search change callback works', (WidgetTester tester) async {
      String lastSearchText = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnCombobox<String>(
              options: testOptions,
              onSearchChanged: (text) => lastSearchText = text,
            ),
          ),
        ),
      );

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Type in search
      await tester.enterText(find.byType(TextField), 'test');
      await tester.pumpAndSettle();

      expect(lastSearchText, equals('test'));
    });

    testWidgets('initial search text works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnCombobox<String>(
              options: testOptions,
              initialSearchText: 'apple',
            ),
          ),
        ),
      );

      // Open dropdown
      await tester.tap(find.byType(ShadcnCombobox<String>));
      await tester.pumpAndSettle();

      // Should show filtered results based on initial search
      expect(find.text('Apple'), findsOneWidget);
      expect(find.text('Banana'), findsNothing);

      // Search field should contain initial text
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, equals('apple'));
    });
  });
}