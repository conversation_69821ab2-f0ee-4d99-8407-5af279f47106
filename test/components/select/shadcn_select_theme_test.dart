import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnSelectTheme', () {
    test('creates default theme with light ColorScheme', () {
      final colorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      );
      
      final theme = ShadcnSelectTheme.defaultTheme(colorScheme);
      
      expect(theme.triggerBackground, equals(colorScheme.surface));
      expect(theme.triggerForeground, equals(colorScheme.onSurface));
      expect(theme.triggerBorder, equals(colorScheme.outline));
      expect(theme.triggerHeight, equals(ShadcnTokens.inputHeightMd));
      expect(theme.showArrow, isTrue);
      expect(theme.showClearButton, isTrue);
      expect(theme.allowMultiSelect, isFalse);
      expect(theme.caseSensitiveSearch, isFalse);
    });

    test('creates default theme with dark ColorScheme', () {
      final colorScheme = ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      );
      
      final theme = ShadcnSelectTheme.defaultTheme(colorScheme);
      
      expect(theme.triggerBackground, equals(colorScheme.surface));
      expect(theme.triggerForeground, equals(colorScheme.onSurface));
      expect(theme.triggerHoverBackground, 
             equals(colorScheme.surface.withAlpha(200)));
    });

    test('copyWith creates new instance with updated properties', () {
      final originalTheme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      final updatedTheme = originalTheme.copyWith(
        triggerHeight: 50.0,
        showArrow: false,
        allowMultiSelect: true,
      );
      
      expect(updatedTheme.triggerHeight, equals(50.0));
      expect(updatedTheme.showArrow, isFalse);
      expect(updatedTheme.allowMultiSelect, isTrue);
      
      // Original properties should remain unchanged
      expect(updatedTheme.triggerBackground, equals(originalTheme.triggerBackground));
      expect(updatedTheme.triggerForeground, equals(originalTheme.triggerForeground));
    });

    test('lerp interpolates between two themes correctly', () {
      final theme1 = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      ).copyWith(
        triggerHeight: 40.0,
        triggerBackground: Colors.blue,
      );
      
      final theme2 = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.red),
      ).copyWith(
        triggerHeight: 60.0,
        triggerBackground: Colors.red,
      );
      
      final lerpedTheme = theme1.lerp(theme2, 0.5);
      
      // Colors should be interpolated
      expect(lerpedTheme.triggerBackground, 
             equals(Color.lerp(Colors.blue, Colors.red, 0.5)));
      
      // Discrete values should use threshold
      expect(lerpedTheme.triggerHeight, equals(theme1.triggerHeight)); // t < 0.5
    });

    test('lerp with null other returns original theme', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      final lerpedTheme = theme.lerp(null, 0.5);
      
      expect(lerpedTheme, equals(theme));
    });

    test('validate returns true for valid theme', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      expect(theme.validate(), isTrue);
    });

    test('validate detects invalid trigger height', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      ).copyWith(triggerHeight: -10.0); // Invalid negative height
      
      expect(theme.validate(), isFalse);
      expect(() => theme.validate(throwOnError: true), throwsA(isA<FlutterError>()));
    });

    test('validate detects invalid dropdown max height', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      ).copyWith(dropdownMaxHeight: -100.0); // Invalid negative height
      
      expect(theme.validate(), isFalse);
    });

    test('validate detects invalid item height', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      ).copyWith(itemHeight: 0.0); // Invalid zero height
      
      expect(theme.validate(), isFalse);
    });

    test('validate detects invalid arrow size', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      ).copyWith(arrowSize: 100.0); // Too large
      
      expect(theme.validate(), isFalse);
    });

    testWidgets('helper methods resolve text styles correctly', (WidgetTester tester) async {
      late ShadcnSelectTheme theme;
      late BuildContext capturedContext;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              capturedContext = context;
              theme = ShadcnSelectTheme.defaultTheme(
                Theme.of(context).colorScheme,
              );
              return Container();
            },
          ),
        ),
      );
      
      // Test trigger text style resolution
      final triggerStyle = theme.resolveTriggerTextStyle(capturedContext);
      expect(triggerStyle, isA<TextStyle>());
      expect(triggerStyle.fontSize, isNotNull);
      
      // Test item text style resolution
      final itemStyle = theme.resolveItemTextStyle(capturedContext);
      expect(itemStyle, isA<TextStyle>());
      
      // Test search text style resolution
      final searchStyle = theme.resolveSearchTextStyle(capturedContext);
      expect(searchStyle, isA<TextStyle>());
      
      // Test search placeholder style resolution
      final placeholderStyle = theme.resolveSearchPlaceholderStyle(capturedContext);
      expect(placeholderStyle, isA<TextStyle>());
      expect(placeholderStyle.color?.alpha, lessThan(255)); // Should be semi-transparent
    });

    testWidgets('theme extensions integrate with Material theme', (WidgetTester tester) async {
      final selectTheme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.purple),
      );
      
      final customTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.purple),
        extensions: [selectTheme],
      );
      
      await tester.pumpWidget(
        MaterialApp(
          theme: customTheme,
          home: Builder(
            builder: (context) {
              final resolvedTheme = Theme.of(context).extension<ShadcnSelectTheme>();
              
              expect(resolvedTheme, isNotNull);
              expect(resolvedTheme, equals(selectTheme));
              
              return Container();
            },
          ),
        ),
      );
    });

    test('themes with different properties are not equal', () {
      final theme1 = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      final theme2 = theme1.copyWith(triggerHeight: 50.0);
      
      expect(theme1 == theme2, isFalse);
      expect(theme1.hashCode == theme2.hashCode, isFalse);
    });

    test('animation properties have sensible defaults', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      expect(theme.animationDuration, equals(ShadcnTokens.durationFast));
      expect(theme.animationCurve, equals(Curves.easeInOut));
    });

    test('spacing properties use consistent tokens', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      expect(theme.triggerPadding, 
             equals(ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing3)));
      expect(theme.dropdownPadding, 
             equals(ShadcnTokens.paddingAll(ShadcnTokens.spacing1)));
      expect(theme.itemPadding, 
             equals(ShadcnTokens.paddingHorizontal(ShadcnTokens.spacing2)));
    });

    test('border radius properties use consistent tokens', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      expect(theme.triggerBorderRadius, 
             equals(ShadcnTokens.borderRadius(ShadcnTokens.radiusMd)));
      expect(theme.dropdownBorderRadius, 
             equals(ShadcnTokens.borderRadius(ShadcnTokens.radiusMd)));
      expect(theme.itemBorderRadius, 
             equals(ShadcnTokens.borderRadius(ShadcnTokens.radiusSm)));
    });

    test('size properties have reasonable defaults', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      expect(theme.triggerHeight, equals(ShadcnTokens.inputHeightMd));
      expect(theme.searchHeight, equals(ShadcnTokens.inputHeightSm));
      expect(theme.itemHeight, equals(32.0));
      expect(theme.dropdownMaxHeight, equals(300.0));
      expect(theme.dropdownMinWidth, equals(180.0));
    });

    test('icon sizes have reasonable defaults', () {
      final theme = ShadcnSelectTheme.defaultTheme(
        ColorScheme.fromSeed(seedColor: Colors.blue),
      );
      
      expect(theme.arrowSize, equals(ShadcnTokens.iconSizeSm));
      expect(theme.clearButtonSize, equals(ShadcnTokens.iconSizeSm));
    });

    test('all color properties are properly initialized', () {
      final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
      final theme = ShadcnSelectTheme.defaultTheme(colorScheme);
      
      // Trigger colors
      expect(theme.triggerBackground, isNotNull);
      expect(theme.triggerForeground, isNotNull);
      expect(theme.triggerBorder, isNotNull);
      expect(theme.triggerHoverBackground, isNotNull);
      expect(theme.triggerFocusBackground, isNotNull);
      expect(theme.triggerFocusBorder, isNotNull);
      expect(theme.triggerDisabledBackground, isNotNull);
      expect(theme.triggerDisabledForeground, isNotNull);
      
      // Dropdown colors
      expect(theme.dropdownBackground, isNotNull);
      expect(theme.dropdownShadow, isNotNull);
      expect(theme.dropdownBorder, isNotNull);
      
      // Item colors
      expect(theme.itemBackground, isNotNull);
      expect(theme.itemForeground, isNotNull);
      expect(theme.itemHoverBackground, isNotNull);
      expect(theme.itemFocusBackground, isNotNull);
      expect(theme.itemSelectedBackground, isNotNull);
      expect(theme.itemSelectedForeground, isNotNull);
      expect(theme.itemDisabledBackground, isNotNull);
      expect(theme.itemDisabledForeground, isNotNull);
      
      // Search colors
      expect(theme.searchBackground, isNotNull);
      expect(theme.searchForeground, isNotNull);
      expect(theme.searchBorder, isNotNull);
      expect(theme.searchPlaceholder, isNotNull);
      
      // Icon colors
      expect(theme.arrowColor, isNotNull);
      expect(theme.clearButtonColor, isNotNull);
    });
  });
}