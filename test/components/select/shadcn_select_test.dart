import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnSelect', () {
    late List<ShadcnSelectOption<String>> testOptions;
    
    setUp(() {
      testOptions = [
        const ShadcnSelectOption(value: 'option1', label: 'Option 1'),
        const ShadcnSelectOption(value: 'option2', label: 'Option 2'),
        const ShadcnSelectOption(
          value: 'option3',
          label: 'Option 3',
          leading: Icon(Icons.star),
        ),
        const ShadcnSelectOption(
          value: 'option4',
          label: 'Option 4 (Disabled)',
          enabled: false,
        ),
      ];
    });

    Widget createSelectWidget({
      String? value,
      ValueChanged<String?>? onChanged,
      bool enabled = true,
      List<ShadcnSelectOption<String>>? options,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ShadcnSelect<String>(
            options: options ?? testOptions,
            value: value,
            onChanged: onChanged,
            enabled: enabled,
          ),
        ),
      );
    }

    testWidgets('renders with placeholder when no value is selected', (WidgetTester tester) async {
      await tester.pumpWidget(createSelectWidget());

      expect(find.text('Select an option...'), findsOneWidget);
      expect(find.byIcon(Icons.keyboard_arrow_down), findsOneWidget);
    });

    testWidgets('displays selected option label', (WidgetTester tester) async {
      await tester.pumpWidget(createSelectWidget(value: 'option1'));

      expect(find.text('Option 1'), findsOneWidget);
      expect(find.text('Select an option...'), findsNothing);
    });

    testWidgets('displays custom placeholder', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnSelect<String>(
              options: testOptions,
              placeholder: 'Choose an option',
            ),
          ),
        ),
      );

      expect(find.text('Choose an option'), findsOneWidget);
    });

    testWidgets('opens dropdown when tapped', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createSelectWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Tap to open dropdown
      await tester.tap(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();

      // Check that dropdown options are visible
      expect(find.text('Option 1'), findsWidgets);
      expect(find.text('Option 2'), findsOneWidget);
      expect(find.text('Option 3'), findsOneWidget);
    });

    testWidgets('selects option when tapped', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createSelectWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Open dropdown
      await tester.tap(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();

      // Tap on second option
      await tester.tap(find.text('Option 2').last);
      await tester.pumpAndSettle();

      expect(selectedValue, equals('option2'));
    });

    testWidgets('closes dropdown after selection', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createSelectWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Open dropdown
      await tester.tap(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();

      // Select option
      await tester.tap(find.text('Option 1').last);
      await tester.pumpAndSettle();

      // Dropdown should be closed (only one Option 1 text should remain)
      expect(find.text('Option 1'), findsOneWidget);
      expect(find.text('Option 2'), findsNothing);
    });

    testWidgets('displays leading icon when provided', (WidgetTester tester) async {
      await tester.pumpWidget(createSelectWidget(value: 'option3'));

      expect(find.byIcon(Icons.star), findsOneWidget);
      expect(find.text('Option 3'), findsOneWidget);
    });

    testWidgets('handles disabled state', (WidgetTester tester) async {
      await tester.pumpWidget(createSelectWidget(enabled: false));

      // Try to tap - should not open dropdown
      await tester.tap(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();

      // Should still show placeholder, no options visible
      expect(find.text('Select an option...'), findsOneWidget);
      expect(find.text('Option 1'), findsNothing);
    });

    testWidgets('disabled options cannot be selected', (WidgetTester tester) async {
      String? selectedValue;

      await tester.pumpWidget(createSelectWidget(
        onChanged: (value) => selectedValue = value,
      ));

      // Open dropdown
      await tester.tap(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();

      // Try to tap disabled option
      await tester.tap(find.text('Option 4 (Disabled)'));
      await tester.pumpAndSettle();

      // Value should not change
      expect(selectedValue, isNull);
    });

    testWidgets('applies custom width and height', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnSelect<String>(
              options: testOptions,
              width: 200,
              height: 50,
            ),
          ),
        ),
      );

      final container = tester.widget<AnimatedContainer>(
        find.descendant(
          of: find.byType(ShadcnSelect<String>),
          matching: find.byType(AnimatedContainer),
        ).first,
      );

      expect(container.width, equals(200));
      expect(container.height, equals(50));
    });

    testWidgets('shows arrow by default', (WidgetTester tester) async {
      await tester.pumpWidget(createSelectWidget());

      expect(find.byIcon(Icons.keyboard_arrow_down), findsOneWidget);
    });

    testWidgets('hides arrow when showArrow is false', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnSelect<String>(
              options: testOptions,
              showArrow: false,
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.keyboard_arrow_down), findsNothing);
    });

    testWidgets('applies custom theme', (WidgetTester tester) async {
      final customTheme = ThemeData(
        extensions: [
          ShadcnSelectTheme.defaultTheme(ColorScheme.fromSeed(seedColor: Colors.red))
            .copyWith(triggerHeight: 60.0),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: customTheme,
          home: Scaffold(
            body: ShadcnSelect<String>(
              options: testOptions,
            ),
          ),
        ),
      );

      final container = tester.widget<AnimatedContainer>(
        find.descendant(
          of: find.byType(ShadcnSelect<String>),
          matching: find.byType(AnimatedContainer),
        ).first,
      );

      expect(container.height, equals(60.0));
    });

    testWidgets('handles focus and keyboard navigation', (WidgetTester tester) async {
      String? selectedValue;
      final focusNode = FocusNode();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnSelect<String>(
              options: testOptions,
              onChanged: (value) => selectedValue = value,
              focusNode: focusNode,
            ),
          ),
        ),
      );

      // Request focus
      focusNode.requestFocus();
      await tester.pumpAndSettle();

      expect(focusNode.hasFocus, isTrue);
    });

    testWidgets('provides proper semantics', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnSelect<String>(
              options: testOptions,
              semanticLabel: 'Test Select',
            ),
          ),
        ),
      );

      expect(find.bySemanticsLabel('Test Select'), findsOneWidget);
    });

    testWidgets('shows tooltip when provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnSelect<String>(
              options: testOptions,
              tooltip: 'Select tooltip',
            ),
          ),
        ),
      );

      // Long press to show tooltip
      await tester.longPress(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();

      expect(find.text('Select tooltip'), findsOneWidget);
    });

    testWidgets('handles empty options list', (WidgetTester tester) async {
      await tester.pumpWidget(createSelectWidget(options: []));

      // Should render without errors
      expect(find.text('Select an option...'), findsOneWidget);

      // Tapping should not crash
      await tester.tap(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();
    });

    testWidgets('rotates arrow when dropdown opens', (WidgetTester tester) async {
      await tester.pumpWidget(createSelectWidget());

      final arrowWidget = find.byType(AnimatedRotation);
      expect(arrowWidget, findsOneWidget);

      // Get initial rotation
      final initialRotation = tester.widget<AnimatedRotation>(arrowWidget);
      expect(initialRotation.turns, equals(0));

      // Open dropdown
      await tester.tap(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();

      // Arrow should be rotated
      final rotatedArrow = tester.widget<AnimatedRotation>(arrowWidget);
      expect(rotatedArrow.turns, equals(0.5));
    });

    testWidgets('closes dropdown when tapping outside', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ShadcnSelect<String>(
                  options: testOptions,
                ),
                Container(
                  key: const Key('outside'),
                  height: 100,
                  color: Colors.blue,
                ),
              ],
            ),
          ),
        ),
      );

      // Open dropdown
      await tester.tap(find.byType(ShadcnSelect<String>));
      await tester.pumpAndSettle();

      // Verify dropdown is open
      expect(find.text('Option 1'), findsWidgets);

      // Tap outside
      await tester.tap(find.byKey(const Key('outside')));
      await tester.pumpAndSettle();

      // Dropdown should be closed
      expect(find.text('Option 1'), findsNothing);
    });
  });

  group('ShadcnSelectOption', () {
    testWidgets('equality works correctly', (WidgetTester tester) async {
      const option1 = ShadcnSelectOption(value: 'test', label: 'Test');
      const option2 = ShadcnSelectOption(value: 'test', label: 'Test Different Label');
      const option3 = ShadcnSelectOption(value: 'different', label: 'Test');

      expect(option1, equals(option2)); // Same value
      expect(option1, isNot(equals(option3))); // Different value
    });

    testWidgets('hashCode works correctly', (WidgetTester tester) async {
      const option1 = ShadcnSelectOption(value: 'test', label: 'Test');
      const option2 = ShadcnSelectOption(value: 'test', label: 'Test Different Label');

      expect(option1.hashCode, equals(option2.hashCode));
    });
  });
}