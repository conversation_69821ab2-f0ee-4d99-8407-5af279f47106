import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../../lib/src/theme/extensions/shadcn_breadcrumb_theme.dart';
import '../../../lib/src/constants/shadcn_tokens.dart';

void main() {
  group('ShadcnBreadcrumbTheme', () {
    testWidgets('creates with default values', (tester) async {
      final theme = ShadcnBreadcrumbTheme();
      
      expect(theme.itemColor, isNull);
      expect(theme.activeItemColor, isNull);
      expect(theme.separatorColor, isNull);
      expect(theme.backgroundColor, isNull);
      expect(theme.minHeight, isNull);
    });

    testWidgets('creates with all properties set', (tester) async {
      final theme = ShadcnBreadcrumbTheme(
        itemColor: Colors.red,
        activeItemColor: Colors.blue,
        disabledItemColor: Colors.grey,
        hoverColor: Colors.orange,
        focusColor: Colors.purple,
        pressedColor: Colors.pink,
        separatorColor: Colors.green,
        separatorIcon: const Icon(Icons.arrow_right),
        separatorSize: 16.0,
        separatorPadding: const EdgeInsets.all(4.0),
        containerPadding: const EdgeInsets.all(8.0),
        backgroundColor: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        minHeight: 40.0,
        textStyle: const TextStyle(fontSize: 14.0),
        activeTextStyle: const TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
        fontWeight: FontWeight.normal,
        fontSize: 14.0,
        itemSpacing: 8.0,
        itemBorderRadius: BorderRadius.circular(4.0),
        itemPadding: const EdgeInsets.symmetric(horizontal: 8.0),
        itemMinHeight: 24.0,
        animationDuration: const Duration(milliseconds: 200),
        animationCurve: Curves.easeInOut,
        focusWidth: 2.0,
        focusOffset: 1.0,
      );
      
      expect(theme.itemColor, equals(Colors.red));
      expect(theme.activeItemColor, equals(Colors.blue));
      expect(theme.disabledItemColor, equals(Colors.grey));
      expect(theme.hoverColor, equals(Colors.orange));
      expect(theme.focusColor, equals(Colors.purple));
      expect(theme.pressedColor, equals(Colors.pink));
      expect(theme.separatorColor, equals(Colors.green));
      expect(theme.separatorIcon, isA<Icon>());
      expect(theme.separatorSize, equals(16.0));
      expect(theme.separatorPadding, equals(const EdgeInsets.all(4.0)));
      expect(theme.containerPadding, equals(const EdgeInsets.all(8.0)));
      expect(theme.backgroundColor, equals(Colors.white));
      expect(theme.borderRadius, equals(BorderRadius.circular(8.0)));
      expect(theme.minHeight, equals(40.0));
      expect(theme.textStyle, equals(const TextStyle(fontSize: 14.0)));
      expect(theme.activeTextStyle, equals(const TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold)));
      expect(theme.fontWeight, equals(FontWeight.normal));
      expect(theme.fontSize, equals(14.0));
      expect(theme.itemSpacing, equals(8.0));
      expect(theme.itemBorderRadius, equals(BorderRadius.circular(4.0)));
      expect(theme.itemPadding, equals(const EdgeInsets.symmetric(horizontal: 8.0)));
      expect(theme.itemMinHeight, equals(24.0));
      expect(theme.animationDuration, equals(const Duration(milliseconds: 200)));
      expect(theme.animationCurve, equals(Curves.easeInOut));
      expect(theme.focusWidth, equals(2.0));
      expect(theme.focusOffset, equals(1.0));
    });

    group('defaultTheme', () {
      testWidgets('creates proper default theme for light mode', (tester) async {
        final colorScheme = ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        );
        final theme = ShadcnBreadcrumbTheme.defaultTheme(colorScheme);
        
        expect(theme.itemColor, isNotNull);
        expect(theme.activeItemColor, isNotNull);
        expect(theme.disabledItemColor, isNotNull);
        expect(theme.separatorColor, isNotNull);
        expect(theme.backgroundColor, equals(Colors.transparent));
        expect(theme.minHeight, equals(36.0)); // ShadcnTokens.buttonHeightSm
        expect(theme.animationDuration, equals(const Duration(milliseconds: 150))); // ShadcnTokens.durationFast
      });

      testWidgets('creates proper default theme for dark mode', (tester) async {
        final colorScheme = ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        );
        final theme = ShadcnBreadcrumbTheme.defaultTheme(colorScheme);
        
        expect(theme.itemColor, isNotNull);
        expect(theme.activeItemColor, isNotNull);
        // Dark mode should have different opacity for item color
        expect(theme.itemColor!.opacity, equals(0.7));
      });

      testWidgets('includes proper default separator', (tester) async {
        final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
        final theme = ShadcnBreadcrumbTheme.defaultTheme(colorScheme);
        
        expect(theme.separatorIcon, isA<Icon>());
        expect((theme.separatorIcon as Icon).icon, equals(Icons.chevron_right));
        expect(theme.separatorSize, equals(16.0)); // ShadcnTokens.iconSizeSm
      });

      testWidgets('sets proper text styles', (tester) async {
        final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
        final theme = ShadcnBreadcrumbTheme.defaultTheme(colorScheme);
        
        expect(theme.textStyle, isNotNull);
        expect(theme.activeTextStyle, isNotNull);
        expect(theme.textStyle!.fontSize, equals(14.0)); // ShadcnTokens.fontSizeMd
        expect(theme.activeTextStyle!.fontWeight, equals(FontWeight.w500)); // ShadcnTokens.fontWeightMedium
      });
    });

    group('copyWith', () {
      testWidgets('copies with new values', (tester) async {
        final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
        final original = ShadcnBreadcrumbTheme.defaultTheme(colorScheme);
        
        final copied = original.copyWith(
          itemColor: Colors.red,
          activeItemColor: Colors.blue,
          minHeight: 50.0,
          backgroundColor: Colors.yellow,
        );
        
        expect(copied.itemColor, equals(Colors.red));
        expect(copied.activeItemColor, equals(Colors.blue));
        expect(copied.minHeight, equals(50.0));
        expect(copied.backgroundColor, equals(Colors.yellow));
        
        // Other values should remain the same
        expect(copied.separatorColor, equals(original.separatorColor));
        expect(copied.textStyle, equals(original.textStyle));
      });

      testWidgets('preserves original values when null passed', (tester) async {
        final original = ShadcnBreadcrumbTheme(
          itemColor: Colors.red,
          minHeight: 40.0,
        );
        
        final copied = original.copyWith(
          activeItemColor: Colors.blue,
          // itemColor and minHeight not specified, should be preserved
        );
        
        expect(copied.itemColor, equals(Colors.red));
        expect(copied.minHeight, equals(40.0));
        expect(copied.activeItemColor, equals(Colors.blue));
      });
    });

    group('lerp', () {
      testWidgets('interpolates colors correctly', (tester) async {
        final theme1 = ShadcnBreadcrumbTheme(
          itemColor: Colors.red,
          activeItemColor: Colors.blue,
        );
        final theme2 = ShadcnBreadcrumbTheme(
          itemColor: Colors.green,
          activeItemColor: Colors.purple,
        );
        
        final lerped = theme1.lerp(theme2, 0.5);
        
        // Colors should be interpolated
        expect(lerped.itemColor, isNotNull);
        expect(lerped.activeItemColor, isNotNull);
        expect(lerped.itemColor, isNot(equals(Colors.red)));
        expect(lerped.itemColor, isNot(equals(Colors.green)));
      });

      testWidgets('uses threshold logic for non-color properties', (tester) async {
        final theme1 = ShadcnBreadcrumbTheme(
          minHeight: 30.0,
          fontSize: 12.0,
        );
        final theme2 = ShadcnBreadcrumbTheme(
          minHeight: 50.0,
          fontSize: 16.0,
        );
        
        final lerped1 = theme1.lerp(theme2, 0.3); // t < 0.5
        final lerped2 = theme1.lerp(theme2, 0.7); // t >= 0.5
        
        expect(lerped1.minHeight, equals(30.0)); // First theme values
        expect(lerped1.fontSize, equals(12.0));
        
        expect(lerped2.minHeight, equals(50.0)); // Second theme values
        expect(lerped2.fontSize, equals(16.0));
      });

      testWidgets('handles EdgeInsets lerp correctly', (tester) async {
        final theme1 = ShadcnBreadcrumbTheme(
          containerPadding: EdgeInsets.all(8.0),
          itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
        );
        final theme2 = ShadcnBreadcrumbTheme(
          containerPadding: EdgeInsets.all(16.0),
          itemPadding: EdgeInsets.symmetric(horizontal: 8.0),
        );
        
        final lerped = theme1.lerp(theme2, 0.5);
        
        expect(lerped.containerPadding, isNotNull);
        expect(lerped.itemPadding, isNotNull);
        // EdgeInsets.lerp should be used
        expect(lerped.containerPadding, equals(const EdgeInsets.all(12.0)));
        expect(lerped.itemPadding, equals(const EdgeInsets.symmetric(horizontal: 6.0)));
      });

      testWidgets('returns original theme when other is not ShadcnBreadcrumbTheme', (tester) async {
        final theme = ShadcnBreadcrumbTheme(itemColor: Colors.red);
        
        final result = theme.lerp(null, 0.5);
        
        expect(result, equals(theme));
      });
    });

    group('validate', () {
      testWidgets('validates successfully with required properties', (tester) async {
        final validTheme = ShadcnBreadcrumbTheme(
          itemColor: Colors.red,
          activeItemColor: Colors.blue,
          separatorColor: Colors.green,
          minHeight: 30.0,
          itemMinHeight: 24.0,
        );
        
        expect(validTheme.validate(), isTrue);
      });

      testWidgets('fails validation with missing required colors', (tester) async {
        final invalidTheme = ShadcnBreadcrumbTheme(
          // Missing itemColor, activeItemColor, separatorColor
          minHeight: 30.0,
        );
        
        expect(invalidTheme.validate(), isFalse);
      });

      testWidgets('fails validation with invalid size values', (tester) async {
        final invalidTheme = ShadcnBreadcrumbTheme(
          itemColor: Colors.red,
          activeItemColor: Colors.blue,
          separatorColor: Colors.green,
          minHeight: -10.0, // Invalid negative height
        );
        
        expect(invalidTheme.validate(), isFalse);
      });

      testWidgets('throws error when throwOnError is true', (tester) async {
        final invalidTheme = ShadcnBreadcrumbTheme(
          minHeight: -10.0,
        );
        
        expect(
          () => invalidTheme.validate(throwOnError: true),
          throwsA(isA<FlutterError>()),
        );
      });
    });

    group('equality and hashCode', () {
      testWidgets('equal themes have same hashCode', (tester) async {
        final theme1 = ShadcnBreadcrumbTheme(
          itemColor: Colors.red,
          activeItemColor: Colors.blue,
          minHeight: 30.0,
        );
        final theme2 = ShadcnBreadcrumbTheme(
          itemColor: Colors.red,
          activeItemColor: Colors.blue,
          minHeight: 30.0,
        );
        
        expect(theme1, equals(theme2));
        expect(theme1.hashCode, equals(theme2.hashCode));
      });

      testWidgets('different themes are not equal', (tester) async {
        final theme1 = ShadcnBreadcrumbTheme(
          itemColor: Colors.red,
          minHeight: 30.0,
        );
        final theme2 = ShadcnBreadcrumbTheme(
          itemColor: Colors.blue, // Different color
          minHeight: 30.0,
        );
        
        expect(theme1, isNot(equals(theme2)));
      });

      testWidgets('identical objects are equal', (tester) async {
        final theme = ShadcnBreadcrumbTheme(itemColor: Colors.red);
        
        expect(theme, equals(theme));
      });

      testWidgets('different types are not equal', (tester) async {
        final theme = ShadcnBreadcrumbTheme(itemColor: Colors.red);
        
        expect(theme, isNot(equals('not a theme')));
      });
    });

    group('toString', () {
      testWidgets('contains key information', (tester) async {
        final theme = ShadcnBreadcrumbTheme(
          itemColor: Colors.red,
          activeItemColor: Colors.blue,
          separatorColor: Colors.green,
          minHeight: 40.0,
        );
        
        final string = theme.toString();
        
        expect(string, contains('ShadcnBreadcrumbTheme'));
        expect(string, contains('40.0'));
      });
    });
  });
}