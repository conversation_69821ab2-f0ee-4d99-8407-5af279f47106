import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../../lib/src/components/breadcrumb/shadcn_breadcrumb.dart';
import '../../../lib/src/theme/extensions/shadcn_breadcrumb_theme.dart';

void main() {
  group('ShadcnBreadcrumb', () {
    late ThemeData testTheme;
    
    setUp(() {
      testTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        extensions: [
          ShadcnBreadcrumbTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue),
          ),
        ],
      );
    });

    testWidgets('renders empty breadcrumb when items list is empty', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(items: []),
          ),
        ),
      );

      expect(find.byType(ShadcnBreadcrumb), findsOneWidget);
      // Should render as empty widget
      expect(find.text('Home'), findsNothing);
      expect(find.text('Products'), findsNothing);
    });

    testWidgets('renders single breadcrumb item correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              items: [
                ShadcnBreadcrumbItem(text: 'Home'),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Home'), findsOneWidget);
      // No separator for single item
      expect(find.byIcon(Icons.chevron_right), findsNothing);
    });

    testWidgets('renders multiple breadcrumb items with separators', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              items: [
                ShadcnBreadcrumbItem(text: 'Home'),
                ShadcnBreadcrumbItem(text: 'Products'),
                ShadcnBreadcrumbItem(text: 'Electronics'),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Products'), findsOneWidget);
      expect(find.text('Electronics'), findsOneWidget);
      
      // Should have 2 separators for 3 items
      expect(find.byIcon(Icons.chevron_right), findsNWidgets(2));
    });

    testWidgets('handles tap callbacks correctly', (tester) async {
      var homeTapped = false;
      var productsTapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnBreadcrumb(
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Home',
                  onTap: () => homeTapped = true,
                ),
                ShadcnBreadcrumbItem(
                  text: 'Products',
                  onTap: () => productsTapped = true,
                ),
                ShadcnBreadcrumbItem(text: 'Current'),
              ],
            ),
          ),
        ),
      );

      // Tap on Home
      await tester.tap(find.text('Home'));
      await tester.pumpAndSettle();
      expect(homeTapped, isTrue);
      
      // Tap on Products
      await tester.tap(find.text('Products'));
      await tester.pumpAndSettle();
      expect(productsTapped, isTrue);
    });

    testWidgets('displays custom separator correctly', (tester) async {
      const customSeparator = Icon(Icons.arrow_forward);
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              separator: customSeparator,
              items: [
                ShadcnBreadcrumbItem(text: 'Home'),
                ShadcnBreadcrumbItem(text: 'Products'),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsNothing);
    });

    testWidgets('shows home icon when requested', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              showHome: true,
              items: [
                ShadcnBreadcrumbItem(text: 'Products'),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.home), findsOneWidget);
      expect(find.text('Products'), findsOneWidget);
      // Should have separator between home icon and first item
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('shows custom home icon when provided', (tester) async {
      const customHomeIcon = Icon(Icons.house);
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              showHome: true,
              homeIcon: customHomeIcon,
              items: [
                ShadcnBreadcrumbItem(text: 'Products'),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.house), findsOneWidget);
      expect(find.byIcon(Icons.home), findsNothing);
    });

    testWidgets('handles disabled items correctly', (tester) async {
      var tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnBreadcrumb(
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Disabled Item',
                  disabled: true,
                  onTap: () => tapped = true,
                ),
                ShadcnBreadcrumbItem(text: 'Normal Item'),
              ],
            ),
          ),
        ),
      );

      // Try to tap disabled item
      await tester.tap(find.text('Disabled Item'));
      await tester.pumpAndSettle();
      expect(tapped, isFalse);
    });

    testWidgets('handles maxItems truncation correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              maxItems: 3,
              items: [
                ShadcnBreadcrumbItem(text: 'Home'),
                ShadcnBreadcrumbItem(text: 'Category1'),
                ShadcnBreadcrumbItem(text: 'Category2'),
                ShadcnBreadcrumbItem(text: 'Category3'),
                ShadcnBreadcrumbItem(text: 'Current'),
              ],
            ),
          ),
        ),
      );

      // Should show first item, ellipsis, and last item
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('...'), findsOneWidget);
      expect(find.text('Current'), findsOneWidget);
      
      // Middle items should not be visible
      expect(find.text('Category1'), findsNothing);
      expect(find.text('Category2'), findsNothing);
    });

    testWidgets('displays icons in breadcrumb items correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Home',
                  icon: Icon(Icons.home),
                ),
                ShadcnBreadcrumbItem(
                  text: 'Products',
                  icon: Icon(Icons.shopping_bag),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.home), findsOneWidget);
      expect(find.byIcon(Icons.shopping_bag), findsOneWidget);
    });

    testWidgets('calls onItemTap callback with correct index', (tester) async {
      var tappedIndex = -1;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnBreadcrumb(
              onItemTap: (index) => tappedIndex = index,
              items: [
                ShadcnBreadcrumbItem(text: 'Home', onTap: () {}),
                ShadcnBreadcrumbItem(text: 'Products', onTap: () {}),
              ],
            ),
          ),
        ),
      );

      await tester.tap(find.text('Products'));
      await tester.pumpAndSettle();
      expect(tappedIndex, equals(1));
    });

    testWidgets('applies theme correctly', (tester) async {
      const customTheme = ShadcnBreadcrumbTheme(
        itemColor: Colors.red,
        activeItemColor: Colors.blue,
        separatorColor: Colors.green,
      );
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              theme: customTheme,
              items: [
                ShadcnBreadcrumbItem(text: 'Home'),
                ShadcnBreadcrumbItem(text: 'Current'),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(ShadcnBreadcrumb), findsOneWidget);
    });

    testWidgets('respects disabled state for entire breadcrumb', (tester) async {
      var homeTapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: Scaffold(
            body: ShadcnBreadcrumb(
              disabled: true,
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Home',
                  onTap: () => homeTapped = true,
                ),
                ShadcnBreadcrumbItem(text: 'Current'),
              ],
            ),
          ),
        ),
      );

      await tester.tap(find.text('Home'));
      await tester.pumpAndSettle();
      expect(homeTapped, isFalse);
    });

    testWidgets('provides proper semantics', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              semanticLabel: 'Main navigation breadcrumb',
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Home',
                  semanticLabel: 'Go to home page',
                ),
                ShadcnBreadcrumbItem(text: 'Current page'),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(ShadcnBreadcrumb), findsOneWidget);
    });

    testWidgets('shows tooltips when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: testTheme,
          home: const Scaffold(
            body: ShadcnBreadcrumb(
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Home',
                  tooltip: 'Navigate to home page',
                ),
                ShadcnBreadcrumbItem(text: 'Current'),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(Tooltip), findsOneWidget);
    });
  });

  group('ShadcnBreadcrumbItem', () {
    testWidgets('creates item with required properties', (tester) async {
      const item = ShadcnBreadcrumbItem(text: 'Test Item');
      
      expect(item.text, equals('Test Item'));
      expect(item.icon, isNull);
      expect(item.onTap, isNull);
      expect(item.disabled, isFalse);
      expect(item.semanticLabel, isNull);
      expect(item.tooltip, isNull);
    });

    testWidgets('creates item with all properties', (tester) async {
      var tapped = false;
      final item = ShadcnBreadcrumbItem(
        text: 'Test Item',
        icon: const Icon(Icons.star),
        onTap: () => tapped = true,
        disabled: true,
        semanticLabel: 'Test semantic label',
        tooltip: 'Test tooltip',
      );
      
      expect(item.text, equals('Test Item'));
      expect(item.icon, isNotNull);
      expect(item.onTap, isNotNull);
      expect(item.disabled, isTrue);
      expect(item.semanticLabel, equals('Test semantic label'));
      expect(item.tooltip, equals('Test tooltip'));
      
      // Test callback
      item.onTap!();
      expect(tapped, isTrue);
    });

    testWidgets('equality works correctly', (tester) async {
      const item1 = ShadcnBreadcrumbItem(text: 'Test');
      const item2 = ShadcnBreadcrumbItem(text: 'Test');
      const item3 = ShadcnBreadcrumbItem(text: 'Different');
      
      expect(item1, equals(item2));
      expect(item1, isNot(equals(item3)));
    });

    testWidgets('toString works correctly', (tester) async {
      const item = ShadcnBreadcrumbItem(text: 'Test Item');
      
      final string = item.toString();
      expect(string, contains('Test Item'));
      expect(string, contains('disabled: false'));
      expect(string, contains('hasCallback: false'));
    });
  });

  group('ShadcnBreadcrumbTheme', () {
    testWidgets('creates default theme correctly', (tester) async {
      final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
      final theme = ShadcnBreadcrumbTheme.defaultTheme(colorScheme);
      
      expect(theme.itemColor, isNotNull);
      expect(theme.activeItemColor, isNotNull);
      expect(theme.separatorColor, isNotNull);
      expect(theme.minHeight, isNotNull);
    });

    testWidgets('copyWith works correctly', (tester) async {
      final colorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
      final original = ShadcnBreadcrumbTheme.defaultTheme(colorScheme);
      final copied = original.copyWith(
        itemColor: Colors.red,
        minHeight: 50.0,
      );
      
      expect(copied.itemColor, equals(Colors.red));
      expect(copied.minHeight, equals(50.0));
      expect(copied.activeItemColor, equals(original.activeItemColor));
    });

    testWidgets('lerp works correctly', (tester) async {
      const theme1 = ShadcnBreadcrumbTheme(
        itemColor: Colors.red,
        minHeight: 30.0,
      );
      const theme2 = ShadcnBreadcrumbTheme(
        itemColor: Colors.blue,
        minHeight: 50.0,
      );
      
      final lerped = theme1.lerp(theme2, 0.5);
      expect(lerped.minHeight, equals(50.0)); // Uses t < 0.5 logic
    });

    testWidgets('validation works correctly', (tester) async {
      const validTheme = ShadcnBreadcrumbTheme(
        itemColor: Colors.red,
        activeItemColor: Colors.blue,
        separatorColor: Colors.green,
        minHeight: 30.0,
      );
      
      expect(validTheme.validate(), isTrue);
      
      const invalidTheme = ShadcnBreadcrumbTheme(
        minHeight: -10.0, // Invalid negative height
      );
      
      expect(invalidTheme.validate(), isFalse);
    });

    testWidgets('equality and hashCode work correctly', (tester) async {
      const theme1 = ShadcnBreadcrumbTheme(
        itemColor: Colors.red,
        minHeight: 30.0,
      );
      const theme2 = ShadcnBreadcrumbTheme(
        itemColor: Colors.red,
        minHeight: 30.0,
      );
      const theme3 = ShadcnBreadcrumbTheme(
        itemColor: Colors.blue,
        minHeight: 30.0,
      );
      
      expect(theme1, equals(theme2));
      expect(theme1, isNot(equals(theme3)));
      expect(theme1.hashCode, equals(theme2.hashCode));
    });

    testWidgets('toString works correctly', (tester) async {
      const theme = ShadcnBreadcrumbTheme(
        itemColor: Colors.red,
        minHeight: 30.0,
      );
      
      final string = theme.toString();
      expect(string, contains('ShadcnBreadcrumbTheme'));
      expect(string, contains('30.0'));
    });
  });
}