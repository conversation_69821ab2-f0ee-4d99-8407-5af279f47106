import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnButtonTheme', () {
    group('defaultTheme', () {
      testWidgets('creates valid light theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);

        expect(theme.primaryBackground, equals(colorScheme.primary));
        expect(theme.primaryForeground, equals(colorScheme.onPrimary));
        expect(theme.destructiveBackground, equals(colorScheme.error));
        expect(theme.destructiveForeground, equals(colorScheme.onError));
        expect(theme.mediumHeight, equals(ShadcnTokens.buttonHeightMd));
        expect(theme.borderRadius, equals(BorderRadius.circular(ShadcnTokens.radiusMd)));
      });

      testWidgets('creates valid dark theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.dark();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);

        expect(theme.primaryBackground, equals(colorScheme.primary));
        expect(theme.primaryForeground, equals(colorScheme.onPrimary));
        expect(theme.destructiveBackground, equals(colorScheme.error));
        expect(theme.destructiveForeground, equals(colorScheme.onError));
        expect(theme.mediumHeight, equals(ShadcnTokens.buttonHeightMd));
        expect(theme.borderRadius, equals(BorderRadius.circular(ShadcnTokens.radiusMd)));
      });

      testWidgets('includes all required variant colors', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);

        // Primary variant
        expect(theme.primaryBackground, isNotNull);
        expect(theme.primaryForeground, isNotNull);
        expect(theme.primaryHover, isNotNull);
        expect(theme.primaryPressed, isNotNull);
        expect(theme.primaryFocused, isNotNull);

        // Secondary variant
        expect(theme.secondaryBackground, isNotNull);
        expect(theme.secondaryForeground, isNotNull);
        expect(theme.secondaryHover, isNotNull);
        expect(theme.secondaryPressed, isNotNull);
        expect(theme.secondaryFocused, isNotNull);

        // Destructive variant
        expect(theme.destructiveBackground, isNotNull);
        expect(theme.destructiveForeground, isNotNull);
        expect(theme.destructiveHover, isNotNull);
        expect(theme.destructivePressed, isNotNull);
        expect(theme.destructiveFocused, isNotNull);

        // Outline variant
        expect(theme.outlineBackground, isNotNull);
        expect(theme.outlineForeground, isNotNull);
        expect(theme.outlineBorder, isNotNull);
        expect(theme.outlineHover, isNotNull);
        expect(theme.outlinePressed, isNotNull);
        expect(theme.outlineFocused, isNotNull);

        // Ghost variant
        expect(theme.ghostBackground, isNotNull);
        expect(theme.ghostForeground, isNotNull);
        expect(theme.ghostBorder, isNotNull);
        expect(theme.ghostHover, isNotNull);
        expect(theme.ghostPressed, isNotNull);
        expect(theme.ghostFocused, isNotNull);

        // Link variant
        expect(theme.linkBackground, isNotNull);
        expect(theme.linkForeground, isNotNull);
        expect(theme.linkBorder, isNotNull);
        expect(theme.linkHover, isNotNull);
        expect(theme.linkPressed, isNotNull);
        expect(theme.linkFocused, isNotNull);

        // Disabled states
        expect(theme.disabledBackground, isNotNull);
        expect(theme.disabledForeground, isNotNull);
        expect(theme.disabledBorder, isNotNull);
      });

      testWidgets('includes all size configurations', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);

        // Heights
        expect(theme.smallHeight, equals(ShadcnTokens.buttonHeightSm));
        expect(theme.mediumHeight, equals(ShadcnTokens.buttonHeightMd));
        expect(theme.largeHeight, equals(ShadcnTokens.buttonHeightLg));

        // Padding
        expect(theme.smallPadding, isNotNull);
        expect(theme.mediumPadding, isNotNull);
        expect(theme.largePadding, isNotNull);

        // Font sizes
        expect(theme.smallFontSize, equals(ShadcnTokens.fontSizeSm));
        expect(theme.mediumFontSize, equals(ShadcnTokens.fontSizeMd));
        expect(theme.largeFontSize, equals(ShadcnTokens.fontSizeLg));

        // Icon sizes
        expect(theme.smallIconSize, equals(ShadcnTokens.iconSizeSm));
        expect(theme.mediumIconSize, equals(ShadcnTokens.iconSizeMd));
        expect(theme.largeIconSize, equals(ShadcnTokens.iconSizeLg));
      });

      testWidgets('includes common properties', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);

        expect(theme.borderRadius, isNotNull);
        expect(theme.borderWidth, equals(ShadcnTokens.borderWidth));
        expect(theme.fontWeight, equals(ShadcnTokens.fontWeightMedium));
        expect(theme.iconSpacing, equals(ShadcnTokens.spacing2));
        expect(theme.minWidth, equals(64));
        expect(theme.animationDuration, equals(ShadcnTokens.durationFast));
        expect(theme.animationCurve, equals(Curves.easeInOut));
      });

      testWidgets('sets proper focus properties', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);

        expect(theme.focusColor, isNotNull);
        expect(theme.focusWidth, equals(2.0));
        expect(theme.focusOffset, equals(2.0));
      });
    });

    group('copyWith', () {
      testWidgets('preserves existing values when no changes', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final original = ShadcnButtonTheme.defaultTheme(colorScheme);
        final copy = original.copyWith();

        expect(copy.primaryBackground, equals(original.primaryBackground));
        expect(copy.primaryForeground, equals(original.primaryForeground));
        expect(copy.mediumHeight, equals(original.mediumHeight));
        expect(copy.borderRadius, equals(original.borderRadius));
      });

      testWidgets('applies specified changes', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final original = ShadcnButtonTheme.defaultTheme(colorScheme);
        final copy = original.copyWith(
          primaryBackground: Colors.purple,
          mediumHeight: 50,
          borderRadius: BorderRadius.circular(20),
        );

        expect(copy.primaryBackground, equals(Colors.purple));
        expect(copy.mediumHeight, equals(50));
        expect(copy.borderRadius, equals(BorderRadius.circular(20)));

        // Unchanged values should remain the same
        expect(copy.primaryForeground, equals(original.primaryForeground));
        expect(copy.smallHeight, equals(original.smallHeight));
      });

      testWidgets('handles all variant colors', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final original = ShadcnButtonTheme.defaultTheme(colorScheme);
        final copy = original.copyWith(
          primaryBackground: Colors.red,
          secondaryBackground: Colors.green,
          destructiveBackground: Colors.blue,
          outlineBackground: Colors.yellow,
          ghostBackground: Colors.purple,
          linkBackground: Colors.orange,
        );

        expect(copy.primaryBackground, equals(Colors.red));
        expect(copy.secondaryBackground, equals(Colors.green));
        expect(copy.destructiveBackground, equals(Colors.blue));
        expect(copy.outlineBackground, equals(Colors.yellow));
        expect(copy.ghostBackground, equals(Colors.purple));
        expect(copy.linkBackground, equals(Colors.orange));
      });

      testWidgets('handles all size properties', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final original = ShadcnButtonTheme.defaultTheme(colorScheme);
        const newPadding = EdgeInsets.all(20);
        final copy = original.copyWith(
          smallHeight: 30,
          mediumHeight: 45,
          largeHeight: 60,
          smallPadding: newPadding,
          mediumPadding: newPadding,
          largePadding: newPadding,
          smallFontSize: 10,
          mediumFontSize: 16,
          largeFontSize: 20,
        );

        expect(copy.smallHeight, equals(30));
        expect(copy.mediumHeight, equals(45));
        expect(copy.largeHeight, equals(60));
        expect(copy.smallPadding, equals(newPadding));
        expect(copy.mediumPadding, equals(newPadding));
        expect(copy.largePadding, equals(newPadding));
        expect(copy.smallFontSize, equals(10));
        expect(copy.mediumFontSize, equals(16));
        expect(copy.largeFontSize, equals(20));
      });
    });

    group('lerp', () {
      testWidgets('interpolates colors correctly', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme1 = ShadcnButtonTheme.defaultTheme(colorScheme).copyWith(
          primaryBackground: Colors.red,
        );
        final theme2 = ShadcnButtonTheme.defaultTheme(colorScheme).copyWith(
          primaryBackground: Colors.blue,
        );

        final lerped = theme1.lerp(theme2, 0.5);
        expect(lerped, isA<ShadcnButtonTheme>());
        expect(lerped.primaryBackground, equals(Color.lerp(Colors.red, Colors.blue, 0.5)));
      });

      testWidgets('interpolates EdgeInsets correctly', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        const padding1 = EdgeInsets.all(10);
        const padding2 = EdgeInsets.all(20);
        
        final theme1 = ShadcnButtonTheme.defaultTheme(colorScheme).copyWith(
          mediumPadding: padding1,
        );
        final theme2 = ShadcnButtonTheme.defaultTheme(colorScheme).copyWith(
          mediumPadding: padding2,
        );

        final lerped = theme1.lerp(theme2, 0.5);
        expect(lerped.mediumPadding, equals(EdgeInsets.lerp(padding1, padding2, 0.5)));
      });

      testWidgets('interpolates BorderRadius correctly', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final radius1 = BorderRadius.circular(5);
        final radius2 = BorderRadius.circular(15);
        
        final theme1 = ShadcnButtonTheme.defaultTheme(colorScheme).copyWith(
          borderRadius: radius1,
        );
        final theme2 = ShadcnButtonTheme.defaultTheme(colorScheme).copyWith(
          borderRadius: radius2,
        );

        final lerped = theme1.lerp(theme2, 0.5);
        expect(lerped.borderRadius, equals(BorderRadius.lerp(radius1, radius2, 0.5)));
      });

      testWidgets('handles null other theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);
        final lerped = theme.lerp(null, 0.5);
        
        expect(lerped, equals(theme));
      });

      testWidgets('handles non-ShadcnButtonTheme other', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);
        // Test that when lerp is called with null, it returns this
        final lerped = theme.lerp(null, 0.5);
        
        expect(lerped, equals(theme));
      });
    });

    group('validation', () {
      testWidgets('validates required properties', (WidgetTester tester) async {
        const theme = ShadcnButtonTheme(
          primaryBackground: Colors.blue,
          primaryForeground: Colors.white,
          disabledBackground: Colors.grey,
          disabledForeground: Colors.black,
        );

        expect(theme.validate(), isTrue);
      });

      testWidgets('fails validation with null required properties', (WidgetTester tester) async {
        const theme = ShadcnButtonTheme();
        expect(theme.validate(), isFalse);
      });

      testWidgets('validates size properties', (WidgetTester tester) async {
        const validTheme = ShadcnButtonTheme(
          primaryBackground: Colors.blue,
          primaryForeground: Colors.white,
          disabledBackground: Colors.grey,
          disabledForeground: Colors.black,
          mediumHeight: 40,
        );

        const invalidTheme = ShadcnButtonTheme(
          primaryBackground: Colors.blue,
          primaryForeground: Colors.white,
          disabledBackground: Colors.grey,
          disabledForeground: Colors.black,
          mediumHeight: -10,
        );

        expect(validTheme.validate(), isTrue);
        expect(invalidTheme.validate(), isFalse);
      });

      testWidgets('validates border width', (WidgetTester tester) async {
        const validTheme = ShadcnButtonTheme(
          primaryBackground: Colors.blue,
          primaryForeground: Colors.white,
          disabledBackground: Colors.grey,
          disabledForeground: Colors.black,
          borderWidth: 2,
        );

        const invalidTheme = ShadcnButtonTheme(
          primaryBackground: Colors.blue,
          primaryForeground: Colors.white,
          disabledBackground: Colors.grey,
          disabledForeground: Colors.black,
          borderWidth: -1,
        );

        expect(validTheme.validate(), isTrue);
        expect(invalidTheme.validate(), isFalse);
      });

      testWidgets('throws error when throwOnError is true', (WidgetTester tester) async {
        const theme = ShadcnButtonTheme();
        expect(
          () => theme.validate(throwOnError: true),
          throwsA(isA<FlutterError>()),
        );
      });
    });

    group('equality', () {
      testWidgets('equal themes are equal', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme1 = ShadcnButtonTheme.defaultTheme(colorScheme);
        final theme2 = ShadcnButtonTheme.defaultTheme(colorScheme);

        expect(theme1, equals(theme2));
        expect(theme1.hashCode, equals(theme2.hashCode));
      });

      testWidgets('different themes are not equal', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme1 = ShadcnButtonTheme.defaultTheme(colorScheme);
        final theme2 = ShadcnButtonTheme.defaultTheme(colorScheme).copyWith(
          primaryBackground: Colors.purple,
        );

        expect(theme1, isNot(equals(theme2)));
        expect(theme1.hashCode, isNot(equals(theme2.hashCode)));
      });
    });

    group('toString', () {
      testWidgets('provides meaningful string representation', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnButtonTheme.defaultTheme(colorScheme);
        final string = theme.toString();

        expect(string, contains('ShadcnButtonTheme'));
        expect(string, contains('primaryBackground'));
        expect(string, contains('primaryForeground'));
        expect(string, contains('mediumHeight'));
        expect(string, contains('borderRadius'));
      });
    });
  });

  group('ShadcnButtonVariant', () {
    testWidgets('has all expected variants', (WidgetTester tester) async {
      expect(ShadcnButtonVariant.values, hasLength(6));
      expect(ShadcnButtonVariant.values, contains(ShadcnButtonVariant.primary));
      expect(ShadcnButtonVariant.values, contains(ShadcnButtonVariant.secondary));
      expect(ShadcnButtonVariant.values, contains(ShadcnButtonVariant.destructive));
      expect(ShadcnButtonVariant.values, contains(ShadcnButtonVariant.outline));
      expect(ShadcnButtonVariant.values, contains(ShadcnButtonVariant.ghost));
      expect(ShadcnButtonVariant.values, contains(ShadcnButtonVariant.link));
    });
  });

  group('ShadcnButtonSize', () {
    testWidgets('has all expected sizes', (WidgetTester tester) async {
      expect(ShadcnButtonSize.values, hasLength(3));
      expect(ShadcnButtonSize.values, contains(ShadcnButtonSize.small));
      expect(ShadcnButtonSize.values, contains(ShadcnButtonSize.medium));
      expect(ShadcnButtonSize.values, contains(ShadcnButtonSize.large));
    });
  });
}