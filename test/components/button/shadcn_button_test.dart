import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnButton', () {
    testWidgets('renders with default properties', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnButton(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.text('Test Button'), findsOneWidget);
      expect(find.byType(ShadcnButton), findsOneWidget);
    });

    testWidgets('renders with child instead of text', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnButton(
              child: const Row(
                children: [
                  Icon(Icons.star),
                  Text('Custom Child'),
                ],
              ),
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.text('Custom Child'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);
    });

    testWidgets('handles tap events correctly', (WidgetTester tester) async {
      var tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnButton(
              text: 'Test Button',
              onPressed: () {
                tapped = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ShadcnButton));
      await tester.pump();

      expect(tapped, isTrue);
    });

    testWidgets('is disabled when onPressed is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ShadcnButton(
              text: 'Disabled Button',
              onPressed: null,
            ),
          ),
        ),
      );

      final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
      expect(button.enabled, isFalse);

      // Verify tap does nothing
      await tester.tap(find.byType(ShadcnButton));
      await tester.pump();
      // No exception should be thrown
    });

    testWidgets('shows loading state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnButton(
              text: 'Loading Button',
              onPressed: () {},
              isLoading: true,
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading Button'), findsOneWidget);

      final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
      expect(button.enabled, isFalse);
    });

    group('Variants', () {
      testWidgets('primary variant uses correct theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ThemeData(
          colorScheme: colorScheme,
          extensions: [
            ShadcnButtonTheme.defaultTheme(colorScheme),
          ],
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: theme,
            home: Scaffold(
              body: ShadcnButton.primary(
                text: 'Primary Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.variant, equals(ShadcnButtonVariant.primary));
      });

      testWidgets('secondary variant uses correct theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ThemeData(
          colorScheme: colorScheme,
          extensions: [
            ShadcnButtonTheme.defaultTheme(colorScheme),
          ],
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: theme,
            home: Scaffold(
              body: ShadcnButton.secondary(
                text: 'Secondary Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.variant, equals(ShadcnButtonVariant.secondary));
      });

      testWidgets('destructive variant uses correct theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ThemeData(
          colorScheme: colorScheme,
          extensions: [
            ShadcnButtonTheme.defaultTheme(colorScheme),
          ],
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: theme,
            home: Scaffold(
              body: ShadcnButton.destructive(
                text: 'Destructive Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.variant, equals(ShadcnButtonVariant.destructive));
      });

      testWidgets('outline variant uses correct theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ThemeData(
          colorScheme: colorScheme,
          extensions: [
            ShadcnButtonTheme.defaultTheme(colorScheme),
          ],
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: theme,
            home: Scaffold(
              body: ShadcnButton.outline(
                text: 'Outline Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.variant, equals(ShadcnButtonVariant.outline));
      });

      testWidgets('ghost variant uses correct theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ThemeData(
          colorScheme: colorScheme,
          extensions: [
            ShadcnButtonTheme.defaultTheme(colorScheme),
          ],
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: theme,
            home: Scaffold(
              body: ShadcnButton.ghost(
                text: 'Ghost Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.variant, equals(ShadcnButtonVariant.ghost));
      });

      testWidgets('link variant uses correct theme', (WidgetTester tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ThemeData(
          colorScheme: colorScheme,
          extensions: [
            ShadcnButtonTheme.defaultTheme(colorScheme),
          ],
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: theme,
            home: Scaffold(
              body: ShadcnButton.link(
                text: 'Link Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.variant, equals(ShadcnButtonVariant.link));
      });
    });

    group('Sizes', () {
      testWidgets('small size applies correct dimensions', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Small Button',
                onPressed: () {},
                size: ShadcnButtonSize.small,
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.size, equals(ShadcnButtonSize.small));

        // Check that the container has the expected height
        final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
        expect(container.constraints?.minHeight, equals(ShadcnTokens.buttonHeightSm));
      });

      testWidgets('medium size applies correct dimensions', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Medium Button',
                onPressed: () {},
                size: ShadcnButtonSize.medium,
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.size, equals(ShadcnButtonSize.medium));

        final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
        expect(container.constraints?.minHeight, equals(ShadcnTokens.buttonHeightMd));
      });

      testWidgets('large size applies correct dimensions', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Large Button',
                onPressed: () {},
                size: ShadcnButtonSize.large,
              ),
            ),
          ),
        );

        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.size, equals(ShadcnButtonSize.large));

        final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
        expect(container.constraints?.minHeight, equals(ShadcnTokens.buttonHeightLg));
      });
    });

    group('Icons', () {
      testWidgets('renders with leading icon', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Button with Icon',
                onPressed: () {},
                leading: const Icon(Icons.star),
              ),
            ),
          ),
        );

        expect(find.byIcon(Icons.star), findsOneWidget);
        expect(find.text('Button with Icon'), findsOneWidget);
      });

      testWidgets('renders with trailing icon', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Button with Icon',
                onPressed: () {},
                trailing: const Icon(Icons.arrow_forward),
              ),
            ),
          ),
        );

        expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
        expect(find.text('Button with Icon'), findsOneWidget);
      });

      testWidgets('renders with both leading and trailing icons', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Button with Icons',
                onPressed: () {},
                leading: const Icon(Icons.star),
                trailing: const Icon(Icons.arrow_forward),
              ),
            ),
          ),
        );

        expect(find.byIcon(Icons.star), findsOneWidget);
        expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
        expect(find.text('Button with Icons'), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('has proper semantics by default', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Accessible Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        expect(
          find.bySemanticsLabel('Accessible Button'),
          findsOneWidget,
        );
      });

      testWidgets('supports custom semantic label', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Button Text',
                onPressed: () {},
                semanticLabel: 'Custom Semantic Label',
              ),
            ),
          ),
        );

        expect(
          find.bySemanticsLabel('Custom Semantic Label'),
          findsOneWidget,
        );
      });

      testWidgets('can be excluded from semantics', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Hidden Button',
                onPressed: () {},
                excludeFromSemantics: true,
              ),
            ),
          ),
        );

        expect(
          find.bySemanticsLabel('Hidden Button'),
          findsNothing,
        );
      });

      testWidgets('supports tooltip', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Button with Tooltip',
                onPressed: () {},
                tooltip: 'This is a tooltip',
              ),
            ),
          ),
        );

        expect(find.byType(Tooltip), findsOneWidget);
        final tooltip = tester.widget<Tooltip>(find.byType(Tooltip));
        expect(tooltip.message, equals('This is a tooltip'));
      });
    });

    group('Custom properties', () {
      testWidgets('applies custom width', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Custom Width Button',
                onPressed: () {},
                width: 200,
              ),
            ),
          ),
        );

        // Check that the button applies the custom width
        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.width, equals(200));
      });

      testWidgets('applies custom height', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Custom Height Button',
                onPressed: () {},
                height: 60,
              ),
            ),
          ),
        );

        // Check that the button applies the custom height
        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.height, equals(60));
      });

      testWidgets('expands to fill width when expand is true', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Expanded Button',
                onPressed: () {},
                expand: true,
              ),
            ),
          ),
        );

        // Check that the button expands to fill width
        final button = tester.widget<ShadcnButton>(find.byType(ShadcnButton));
        expect(button.expand, isTrue);
      });

      testWidgets('applies custom padding', (WidgetTester tester) async {
        const customPadding = EdgeInsets.all(20);
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Custom Padding Button',
                onPressed: () {},
                padding: customPadding,
              ),
            ),
          ),
        );

        final padding = tester.widget<Padding>(find.byType(Padding).last);
        expect(padding.padding, equals(customPadding));
      });

      testWidgets('applies custom border radius', (WidgetTester tester) async {
        const customRadius = BorderRadius.all(Radius.circular(20));
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Custom Radius Button',
                onPressed: () {},
                borderRadius: customRadius,
              ),
            ),
          ),
        );

        final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
        expect(container.decoration, isA<BoxDecoration>());
        final decoration = container.decoration as BoxDecoration;
        expect(decoration.borderRadius, equals(customRadius));
      });
    });

    group('Focus behavior', () {
      testWidgets('supports focus node', (WidgetTester tester) async {
        final focusNode = FocusNode();
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Focusable Button',
                onPressed: () {},
                focusNode: focusNode,
              ),
            ),
          ),
        );

        expect(find.byType(Focus), findsOneWidget);
      });

      testWidgets('supports autofocus', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Autofocus Button',
                onPressed: () {},
                autofocus: true,
              ),
            ),
          ),
        );

        final inkWell = tester.widget<InkWell>(find.byType(InkWell));
        expect(inkWell.autofocus, isTrue);
      });
    });

    group('Long press behavior', () {
      testWidgets('handles long press events', (WidgetTester tester) async {
        var longPressed = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Long Press Button',
                onPressed: () {},
                onLongPress: () {
                  longPressed = true;
                },
              ),
            ),
          ),
        );

        await tester.longPress(find.byType(ShadcnButton));
        await tester.pump();

        expect(longPressed, isTrue);
      });
    });

    group('Theme integration', () {
      testWidgets('uses theme extension when provided', (WidgetTester tester) async {
        const customTheme = ShadcnButtonTheme(
          primaryBackground: Colors.purple,
          primaryForeground: Colors.white,
          mediumHeight: 50,
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              extensions: const [customTheme],
            ),
            home: Scaffold(
              body: ShadcnButton(
                text: 'Themed Button',
                onPressed: () {},
                variant: ShadcnButtonVariant.primary,
              ),
            ),
          ),
        );

        // The button should render without errors and apply the theme
        expect(find.byType(ShadcnButton), findsOneWidget);
        expect(find.text('Themed Button'), findsOneWidget);
      });

      testWidgets('falls back to Material theme when no extension', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              colorScheme: const ColorScheme.light(primary: Colors.red),
            ),
            home: Scaffold(
              body: ShadcnButton(
                text: 'Material Themed Button',
                onPressed: () {},
                variant: ShadcnButtonVariant.primary,
              ),
            ),
          ),
        );

        // The button should render without errors using Material theme fallback
        expect(find.byType(ShadcnButton), findsOneWidget);
        expect(find.text('Material Themed Button'), findsOneWidget);
      });
    });

    group('Error handling', () {
      testWidgets('throws assertion when neither text nor child provided', (WidgetTester tester) async {
        expect(
          () => ShadcnButton(
            onPressed: () {},
          ),
          throwsA(isA<AssertionError>()),
        );
      });

      testWidgets('handles null theme gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ShadcnButton(
                text: 'Button without theme',
                onPressed: () {},
              ),
            ),
          ),
        );

        // Should render without throwing errors
        expect(find.byType(ShadcnButton), findsOneWidget);
        expect(find.text('Button without theme'), findsOneWidget);
      });
    });
  });
}