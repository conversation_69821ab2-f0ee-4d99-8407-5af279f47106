import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  group('ShadcnButton Basic Tests', () {
    testWidgets('Button renders with text', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnButton(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.text('Test Button'), findsOneWidget);
      expect(find.byType(ShadcnButton), findsOneWidget);
    });

    testWidgets('Button theme is applied correctly', (WidgetTester tester) async {
      const colorScheme = ColorScheme.light();
      final buttonTheme = ShadcnButtonTheme.defaultTheme(colorScheme);

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: colorScheme,
            extensions: [buttonTheme],
          ),
          home: Scaffold(
            body: Shadcn<PERSON>utton(
              text: 'Themed Button',
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.text('Themed Button'), findsOneWidget);
      expect(find.byType(ShadcnButton), findsOneWidget);
    });

    testWidgets('Button variants work', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ShadcnButton.primary(
                  text: 'Primary',
                  onPressed: () {},
                ),
                ShadcnButton.secondary(
                  text: 'Secondary',
                  onPressed: () {},
                ),
                ShadcnButton.destructive(
                  text: 'Destructive',
                  onPressed: () {},
                ),
                ShadcnButton.outline(
                  text: 'Outline',
                  onPressed: () {},
                ),
                ShadcnButton.ghost(
                  text: 'Ghost',
                  onPressed: () {},
                ),
                ShadcnButton.link(
                  text: 'Link',
                  onPressed: () {},
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Primary'), findsOneWidget);
      expect(find.text('Secondary'), findsOneWidget);
      expect(find.text('Destructive'), findsOneWidget);
      expect(find.text('Outline'), findsOneWidget);
      expect(find.text('Ghost'), findsOneWidget);
      expect(find.text('Link'), findsOneWidget);
    });

    testWidgets('Button sizes work', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                ShadcnButton(
                  text: 'Small',
                  onPressed: () {},
                  size: ShadcnButtonSize.small,
                ),
                ShadcnButton(
                  text: 'Medium',
                  onPressed: () {},
                  size: ShadcnButtonSize.medium,
                ),
                ShadcnButton(
                  text: 'Large',
                  onPressed: () {},
                  size: ShadcnButtonSize.large,
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Small'), findsOneWidget);
      expect(find.text('Medium'), findsOneWidget);
      expect(find.text('Large'), findsOneWidget);
    });

    testWidgets('Button tap works', (WidgetTester tester) async {
      var tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnButton(
              text: 'Tap Me',
              onPressed: () {
                tapped = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ShadcnButton));
      await tester.pump();

      expect(tapped, isTrue);
    });

    testWidgets('Button theme validation works', (WidgetTester tester) async {
      const colorScheme = ColorScheme.light();
      final theme = ShadcnButtonTheme.defaultTheme(colorScheme);

      expect(theme.validate(), isTrue);
      expect(theme.primaryBackground, equals(colorScheme.primary));
      expect(theme.primaryForeground, equals(colorScheme.onPrimary));
      expect(theme.mediumHeight, equals(ShadcnTokens.buttonHeightMd));
    });
  });
}