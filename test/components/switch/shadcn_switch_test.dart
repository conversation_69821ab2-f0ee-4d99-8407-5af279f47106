import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/components/switch/shadcn_switch.dart';
import 'package:shadcn/src/theme/extensions/shadcn_switch_theme.dart';

import '../../test_helpers.dart';

void main() {
  group('ShadcnSwitch', () {
    testWidgets('displays correctly with basic properties', (tester) async {
      bool switchValue = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: switchValue,
            onChanged: (value) => switchValue = value,
            label: 'Test Switch',
          ),
        ),
      );

      expect(find.text('Test Switch'), findsOneWidget);
      expect(find.byType(ShadcnSwitch), findsOneWidget);
    });

    testWidgets('handles toggle interactions correctly', (tester) async {
      bool switchValue = false;
      bool callbackTriggered = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: switchValue,
            onChanged: (value) {
              switchValue = value;
              callbackTriggered = true;
            },
            label: 'Test Switch',
          ),
        ),
      );

      await tester.tap(find.byType(ShadcnSwitch));
      await tester.pump();

      expect(callbackTriggered, isTrue);
      expect(switchValue, isTrue);
    });

    testWidgets('shows on and off states correctly', (tester) async {
      // Test off state
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: false,
            onChanged: (value) {},
            label: 'Off Switch',
          ),
        ),
      );

      expect(find.byType(ShadcnSwitch), findsOneWidget);

      // Test on state
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: true,
            onChanged: (value) {},
            label: 'On Switch',
          ),
        ),
      );

      expect(find.byType(ShadcnSwitch), findsOneWidget);
    });

    testWidgets('displays helper text when provided', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: false,
            onChanged: (value) {},
            label: 'Test Switch',
            helperText: 'This is helper text',
          ),
        ),
      );

      expect(find.text('This is helper text'), findsOneWidget);
    });

    testWidgets('displays error text when provided', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: false,
            onChanged: (value) {},
            label: 'Test Switch',
            errorText: 'This is an error',
          ),
        ),
      );

      expect(find.text('This is an error'), findsOneWidget);
    });

    testWidgets('error text overrides helper text', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: false,
            onChanged: (value) {},
            label: 'Test Switch',
            helperText: 'Helper text',
            errorText: 'Error text',
          ),
        ),
      );

      expect(find.text('Error text'), findsOneWidget);
      expect(find.text('Helper text'), findsNothing);
    });

    testWidgets('respects enabled/disabled state', (tester) async {
      bool switchValue = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: switchValue,
            onChanged: null, // Disabled
            label: 'Disabled Switch',
          ),
        ),
      );

      final initialValue = switchValue;
      await tester.tap(find.byType(ShadcnSwitch));
      await tester.pump();

      // Should not trigger any changes when disabled
      expect(switchValue, equals(initialValue));
    });

    testWidgets('supports different sizes', (tester) async {
      for (final size in ShadcnSwitchSize.values) {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              label: 'Size Test',
              size: size,
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
        await tester.pumpWidget(Container()); // Clear between tests
      }
    });

    testWidgets('supports custom child widget', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: false,
            onChanged: (value) {},
            child: const Row(
              children: [
                Icon(Icons.lightbulb),
                SizedBox(width: 8),
                Text('Custom Child'),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.lightbulb), findsOneWidget);
      expect(find.text('Custom Child'), findsOneWidget);
    });

    testWidgets('label click toggles switch when labelClickable is true', (tester) async {
      bool switchValue = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: switchValue,
            onChanged: (value) => switchValue = value,
            label: 'Clickable Label',
            labelClickable: true,
          ),
        ),
      );

      await tester.tap(find.text('Clickable Label'));
      await tester.pump();

      expect(switchValue, isTrue);
    });

    testWidgets('label click does not toggle when labelClickable is false', (tester) async {
      bool switchValue = false;

      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: switchValue,
            onChanged: (value) => switchValue = value,
            label: 'Non-clickable Label',
            labelClickable: false,
          ),
        ),
      );

      await tester.tap(find.text('Non-clickable Label'));
      await tester.pump();

      expect(switchValue, isFalse);
    });

    testWidgets('has correct semantics', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: true,
            onChanged: (value) {},
            label: 'Semantic Switch',
            semanticLabel: 'Custom semantic label',
          ),
        ),
      );

      expect(find.byType(ShadcnSwitch), findsOneWidget);
    });

    testWidgets('supports tooltip', (tester) async {
      await tester.pumpWidget(
        createTestApp(
          child: ShadcnSwitch(
            value: false,
            onChanged: (value) {},
            label: 'Tooltip Switch',
            tooltip: 'This is a tooltip',
          ),
        ),
      );

      expect(find.byTooltip('This is a tooltip'), findsOneWidget);
    });

    group('Form Field Integration', () {
      testWidgets('works with form validation', (tester) async {
        final formKey = GlobalKey<FormState>();

        await tester.pumpWidget(
          createTestApp(
            child: Form(
              key: formKey,
              child: ShadcnSwitch.formField(
                initialValue: false,
                onChanged: (value) {},
                validator: (value) {
                  if (value != true) {
                    return 'Switch must be enabled';
                  }
                  return null;
                },
                label: 'Agreement Switch',
              ),
            ),
          ),
        );

        // Trigger validation
        expect(formKey.currentState!.validate(), isFalse);
        await tester.pump();

        expect(find.text('Switch must be enabled'), findsOneWidget);
      });

      testWidgets('form field updates correctly', (tester) async {
        final formKey = GlobalKey<FormState>();
        bool? formValue;

        await tester.pumpWidget(
          createTestApp(
            child: Form(
              key: formKey,
              onChanged: () {
                formKey.currentState!.save();
              },
              child: ShadcnSwitch.formField(
                initialValue: false,
                onChanged: (value) => formValue = value,
                onSaved: (value) => formValue = value,
                label: 'Form Switch',
              ),
            ),
          ),
        );

        await tester.tap(find.byType(ShadcnSwitch));
        await tester.pump();

        formKey.currentState!.save();
        expect(formValue, isTrue);
      });
    });

    group('Theme Integration', () {
      testWidgets('applies custom theme correctly', (tester) async {
        const customTheme = ShadcnSwitchTheme(
          onTrackColor: Colors.red,
          onThumbColor: Colors.white,
          trackWidth: 50.0,
        );

        await tester.pumpWidget(
          createTestApp(
            theme: ThemeData(
              extensions: const [customTheme],
            ),
            child: ShadcnSwitch(
              value: true,
              onChanged: (value) {},
              label: 'Custom Theme',
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });

      testWidgets('falls back to default theme when no custom theme provided', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: true,
              onChanged: (value) {},
              label: 'Default Theme',
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('supports focus', (tester) async {
        final focusNode = FocusNode();

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              label: 'Focusable Switch',
              focusNode: focusNode,
            ),
          ),
        );

        focusNode.requestFocus();
        await tester.pump();

        expect(focusNode.hasFocus, isTrue);
      });

      testWidgets('can be excluded from semantics', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              label: 'Hidden from semantics',
              excludeFromSemantics: true,
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });
    });

    group('Haptic Feedback', () {
      testWidgets('triggers haptic feedback when enabled', (tester) async {
        final List<MethodCall> log = <MethodCall>[];
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, (methodCall) async {
          log.add(methodCall);
          return null;
        });

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              label: 'Haptic Switch',
              enableFeedback: true,
            ),
          ),
        );

        await tester.tap(find.byType(ShadcnSwitch));
        await tester.pump();

        expect(
          log,
          contains(isMethodCall('HapticFeedback.vibrate', arguments: 'HapticFeedbackType.lightImpact')),
        );

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, null);
      });

      testWidgets('does not trigger haptic feedback when disabled', (tester) async {
        final List<MethodCall> log = <MethodCall>[];
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, (methodCall) async {
          log.add(methodCall);
          return null;
        });

        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              label: 'No Haptic Switch',
              enableFeedback: false,
            ),
          ),
        );

        await tester.tap(find.byType(ShadcnSwitch));
        await tester.pump();

        expect(
          log,
          isNot(contains(isMethodCall('HapticFeedback.vibrate', arguments: 'HapticFeedbackType.lightImpact'))),
        );

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(SystemChannels.platform, null);
      });
    });

    group('Custom Decorations', () {
      testWidgets('supports custom thumb decoration', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: true,
              onChanged: (value) {},
              label: 'Custom Thumb',
              thumbDecoration: const BoxDecoration(
                color: Colors.purple,
                shape: BoxShape.rectangle,
              ),
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });

      testWidgets('supports custom track decoration', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: true,
              onChanged: (value) {},
              label: 'Custom Track',
              trackDecoration: const BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });
    });

    group('Animation and Duration', () {
      testWidgets('supports custom animation duration', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              label: 'Custom Animation',
              animationDuration: const Duration(milliseconds: 500),
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });

      testWidgets('animates between states', (tester) async {
        bool switchValue = false;

        await tester.pumpWidget(
          createTestApp(
            child: StatefulBuilder(
              builder: (context, setState) {
                return ShadcnSwitch(
                  value: switchValue,
                  onChanged: (value) {
                    setState(() {
                      switchValue = value;
                    });
                  },
                  label: 'Animated Switch',
                );
              },
            ),
          ),
        );

        // Toggle switch
        await tester.tap(find.byType(ShadcnSwitch));
        await tester.pump();

        // Verify switch toggled
        expect(switchValue, isTrue);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles null label and child', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              // No label or child provided
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });

      testWidgets('handles custom padding', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              label: 'Padded Switch',
              padding: const EdgeInsets.all(20.0),
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });

      testWidgets('handles custom mouse cursor', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch(
              value: false,
              onChanged: (value) {},
              label: 'Custom Cursor',
              mouseCursor: SystemMouseCursors.help,
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });

      testWidgets('preserves initial value in form field', (tester) async {
        await tester.pumpWidget(
          createTestApp(
            child: ShadcnSwitch.formField(
              initialValue: true,
              onChanged: (value) {},
              label: 'Initial True',
            ),
          ),
        );

        expect(find.byType(ShadcnSwitch), findsOneWidget);
      });
    });

    group('Toggle Behavior', () {
      testWidgets('toggles from false to true', (tester) async {
        bool switchValue = false;

        await tester.pumpWidget(
          createTestApp(
            child: StatefulBuilder(
              builder: (context, setState) {
                return ShadcnSwitch(
                  value: switchValue,
                  onChanged: (value) {
                    setState(() {
                      switchValue = value;
                    });
                  },
                  label: 'Toggle Test',
                );
              },
            ),
          ),
        );

        expect(switchValue, isFalse);
        
        await tester.tap(find.byType(ShadcnSwitch));
        await tester.pump();

        expect(switchValue, isTrue);
      });

      testWidgets('toggles from true to false', (tester) async {
        bool switchValue = true;

        await tester.pumpWidget(
          createTestApp(
            child: StatefulBuilder(
              builder: (context, setState) {
                return ShadcnSwitch(
                  value: switchValue,
                  onChanged: (value) {
                    setState(() {
                      switchValue = value;
                    });
                  },
                  label: 'Toggle Test',
                );
              },
            ),
          ),
        );

        expect(switchValue, isTrue);
        
        await tester.tap(find.byType(ShadcnSwitch));
        await tester.pump();

        expect(switchValue, isFalse);
      });
    });
  });
}