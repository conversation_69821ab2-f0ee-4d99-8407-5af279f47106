import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/theme/extensions/shadcn_switch_theme.dart';

void main() {
  group('ShadcnSwitchTheme', () {
    group('defaultTheme', () {
      testWidgets('creates theme with light color scheme', (tester) async {
        const lightColorScheme = ColorScheme.light();
        final theme = ShadcnSwitchTheme.defaultTheme(lightColorScheme);

        expect(theme.onTrackColor, equals(lightColorScheme.primary));
        expect(theme.onThumbColor, equals(lightColorScheme.onPrimary));
        expect(theme.onBorderColor, equals(lightColorScheme.primary));
        expect(theme.trackWidth, equals(44.0));
        expect(theme.trackHeight, equals(24.0));
        expect(theme.thumbSize, equals(20.0));
      });

      testWidgets('creates theme with dark color scheme', (tester) async {
        const darkColorScheme = ColorScheme.dark();
        final theme = ShadcnSwitchTheme.defaultTheme(darkColorScheme);

        expect(theme.onTrackColor, equals(darkColorScheme.primary));
        expect(theme.onThumbColor, equals(darkColorScheme.onPrimary));
        expect(theme.onBorderColor, equals(darkColorScheme.primary));
        expect(theme.trackWidth, equals(44.0));
        expect(theme.trackHeight, equals(24.0));
        expect(theme.thumbSize, equals(20.0));
      });

      testWidgets('has proper default values for different sizes', (tester) async {
        const colorScheme = ColorScheme.light();
        final theme = ShadcnSwitchTheme.defaultTheme(colorScheme);

        // Medium size (default)
        expect(theme.trackWidth, equals(44.0));
        expect(theme.trackHeight, equals(24.0));
        expect(theme.thumbSize, equals(20.0));
        
        // Small size
        expect(theme.smallTrackWidth, equals(36.0));
        expect(theme.smallTrackHeight, equals(20.0));
        expect(theme.smallThumbSize, equals(16.0));
        
        // Large size
        expect(theme.largeTrackWidth, equals(52.0));
        expect(theme.largeTrackHeight, equals(28.0));
        expect(theme.largeThumbSize, equals(24.0));
        
        expect(theme.borderWidth, equals(1.0));
        expect(theme.animationDuration, equals(const Duration(milliseconds: 150)));
      });

      testWidgets('handles light vs dark theme differences', (tester) async {
        const lightColorScheme = ColorScheme.light();
        const darkColorScheme = ColorScheme.dark();
        
        final lightTheme = ShadcnSwitchTheme.defaultTheme(lightColorScheme);
        final darkTheme = ShadcnSwitchTheme.defaultTheme(darkColorScheme);

        // Both should have shadows in light mode but not in dark mode
        expect(lightTheme.thumbShadow, isNotNull);
        expect(darkTheme.thumbShadow, isNull);
        
        expect(lightTheme.thumbElevation, equals(1));
        expect(darkTheme.thumbElevation, equals(0));
      });
    });

    group('copyWith', () {
      test('creates new instance with updated values', () {
        const original = ShadcnSwitchTheme(
          onTrackColor: Colors.red,
          trackWidth: 44.0,
          thumbSize: 20.0,
        );

        final updated = original.copyWith(
          onTrackColor: Colors.blue,
          onThumbColor: Colors.white,
          trackHeight: 28.0,
        );

        expect(updated.onTrackColor, equals(Colors.blue));
        expect(updated.onThumbColor, equals(Colors.white));
        expect(updated.trackHeight, equals(28.0));
        expect(updated.trackWidth, equals(44.0)); // Unchanged
        expect(updated.thumbSize, equals(20.0)); // Unchanged
      });

      test('keeps original values when null provided', () {
        const original = ShadcnSwitchTheme(
          onTrackColor: Colors.red,
          onThumbColor: Colors.white,
          trackWidth: 44.0,
          trackHeight: 24.0,
          thumbSize: 20.0,
        );

        final updated = original.copyWith();

        expect(updated.onTrackColor, equals(Colors.red));
        expect(updated.onThumbColor, equals(Colors.white));
        expect(updated.trackWidth, equals(44.0));
        expect(updated.trackHeight, equals(24.0));
        expect(updated.thumbSize, equals(20.0));
      });
    });

    group('lerp', () {
      test('interpolates between two themes correctly', () {
        const themeA = ShadcnSwitchTheme(
          onTrackColor: Colors.red,
          trackWidth: 40.0,
          trackHeight: 20.0,
          thumbSize: 16.0,
        );

        const themeB = ShadcnSwitchTheme(
          onTrackColor: Colors.blue,
          trackWidth: 48.0,
          trackHeight: 28.0,
          thumbSize: 24.0,
        );

        final lerped = themeA.lerp(themeB, 0.5);

        expect(lerped.trackWidth, equals(44.0)); // Midpoint between 40 and 48
        expect(lerped.trackHeight, equals(24.0)); // Midpoint between 20 and 28
        expect(lerped.thumbSize, equals(20.0)); // Midpoint between 16 and 24
        // Color lerping is more complex, but we can verify it's not the original colors
        expect(lerped.onTrackColor, isNot(equals(Colors.red)));
        expect(lerped.onTrackColor, isNot(equals(Colors.blue)));
      });

      test('returns original theme when other is null', () {
        const theme = ShadcnSwitchTheme(
          onTrackColor: Colors.red,
          trackWidth: 44.0,
        );

        final lerped = theme.lerp(null, 0.5);

        expect(lerped.onTrackColor, equals(Colors.red));
        expect(lerped.trackWidth, equals(44.0));
      });

      test('handles t = 0.0 correctly', () {
        const themeA = ShadcnSwitchTheme(
          onTrackColor: Colors.red,
          trackWidth: 40.0,
        );

        const themeB = ShadcnSwitchTheme(
          onTrackColor: Colors.blue,
          trackWidth: 48.0,
        );

        final lerped = themeA.lerp(themeB, 0.0);

        expect(lerped.onTrackColor, equals(Colors.red));
        expect(lerped.trackWidth, equals(40.0));
      });

      test('handles t = 1.0 correctly', () {
        const themeA = ShadcnSwitchTheme(
          onTrackColor: Colors.red,
          trackWidth: 40.0,
        );

        const themeB = ShadcnSwitchTheme(
          onTrackColor: Colors.blue,
          trackWidth: 48.0,
        );

        final lerped = themeA.lerp(themeB, 1.0);

        expect(lerped.onTrackColor, equals(Colors.blue));
        expect(lerped.trackWidth, equals(48.0));
      });
    });

    group('validate', () {
      test('returns true for valid theme', () {
        const theme = ShadcnSwitchTheme(
          onTrackColor: Colors.blue,
          onThumbColor: Colors.white,
          trackWidth: 44.0,
          trackHeight: 24.0,
          thumbSize: 20.0,
          borderWidth: 1.0,
        );

        expect(theme.validate(), isTrue);
      });

      test('validates track width constraints', () {
        const invalidTheme = ShadcnSwitchTheme(
          trackWidth: -5.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('validates track height constraints', () {
        const invalidTheme = ShadcnSwitchTheme(
          trackHeight: -5.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('validates thumb size constraints', () {
        const invalidTheme = ShadcnSwitchTheme(
          thumbSize: -5.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('validates border width constraints', () {
        const invalidTheme = ShadcnSwitchTheme(
          borderWidth: -1.0,
        );

        expect(invalidTheme.validate(), isFalse);
      });

      test('throws exception when throwOnError is true', () {
        const invalidTheme = ShadcnSwitchTheme(
          trackWidth: -5.0,
        );

        expect(() => invalidTheme.validate(throwOnError: true), throwsA(isA<ThemeException>()));
      });
    });

    group('size resolution methods', () {
      test('resolveTrackWidthForVariant returns correct values', () {
        const theme = ShadcnSwitchTheme(
          smallTrackWidth: 36.0,
          trackWidth: 44.0,
          largeTrackWidth: 52.0,
        );

        expect(theme.resolveTrackWidthForVariant(ShadcnSwitchSize.small), equals(36.0));
        expect(theme.resolveTrackWidthForVariant(ShadcnSwitchSize.medium), equals(44.0));
        expect(theme.resolveTrackWidthForVariant(ShadcnSwitchSize.large), equals(52.0));
      });

      test('resolveTrackHeightForVariant returns correct values', () {
        const theme = ShadcnSwitchTheme(
          smallTrackHeight: 20.0,
          trackHeight: 24.0,
          largeTrackHeight: 28.0,
        );

        expect(theme.resolveTrackHeightForVariant(ShadcnSwitchSize.small), equals(20.0));
        expect(theme.resolveTrackHeightForVariant(ShadcnSwitchSize.medium), equals(24.0));
        expect(theme.resolveTrackHeightForVariant(ShadcnSwitchSize.large), equals(28.0));
      });

      test('resolveThumbSizeForVariant returns correct values', () {
        const theme = ShadcnSwitchTheme(
          smallThumbSize: 16.0,
          thumbSize: 20.0,
          largeThumbSize: 24.0,
        );

        expect(theme.resolveThumbSizeForVariant(ShadcnSwitchSize.small), equals(16.0));
        expect(theme.resolveThumbSizeForVariant(ShadcnSwitchSize.medium), equals(20.0));
        expect(theme.resolveThumbSizeForVariant(ShadcnSwitchSize.large), equals(24.0));
      });

      test('falls back to defaults when sizes not specified', () {
        const theme = ShadcnSwitchTheme();

        expect(theme.resolveTrackWidthForVariant(ShadcnSwitchSize.small), equals(36.0));
        expect(theme.resolveTrackWidthForVariant(ShadcnSwitchSize.medium), equals(44.0));
        expect(theme.resolveTrackWidthForVariant(ShadcnSwitchSize.large), equals(52.0));

        expect(theme.resolveTrackHeightForVariant(ShadcnSwitchSize.small), equals(20.0));
        expect(theme.resolveTrackHeightForVariant(ShadcnSwitchSize.medium), equals(24.0));
        expect(theme.resolveTrackHeightForVariant(ShadcnSwitchSize.large), equals(28.0));

        expect(theme.resolveThumbSizeForVariant(ShadcnSwitchSize.small), equals(16.0));
        expect(theme.resolveThumbSizeForVariant(ShadcnSwitchSize.medium), equals(20.0));
        expect(theme.resolveThumbSizeForVariant(ShadcnSwitchSize.large), equals(24.0));
      });
    });

    group('theme extension behavior', () {
      testWidgets('integrates with Material theme system', (tester) async {
        const customTheme = ShadcnSwitchTheme(
          onTrackColor: Colors.purple,
          trackWidth: 50.0,
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              extensions: const [customTheme],
            ),
            home: Builder(
              builder: (context) {
                final resolvedTheme = Theme.of(context).extension<ShadcnSwitchTheme>();
                expect(resolvedTheme?.onTrackColor, equals(Colors.purple));
                expect(resolvedTheme?.trackWidth, equals(50.0));
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('edge cases', () {
      test('handles null values in lerp correctly', () {
        const theme = ShadcnSwitchTheme(
          onTrackColor: Colors.red,
        );

        final lerped = theme.lerp(theme, 0.5);
        expect(lerped.onTrackColor, equals(Colors.red));
      });

      test('copyWith handles all nullable properties', () {
        const theme = ShadcnSwitchTheme();
        
        final updated = theme.copyWith(
          onTrackColor: Colors.red,
          onThumbColor: Colors.white,
          onBorderColor: Colors.blue,
          offTrackColor: Colors.grey,
          offThumbColor: Colors.black,
          offBorderColor: Colors.darkGrey,
          hoverOverlay: Colors.purple,
          pressedOverlay: Colors.pink,
          focusedOverlay: Colors.teal,
          trackWidth: 50.0,
          trackHeight: 30.0,
          smallTrackWidth: 40.0,
          smallTrackHeight: 25.0,
          largeTrackWidth: 60.0,
          largeTrackHeight: 35.0,
          thumbSize: 25.0,
          smallThumbSize: 20.0,
          largeThumbSize: 30.0,
          borderWidth: 2.0,
          animationDuration: const Duration(milliseconds: 200),
        );

        expect(updated.onTrackColor, equals(Colors.red));
        expect(updated.onThumbColor, equals(Colors.white));
        expect(updated.trackWidth, equals(50.0));
        expect(updated.borderWidth, equals(2.0));
      });

      test('handles shadow properties correctly', () {
        const shadows = [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ];

        const theme = ShadcnSwitchTheme(
          thumbShadow: shadows,
          thumbElevation: 4.0,
        );

        final updated = theme.copyWith(
          thumbElevation: 8.0,
        );

        expect(updated.thumbShadow, equals(shadows)); // Unchanged
        expect(updated.thumbElevation, equals(8.0)); // Changed
      });

      test('handles border radius correctly', () {
        const borderRadius = BorderRadius.all(Radius.circular(15));
        
        const theme = ShadcnSwitchTheme(
          trackBorderRadius: borderRadius,
        );

        final updated = theme.copyWith();
        expect(updated.trackBorderRadius, equals(borderRadius));
      });
    });
  });
}