import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/utils/theme_resolver.dart';

void main() {
  group('ShadcnThemeResolver', () {
    late ThemeData lightTheme;
    
    setUp(() {
      lightTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
      );
    });

    group('resolveThemeExtension', () {
      testWidgets('returns extension when available', (tester) async {
        final testExtension = TestThemeExtension(color: Colors.red);
        final themeWithExtension = lightTheme.copyWith(
          extensions: [testExtension],
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: themeWithExtension,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveThemeExtension<TestThemeExtension>(
                  context,
                  (colorScheme) => TestThemeExtension.defaultTheme(colorScheme),
                );
                
                expect(resolved, equals(testExtension));
                expect(resolved.color, equals(Colors.red));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('falls back to default factory when extension not available', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveThemeExtension<TestThemeExtension>(
                  context,
                  (colorScheme) => TestThemeExtension.defaultTheme(colorScheme),
                );
                
                expect(resolved, isA<TestThemeExtension>());
                expect(resolved.color, equals(lightTheme.colorScheme.primary));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('handles theme resolution errors with emergency fallback', (tester) async {
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final resolved = ShadcnThemeResolver.resolveThemeExtension<TestThemeExtension>(
                context,
                (colorScheme) => TestThemeExtension.defaultTheme(colorScheme),
              );
              
              expect(resolved, isA<TestThemeExtension>());
              // Should fall back to blue seed color
              expect(resolved.color, equals(Colors.blue));
              return const SizedBox.shrink();
            },
          ),
        );
      });
    });

    group('resolveColor', () {
      testWidgets('returns custom color when provided', (tester) async {
        const customColor = Colors.red;
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveColor(
                  context,
                  customColor,
                  (theme) => theme.colorScheme.primary,
                );
                
                expect(resolved, equals(customColor));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('falls back to material color when custom is null', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveColor(
                  context,
                  null,
                  (theme) => theme.colorScheme.secondary,
                );
                
                expect(resolved, equals(lightTheme.colorScheme.secondary));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('falls back to hardcoded color on error', (tester) async {
        const fallbackColor = Colors.green;
        
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final resolved = ShadcnThemeResolver.resolveColor(
                context,
                null,
                (theme) => throw Exception('Test error'),
                fallbackColor,
              );
              
              expect(resolved, equals(fallbackColor));
              return const SizedBox.shrink();
            },
          ),
        );
      });
    });

    group('resolveTextStyle', () {
      testWidgets('merges custom style with material base', (tester) async {
        const customStyle = TextStyle(fontSize: 20, color: Colors.red);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveTextStyle(
                  context,
                  customStyle,
                  (textTheme) => textTheme.bodyMedium!,
                );
                
                expect(resolved.fontSize, equals(20));
                expect(resolved.color, equals(Colors.red));
                // Should inherit other properties from base style
                expect(resolved.fontFamily, equals(lightTheme.textTheme.bodyMedium!.fontFamily));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('returns material style when custom is null', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveTextStyle(
                  context,
                  null,
                  (textTheme) => textTheme.headlineLarge!,
                );
                
                expect(resolved, equals(lightTheme.textTheme.headlineLarge));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('falls back to provided fallback on error', (tester) async {
        const fallbackStyle = TextStyle(fontSize: 16, color: Colors.black);
        
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final resolved = ShadcnThemeResolver.resolveTextStyle(
                context,
                null,
                (textTheme) => throw Exception('Test error'),
                fallbackStyle,
              );
              
              expect(resolved, equals(fallbackStyle));
              return const SizedBox.shrink();
            },
          ),
        );
      });
    });

    group('resolveSpacing', () {
      testWidgets('returns custom spacing when provided', (tester) async {
        const customSpacing = EdgeInsets.all(20);
        const defaultSpacing = EdgeInsets.all(10);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveSpacing(
                  context,
                  customSpacing,
                  defaultSpacing,
                );
                
                expect(resolved, equals(customSpacing));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('applies visual density to default spacing', (tester) async {
        const defaultSpacing = EdgeInsets.symmetric(horizontal: 16, vertical: 8);
        final denseTheme = lightTheme.copyWith(
          visualDensity: VisualDensity.compact,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: denseTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveSpacing(
                  context,
                  null,
                  defaultSpacing,
                );
                
                final expectedAdjustment = VisualDensity.compact.baseSizeAdjustment;
                final expected = EdgeInsets.fromLTRB(
                  defaultSpacing.left + expectedAdjustment.dx,
                  defaultSpacing.top + expectedAdjustment.dy,
                  defaultSpacing.right + expectedAdjustment.dx,
                  defaultSpacing.bottom + expectedAdjustment.dy,
                );
                
                expect(resolved, equals(expected));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('falls back to default spacing on error', (tester) async {
        const defaultSpacing = EdgeInsets.all(10);
        
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final resolved = ShadcnThemeResolver.resolveSpacing(
                context,
                null,
                defaultSpacing,
              );
              
              expect(resolved, equals(defaultSpacing));
              return const SizedBox.shrink();
            },
          ),
        );
      });
    });

    group('resolveDouble', () {
      testWidgets('returns custom value when provided', (tester) async {
        const customValue = 50.0;
        const defaultValue = 40.0;
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveDouble(
                  context,
                  customValue,
                  defaultValue,
                );
                
                expect(resolved, equals(customValue));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('applies vertical density adjustment to default value', (tester) async {
        const defaultValue = 40.0;
        final denseTheme = lightTheme.copyWith(
          visualDensity: VisualDensity.compact,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: denseTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveDouble(
                  context,
                  null,
                  defaultValue,
                );
                
                final expectedAdjustment = VisualDensity.compact.baseSizeAdjustment.dy;
                final expected = (defaultValue + expectedAdjustment).clamp(0.0, double.infinity);
                
                expect(resolved, equals(expected));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('skips density adjustment when disabled', (tester) async {
        const defaultValue = 40.0;
        final denseTheme = lightTheme.copyWith(
          visualDensity: VisualDensity.compact,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: denseTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveDouble(
                  context,
                  null,
                  defaultValue,
                  applyVerticalDensity: false,
                );
                
                expect(resolved, equals(defaultValue));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('clamps negative values to zero', (tester) async {
        const defaultValue = 5.0;
        final extremelyDenseTheme = lightTheme.copyWith(
          visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: extremelyDenseTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveDouble(
                  context,
                  null,
                  defaultValue,
                );
                
                expect(resolved, equals(0.0));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });
    });

    group('validation methods', () {
      testWidgets('validateThemeProperty returns true for non-null values', (tester) async {
        const value = 'test';
        final result = ShadcnThemeResolver.validateThemeProperty(
          value,
          'testProperty',
        );
        
        expect(result, isTrue);
      });

      testWidgets('validateThemeProperty returns false for null values', (tester) async {
        final result = ShadcnThemeResolver.validateThemeProperty(
          null,
          'testProperty',
        );
        
        expect(result, isFalse);
      });

      testWidgets('validateThemeProperty throws when throwOnError is true', (tester) async {
        expect(
          () => ShadcnThemeResolver.validateThemeProperty(
            null,
            'testProperty',
            throwOnError: true,
          ),
          throwsA(isA<ThemeException>()),
        );
      });

      testWidgets('validateColorScheme returns true for valid color scheme', (tester) async {
        final result = ShadcnThemeResolver.validateColorScheme(
          lightTheme.colorScheme,
        );
        
        expect(result, isTrue);
      });

      testWidgets('validateTextTheme returns true for valid text theme', (tester) async {
        final result = ShadcnThemeResolver.validateTextTheme(
          lightTheme.textTheme,
        );
        
        expect(result, isTrue);
      });

      testWidgets('validateCompleteTheme returns true for valid theme', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final result = ShadcnThemeResolver.validateCompleteTheme(context);
                expect(result, isTrue);
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('validateThemeExtension returns false for null extension', (tester) async {
        final result = ShadcnThemeResolver.validateThemeExtension<TestThemeExtension>(
          null,
          ['color'],
        );
        
        expect(result, isFalse);
      });

      testWidgets('validateThemeExtension returns true for non-null extension', (tester) async {
        final extension = TestThemeExtension(color: Colors.blue);
        final result = ShadcnThemeResolver.validateThemeExtension<TestThemeExtension>(
          extension,
          ['color'],
        );
        
        expect(result, isTrue);
      });
    });

    group('safe getters', () {
      testWidgets('getColorScheme returns theme color scheme', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final colorScheme = ShadcnThemeResolver.getColorScheme(context);
                expect(colorScheme, equals(lightTheme.colorScheme));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('getTextTheme returns theme text theme', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final textTheme = ShadcnThemeResolver.getTextTheme(context);
                expect(textTheme, equals(lightTheme.textTheme));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('getColorScheme falls back on error', (tester) async {
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final colorScheme = ShadcnThemeResolver.getColorScheme(context);
              expect(colorScheme, isA<ColorScheme>());
              return const SizedBox.shrink();
            },
          ),
        );
      });

      testWidgets('getTextTheme falls back on error', (tester) async {
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final textTheme = ShadcnThemeResolver.getTextTheme(context);
              expect(textTheme, isA<TextTheme>());
              return const SizedBox.shrink();
            },
          ),
        );
      });
    });

    group('edge cases and error handling', () {
      testWidgets('handles missing theme ancestor gracefully', (tester) async {
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              // This should not throw, but use fallback values
              final resolved = ShadcnThemeResolver.resolveThemeExtension<TestThemeExtension>(
                context,
                (colorScheme) => TestThemeExtension.defaultTheme(colorScheme),
              );
              
              expect(resolved, isA<TestThemeExtension>());
              return const SizedBox.shrink();
            },
          ),
        );
      });

      testWidgets('theme validation works with minimal themes', (tester) async {
        final minimalTheme = ThemeData.light();
        
        await tester.pumpWidget(
          MaterialApp(
            theme: minimalTheme,
            home: Builder(
              builder: (context) {
                final result = ShadcnThemeResolver.validateCompleteTheme(context);
                expect(result, isTrue);
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('handles null color scheme gracefully', (tester) async {
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final colorScheme = ShadcnThemeResolver.getColorScheme(context);
              expect(colorScheme, isA<ColorScheme>());
              expect(colorScheme.primary, isA<Color>());
              return const SizedBox.shrink();
            },
          ),
        );
      });

      testWidgets('handles extreme visual density values in resolveDouble', (tester) async {
        final extremeDensityTheme = lightTheme.copyWith(
          visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: extremeDensityTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveDouble(
                  context,
                  null,
                  5.0, // Small value that might go negative with extreme density
                );
                
                // Should be clamped to 0, not negative
                expect(resolved, equals(0.0));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('validateColorScheme handles edge cases', (tester) async {
        // Create a minimal but valid color scheme
        final minimalColorScheme = ColorScheme.fromSeed(seedColor: Colors.blue);
        
        final result = ShadcnThemeResolver.validateColorScheme(
          minimalColorScheme,
          throwOnError: false,
        );
        
        expect(result, isTrue);
      });

      testWidgets('validateTextTheme handles edge cases', (tester) async {
        final minimalTextTheme = ThemeData().textTheme;
        
        final result = ShadcnThemeResolver.validateTextTheme(
          minimalTextTheme,
          throwOnError: false,
        );
        
        expect(result, isTrue);
      });

      testWidgets('validateThemeProperty handles different data types', (tester) async {
        // Test with different data types
        expect(
          ShadcnThemeResolver.validateThemeProperty<String>('test', 'stringProp'),
          isTrue,
        );
        expect(
          ShadcnThemeResolver.validateThemeProperty<int>(42, 'intProp'),
          isTrue,
        );
        expect(
          ShadcnThemeResolver.validateThemeProperty<double>(3.14, 'doubleProp'),
          isTrue,
        );
        expect(
          ShadcnThemeResolver.validateThemeProperty<bool>(true, 'boolProp'),
          isTrue,
        );
        expect(
          ShadcnThemeResolver.validateThemeProperty<Color>(Colors.red, 'colorProp'),
          isTrue,
        );
      });

      testWidgets('error messages are properly formatted in ThemeException', (tester) async {
        expect(
          () => ShadcnThemeResolver.validateThemeProperty(
            null,
            'testProp',
            throwOnError: true,
          ),
          throwsA(predicate<ThemeException>((e) => 
            e.message.contains('testProp') && 
            e.toString().startsWith('ThemeException:')
          )),
        );
      });

      testWidgets('resolveSpacing handles negative density adjustments', (tester) async {
        const smallSpacing = EdgeInsets.all(2.0);
        final compactTheme = lightTheme.copyWith(
          visualDensity: const VisualDensity(horizontal: -2, vertical: -2),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: compactTheme,
            home: Builder(
              builder: (context) {
                final resolved = ShadcnThemeResolver.resolveSpacing(
                  context,
                  null,
                  smallSpacing,
                );
                
                // All spacing values should remain positive
                expect(resolved.left, greaterThanOrEqualTo(0));
                expect(resolved.top, greaterThanOrEqualTo(0));
                expect(resolved.right, greaterThanOrEqualTo(0));
                expect(resolved.bottom, greaterThanOrEqualTo(0));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('validates complete theme with throwOnError', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                // Should not throw with valid theme
                expect(
                  () => ShadcnThemeResolver.validateCompleteTheme(
                    context, 
                    throwOnError: true,
                  ),
                  returnsNormally,
                );
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('handles concurrent theme resolutions', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                // Simulate multiple concurrent theme resolutions
                final futures = List.generate(10, (index) => Future(() {
                  return ShadcnThemeResolver.resolveThemeExtension<TestThemeExtension>(
                    context,
                    (colorScheme) => TestThemeExtension.defaultTheme(colorScheme),
                  );
                }));
                
                return FutureBuilder<List<TestThemeExtension>>(
                  future: Future.wait(futures),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      expect(snapshot.data!.length, equals(10));
                      for (final extension in snapshot.data!) {
                        expect(extension, isA<TestThemeExtension>());
                      }
                    }
                    return const SizedBox.shrink();
                  },
                );
              },
            ),
          ),
        );
      });
    });
  });

  group('ThemeException', () {
    test('creates exception with message', () {
      const message = 'Test error message';
      const exception = ThemeException(message);
      
      expect(exception.message, equals(message));
      expect(exception.toString(), equals('ThemeException: $message'));
    });
  });
}

/// Test theme extension for testing purposes.
class TestThemeExtension extends ThemeExtension<TestThemeExtension> {
  final Color color;

  const TestThemeExtension({
    required this.color,
  });

  static TestThemeExtension defaultTheme(ColorScheme colorScheme) {
    return TestThemeExtension(color: colorScheme.primary);
  }

  @override
  TestThemeExtension copyWith({
    Color? color,
  }) {
    return TestThemeExtension(
      color: color ?? this.color,
    );
  }

  @override
  TestThemeExtension lerp(TestThemeExtension? other, double t) {
    if (other is! TestThemeExtension) return this;
    return TestThemeExtension(
      color: Color.lerp(color, other.color, t)!,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TestThemeExtension && other.color == color;
  }
  
  @override
  int get hashCode => color.hashCode;
}