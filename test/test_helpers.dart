import 'package:flutter/material.dart';

/// Creates a test app wrapper with proper MaterialApp and theme setup
Widget createTestApp({
  required Widget child,
  ThemeData? theme,
  ThemeData? darkTheme,
  ThemeMode themeMode = ThemeMode.light,
}) {
  return MaterialApp(
    theme: theme ?? ThemeData.light(),
    darkTheme: darkTheme,
    themeMode: themeMode,
    home: Scaffold(
      body: child,
    ),
  );
}

/// Creates a test theme with extensions for testing
ThemeData createTestTheme({
  Brightness brightness = Brightness.light,
  List<ThemeExtension> extensions = const [],
}) {
  final baseTheme = brightness == Brightness.light 
    ? ThemeData.light() 
    : ThemeData.dark();

  return baseTheme.copyWith(
    extensions: extensions,
  );
}

/// Common test color scheme for consistent testing
const testColorScheme = ColorScheme.light(
  primary: Color(0xFF0066FF),
  secondary: Color(0xFF6C757D),
  surface: Color(0xFFFFFBFE),
  error: Color(0xFFDC3545),
);

const testDarkColorScheme = ColorScheme.dark(
  primary: Color(0xFF0066FF),
  secondary: Color(0xFF6C757D),
  surface: Color(0xFF1C1B1F),
  error: Color(0xFFDC3545),
);