import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Import only the accordion components directly to avoid issues with other components
import 'package:shadcn/src/components/accordion/shadcn_accordion.dart';
import 'package:shadcn/src/theme/extensions/shadcn_accordion_theme.dart';

void main() {
  group('ShadcnAccordion Simple Tests', () {
    testWidgets('creates accordion without errors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnAccordion(
              items: [
                ShadcnAccordionItem(
                  value: 'test',
                  header: Text('Test Header'),
                  content: Text('Test Content'),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(ShadcnAccordion), findsOneWidget);
      expect(find.text('Test Header'), findsOneWidget);
      // Content should be hidden initially since no expandedValue is set
      expect(find.text('Test Content'), findsNothing);
    });

    testWidgets('expands content when expandedValue is set', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ShadcnAccordion(
              expandedValue: 'test',
              items: [
                ShadcnAccordionItem(
                  value: 'test',
                  header: Text('Test Header'),
                  content: Text('Test Content'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(ShadcnAccordion), findsOneWidget);
      expect(find.text('Test Header'), findsOneWidget);
      expect(find.text('Test Content'), findsOneWidget);
    });

    test('ShadcnAccordionItem has correct properties', () {
      const item = ShadcnAccordionItem(
        value: 'test-item',
        header: Text('Header'),
        content: Text('Content'),
      );

      expect(item.value, 'test-item');
      expect(item.header, isA<Text>());
      expect(item.content, isA<Text>());
      expect(item.disabled, false);
      expect(item.triggerIcon, isNull);
      expect(item.expandedTriggerIcon, isNull);
      expect(item.semanticLabel, isNull);
    });
  });
  
  group('ShadcnAccordionTheme Tests', () {
    late ColorScheme lightColorScheme;

    setUp(() {
      lightColorScheme = ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.light);
    });

    test('creates valid default theme', () {
      final theme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
      
      expect(theme.headerBackground, lightColorScheme.surface);
      expect(theme.headerForeground, lightColorScheme.onSurface);
      expect(theme.contentBackground, lightColorScheme.surface);
      expect(theme.borderColor, lightColorScheme.outline);
      expect(theme.borderWidth, 1.0);
      expect(theme.triggerIconSize, 20.0);
      expect(theme.animationDuration, const Duration(milliseconds: 250));
      expect(theme.animationCurve, Curves.easeInOut);
    });

    test('copyWith works correctly', () {
      final originalTheme = ShadcnAccordionTheme.defaultTheme(lightColorScheme);
      final newTheme = originalTheme.copyWith(
        headerBackground: Colors.red,
        borderWidth: 3.0,
      );
      
      expect(newTheme.headerBackground, Colors.red);
      expect(newTheme.borderWidth, 3.0);
      // Other values should remain the same
      expect(newTheme.headerForeground, originalTheme.headerForeground);
      expect(newTheme.contentBackground, originalTheme.contentBackground);
    });

    test('validates correctly', () {
      const validTheme = ShadcnAccordionTheme(
        borderWidth: 2.0,
        triggerIconSize: 20.0,
        elevation: 4.0,
      );
      
      expect(validTheme.validate(), isTrue);

      const invalidTheme = ShadcnAccordionTheme(borderWidth: -1.0);
      expect(invalidTheme.validate(), isFalse);
    });
  });
}