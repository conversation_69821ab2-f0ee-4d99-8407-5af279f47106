import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/utils/theme_resolver.dart';
import 'package:shadcn/src/theme/extensions/shadcn_theme_extension.dart';

void main() {
  group('ShadcnThemeResolver Unit Tests', () {
    group('ThemeException', () {
      test('creates exception with message', () {
        const message = 'Test error message';
        const exception = ThemeException(message);
        
        expect(exception.message, equals(message));
        expect(exception.toString(), equals('ThemeException: $message'));
      });
    });

    group('validateThemeProperty', () {
      test('returns true for non-null values', () {
        const value = 'test';
        final result = ShadcnThemeResolver.validateThemeProperty(
          value,
          'testProperty',
        );
        
        expect(result, isTrue);
      });

      test('returns false for null values', () {
        final result = ShadcnThemeResolver.validateThemeProperty(
          null,
          'testProperty',
        );
        
        expect(result, isFalse);
      });

      test('throws when throwOnError is true', () {
        expect(
          () => ShadcnThemeResolver.validateThemeProperty(
            null,
            'testProperty',
            throwOnError: true,
          ),
          throwsA(isA<ThemeException>()),
        );
      });

      test('validates different data types', () {
        expect(
          ShadcnThemeResolver.validateThemeProperty<String>('test', 'stringProp'),
          isTrue,
        );
        expect(
          ShadcnThemeResolver.validateThemeProperty<int>(42, 'intProp'),
          isTrue,
        );
        expect(
          ShadcnThemeResolver.validateThemeProperty<double>(3.14, 'doubleProp'),
          isTrue,
        );
        expect(
          ShadcnThemeResolver.validateThemeProperty<bool>(true, 'boolProp'),
          isTrue,
        );
      });
    });

    group('validateThemeExtension', () {
      test('returns false for null extension', () {
        final result = ShadcnThemeResolver.validateThemeExtension<TestThemeExtension>(
          null,
          ['color'],
        );
        
        expect(result, isFalse);
      });

      test('returns true for non-null extension', () {
        final extension = TestThemeExtension(color: const Color(0xFF0000FF));
        final result = ShadcnThemeResolver.validateThemeExtension<TestThemeExtension>(
          extension,
          ['color'],
        );
        
        expect(result, isTrue);
      });
    });
  });
}

/// Mock implementation for testing
class TestThemeExtension extends ShadcnThemeExtension<TestThemeExtension> {
  final Color color;
  
  const TestThemeExtension({required this.color});
  
  @override
  TestThemeExtension copyWith({Color? color}) {
    return TestThemeExtension(color: color ?? this.color);
  }
  
  @override
  TestThemeExtension lerp(TestThemeExtension? other, double t) {
    if (other == null) return this;
    return TestThemeExtension(
      color: Color.lerp(color, other.color, t) ?? color,
    );
  }
}