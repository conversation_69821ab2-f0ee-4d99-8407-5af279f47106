import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shadcn/src/theme/extensions/shadcn_theme_extension.dart';
import 'package:shadcn/src/utils/theme_resolver.dart';

void main() {
  group('ShadcnThemeExtension', () {
    late ThemeData lightTheme;
    late ThemeData darkTheme;
    late TestShadcnThemeExtension testExtension;
    
    setUp(() {
      lightTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
      );
      
      darkTheme = ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
      );
      
      testExtension = TestShadcnThemeExtension(
        customColor: Colors.red,
        customTextStyle: const TextStyle(fontSize: 20),
        customSpacing: const EdgeInsets.all(20),
        customRadius: BorderRadius.circular(8),
        customHeight: 50.0,
      );
    });

    group('resolveColor', () {
      testWidgets('uses ShadcnThemeResolver for color resolution', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolvedColor = testExtension.resolveColor(
                  context,
                  Colors.green,
                  (theme) => theme.colorScheme.primary,
                  Colors.grey,
                );
                
                // Should return the custom color
                expect(resolvedColor, equals(Colors.green));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('falls back to material color when custom is null', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolvedColor = testExtension.resolveColor(
                  context,
                  null,
                  (theme) => theme.colorScheme.secondary,
                  Colors.grey,
                );
                
                expect(resolvedColor, equals(lightTheme.colorScheme.secondary));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('uses fallback color on error', (tester) async {
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final resolvedColor = testExtension.resolveColor(
                context,
                null,
                (theme) => throw Exception('Test error'),
                Colors.red,
              );
              
              expect(resolvedColor, equals(Colors.red));
              return const SizedBox.shrink();
            },
          ),
        );
      });
    });

    group('resolveTextStyle', () {
      testWidgets('merges custom style with material base using resolver', (tester) async {
        const customStyle = TextStyle(fontSize: 24, color: Colors.red);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolvedStyle = testExtension.resolveTextStyle(
                  context,
                  customStyle,
                  (textTheme) => textTheme.bodyMedium!,
                );
                
                expect(resolvedStyle.fontSize, equals(24));
                expect(resolvedStyle.color, equals(Colors.red));
                // Should inherit other properties from base style
                expect(resolvedStyle.fontFamily, 
                  equals(lightTheme.textTheme.bodyMedium!.fontFamily));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('returns material style when custom is null', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolvedStyle = testExtension.resolveTextStyle(
                  context,
                  null,
                  (textTheme) => textTheme.headlineSmall!,
                );
                
                expect(resolvedStyle, equals(lightTheme.textTheme.headlineSmall));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('uses fallback style on error', (tester) async {
        const fallbackStyle = TextStyle(fontSize: 16, color: Colors.black);
        
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              final resolvedStyle = testExtension.resolveTextStyle(
                context,
                null,
                (textTheme) => throw Exception('Test error'),
                fallbackStyle,
              );
              
              expect(resolvedStyle, equals(fallbackStyle));
              return const SizedBox.shrink();
            },
          ),
        );
      });
    });

    group('resolveSpacing', () {
      testWidgets('returns custom spacing when provided', (tester) async {
        const customSpacing = EdgeInsets.all(30);
        const defaultSpacing = EdgeInsets.all(10);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolvedSpacing = testExtension.resolveSpacing(
                  context,
                  customSpacing,
                  defaultSpacing,
                );
                
                expect(resolvedSpacing, equals(customSpacing));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('applies visual density to default spacing', (tester) async {
        const defaultSpacing = EdgeInsets.symmetric(horizontal: 16, vertical: 8);
        final denseTheme = lightTheme.copyWith(
          visualDensity: VisualDensity.compact,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: denseTheme,
            home: Builder(
              builder: (context) {
                final resolvedSpacing = testExtension.resolveSpacing(
                  context,
                  null,
                  defaultSpacing,
                );
                
                final expectedAdjustment = VisualDensity.compact.baseSizeAdjustment;
                final expected = EdgeInsets.fromLTRB(
                  defaultSpacing.left + expectedAdjustment.dx,
                  defaultSpacing.top + expectedAdjustment.dy,
                  defaultSpacing.right + expectedAdjustment.dx,
                  defaultSpacing.bottom + expectedAdjustment.dy,
                );
                
                expect(resolvedSpacing, equals(expected));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });
    });

    group('resolveBorderRadius', () {
      testWidgets('returns custom radius when provided', (tester) async {
        final customRadius = BorderRadius.circular(15);
        final defaultRadius = BorderRadius.circular(8);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolvedRadius = testExtension.resolveBorderRadius(
                  context,
                  customRadius,
                  defaultRadius,
                );
                
                expect(resolvedRadius, equals(customRadius));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('returns default radius when custom is null', (tester) async {
        final defaultRadius = BorderRadius.circular(12);
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolvedRadius = testExtension.resolveBorderRadius(
                  context,
                  null,
                  defaultRadius,
                );
                
                expect(resolvedRadius, equals(defaultRadius));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });
    });

    group('resolveDouble', () {
      testWidgets('returns custom value when provided', (tester) async {
        const customValue = 60.0;
        const defaultValue = 40.0;
        
        await tester.pumpWidget(
          MaterialApp(
            theme: lightTheme,
            home: Builder(
              builder: (context) {
                final resolvedValue = testExtension.resolveDouble(
                  context,
                  customValue,
                  defaultValue,
                );
                
                expect(resolvedValue, equals(customValue));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('applies visual density to default value', (tester) async {
        const defaultValue = 40.0;
        final denseTheme = lightTheme.copyWith(
          visualDensity: VisualDensity.compact,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: denseTheme,
            home: Builder(
              builder: (context) {
                final resolvedValue = testExtension.resolveDouble(
                  context,
                  null,
                  defaultValue,
                );
                
                final expectedAdjustment = VisualDensity.compact.baseSizeAdjustment.dy;
                final expected = (defaultValue + expectedAdjustment).clamp(0.0, double.infinity);
                
                expect(resolvedValue, equals(expected));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });

      testWidgets('skips density adjustment when disabled', (tester) async {
        const defaultValue = 40.0;
        final denseTheme = lightTheme.copyWith(
          visualDensity: VisualDensity.compact,
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: denseTheme,
            home: Builder(
              builder: (context) {
                final resolvedValue = testExtension.resolveDouble(
                  context,
                  null,
                  defaultValue,
                  applyVerticalDensity: false,
                );
                
                expect(resolvedValue, equals(defaultValue));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });
    });

    group('validate', () {
      test('base implementation returns true', () {
        final result = testExtension.validate();
        expect(result, isTrue);
      });

      test('base implementation with throwOnError returns true', () {
        final result = testExtension.validate(throwOnError: true);
        expect(result, isTrue);
      });
    });

    group('static methods', () {
      group('of', () {
        testWidgets('returns extension when available', (tester) async {
          final themeWithExtension = lightTheme.copyWith(
            extensions: [testExtension],
          );

          await tester.pumpWidget(
            MaterialApp(
              theme: themeWithExtension,
              home: Builder(
                builder: (context) {
                  final extension = ShadcnThemeExtension.of<TestShadcnThemeExtension>(context);
                  expect(extension, equals(testExtension));
                  return const SizedBox.shrink();
                },
              ),
            ),
          );
        });

        testWidgets('returns null when extension not available', (tester) async {
          await tester.pumpWidget(
            MaterialApp(
              theme: lightTheme,
              home: Builder(
                builder: (context) {
                  final extension = ShadcnThemeExtension.of<TestShadcnThemeExtension>(context);
                  expect(extension, isNull);
                  return const SizedBox.shrink();
                },
              ),
            ),
          );
        });

        testWidgets('returns null on error', (tester) async {
          await tester.pumpWidget(
            Builder(
              builder: (context) {
                final extension = ShadcnThemeExtension.of<TestShadcnThemeExtension>(context);
                expect(extension, isNull);
                return const SizedBox.shrink();
              },
            ),
          );
        });
      });

      group('resolve', () {
        testWidgets('returns extension when available', (tester) async {
          final themeWithExtension = lightTheme.copyWith(
            extensions: [testExtension],
          );

          await tester.pumpWidget(
            MaterialApp(
              theme: themeWithExtension,
              home: Builder(
                builder: (context) {
                  final resolved = ShadcnThemeExtension.resolve<TestShadcnThemeExtension>(
                    context,
                    (colorScheme) => TestShadcnThemeExtension.defaultTheme(colorScheme),
                  );
                  
                  expect(resolved, equals(testExtension));
                  return const SizedBox.shrink();
                },
              ),
            ),
          );
        });

        testWidgets('falls back to default factory when extension not available', (tester) async {
          await tester.pumpWidget(
            MaterialApp(
              theme: lightTheme,
              home: Builder(
                builder: (context) {
                  final resolved = ShadcnThemeExtension.resolve<TestShadcnThemeExtension>(
                    context,
                    (colorScheme) => TestShadcnThemeExtension.defaultTheme(colorScheme),
                  );
                  
                  expect(resolved, isA<TestShadcnThemeExtension>());
                  expect(resolved.customColor, equals(lightTheme.colorScheme.primary));
                  return const SizedBox.shrink();
                },
              ),
            ),
          );
        });

        testWidgets('handles errors with emergency fallback', (tester) async {
          await tester.pumpWidget(
            Builder(
              builder: (context) {
                final resolved = ShadcnThemeExtension.resolve<TestShadcnThemeExtension>(
                  context,
                  (colorScheme) => TestShadcnThemeExtension.defaultTheme(colorScheme),
                );
                
                expect(resolved, isA<TestShadcnThemeExtension>());
                expect(resolved.customColor, equals(Colors.blue)); // Emergency fallback color
                return const SizedBox.shrink();
              },
            ),
          );
        });
      });
    });

    group('integration with different theme configurations', () {
      testWidgets('works with light and dark themes', (tester) async {
        for (final theme in [lightTheme, darkTheme]) {
          await tester.pumpWidget(
            MaterialApp(
              theme: theme,
              home: Builder(
                builder: (context) {
                  final resolvedColor = testExtension.resolveColor(
                    context,
                    null,
                    (theme) => theme.colorScheme.primary,
                  );
                  
                  expect(resolvedColor, equals(theme.colorScheme.primary));
                  return const SizedBox.shrink();
                },
              ),
            ),
          );
        }
      });

      testWidgets('works with different visual densities', (tester) async {
        final densities = [
          VisualDensity.standard,
          VisualDensity.compact,
          VisualDensity.comfortable,
          const VisualDensity(horizontal: -2, vertical: -2),
        ];

        for (final density in densities) {
          final themedTheme = lightTheme.copyWith(visualDensity: density);
          
          await tester.pumpWidget(
            MaterialApp(
              theme: themedTheme,
              home: Builder(
                builder: (context) {
                  final resolvedDouble = testExtension.resolveDouble(
                    context,
                    null,
                    40.0,
                  );
                  
                  final expectedAdjustment = density.baseSizeAdjustment.dy;
                  final expected = (40.0 + expectedAdjustment).clamp(0.0, double.infinity);
                  
                  expect(resolvedDouble, equals(expected));
                  return const SizedBox.shrink();
                },
              ),
            ),
          );
        }
      });
    });

    group('error handling and edge cases', () {
      testWidgets('handles null theme gracefully', (tester) async {
        await tester.pumpWidget(
          Builder(
            builder: (context) {
              // All methods should handle missing theme gracefully
              final color = testExtension.resolveColor(
                context,
                null,
                (theme) => theme.colorScheme.primary,
                Colors.red,
              );
              expect(color, equals(Colors.red));
              
              final textStyle = testExtension.resolveTextStyle(
                context,
                null,
                (textTheme) => textTheme.bodyMedium!,
                const TextStyle(),
              );
              expect(textStyle, equals(const TextStyle()));
              
              return const SizedBox.shrink();
            },
          ),
        );
      });

      testWidgets('handles extreme visual density values', (tester) async {
        final extremeDensityTheme = lightTheme.copyWith(
          visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
        );
        
        await tester.pumpWidget(
          MaterialApp(
            theme: extremeDensityTheme,
            home: Builder(
              builder: (context) {
                final resolvedDouble = testExtension.resolveDouble(
                  context,
                  null,
                  10.0,
                );
                
                // Should be clamped to 0 even with extreme negative adjustment
                expect(resolvedDouble, equals(0.0));
                return const SizedBox.shrink();
              },
            ),
          ),
        );
      });
    });
  });
}

/// Test implementation of ShadcnThemeExtension for testing purposes.
class TestShadcnThemeExtension extends ShadcnThemeExtension<TestShadcnThemeExtension> {
  final Color? customColor;
  final TextStyle? customTextStyle;
  final EdgeInsets? customSpacing;
  final BorderRadius? customRadius;
  final double? customHeight;

  const TestShadcnThemeExtension({
    this.customColor,
    this.customTextStyle,
    this.customSpacing,
    this.customRadius,
    this.customHeight,
  });

  static TestShadcnThemeExtension defaultTheme(ColorScheme colorScheme) {
    return TestShadcnThemeExtension(
      customColor: colorScheme.primary,
      customTextStyle: const TextStyle(fontSize: 14),
      customSpacing: const EdgeInsets.all(8),
      customRadius: BorderRadius.circular(8),
      customHeight: 40.0,
    );
  }

  @override
  TestShadcnThemeExtension copyWith({
    Color? customColor,
    TextStyle? customTextStyle,
    EdgeInsets? customSpacing,
    BorderRadius? customRadius,
    double? customHeight,
  }) {
    return TestShadcnThemeExtension(
      customColor: customColor ?? this.customColor,
      customTextStyle: customTextStyle ?? this.customTextStyle,
      customSpacing: customSpacing ?? this.customSpacing,
      customRadius: customRadius ?? this.customRadius,
      customHeight: customHeight ?? this.customHeight,
    );
  }

  @override
  TestShadcnThemeExtension lerp(TestShadcnThemeExtension? other, double t) {
    if (other is! TestShadcnThemeExtension) return this;
    return TestShadcnThemeExtension(
      customColor: Color.lerp(customColor, other.customColor, t),
      customTextStyle: TextStyle.lerp(customTextStyle, other.customTextStyle, t),
      customSpacing: EdgeInsets.lerp(customSpacing, other.customSpacing, t),
      customRadius: BorderRadius.lerp(customRadius, other.customRadius, t),
      customHeight: lerpDouble(customHeight, other.customHeight, t),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    // Test implementation can add specific validation
    final isValid = customColor != null && customHeight != null;
    
    if (!isValid && throwOnError) {
      throw ThemeException('TestShadcnThemeExtension validation failed: missing required properties');
    }
    
    return isValid;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TestShadcnThemeExtension &&
        other.customColor == customColor &&
        other.customTextStyle == customTextStyle &&
        other.customSpacing == customSpacing &&
        other.customRadius == customRadius &&
        other.customHeight == customHeight;
  }
  
  @override
  int get hashCode => Object.hash(
    customColor,
    customTextStyle,
    customSpacing,
    customRadius,
    customHeight,
  );
}

/// Helper function for lerping double values (similar to Flutter's internal implementation)
double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}