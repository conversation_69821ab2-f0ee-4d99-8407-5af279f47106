import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnProgress component.
/// 
/// Provides theming for progress indicators with linear and circular variants,
/// including colors, sizing, and animation properties.
class ShadcnProgressTheme extends ShadcnThemeExtension<ShadcnProgressTheme> {
  /// Background color of the progress track
  final Color? backgroundColor;
  
  /// Color of the progress indicator/fill
  final Color? progressColor;
  
  /// Height of linear progress indicator
  final double? height;
  
  /// Size of circular progress indicator
  final double? size;
  
  /// Stroke width for circular progress indicator
  final double? strokeWidth;
  
  /// Border radius for linear progress indicator
  final BorderRadius? borderRadius;
  
  /// Duration for progress animation
  final Duration? animationDuration;
  
  /// Animation curve for progress updates
  final Curve? animationCurve;
  
  /// Text style for progress percentage display
  final TextStyle? textStyle;

  const ShadcnProgressTheme({
    this.backgroundColor,
    this.progressColor,
    this.height,
    this.size,
    this.strokeWidth,
    this.borderRadius,
    this.animationDuration,
    this.animationCurve,
    this.textStyle,
  });

  @override
  ShadcnProgressTheme copyWith({
    Color? backgroundColor,
    Color? progressColor,
    double? height,
    double? size,
    double? strokeWidth,
    BorderRadius? borderRadius,
    Duration? animationDuration,
    Curve? animationCurve,
    TextStyle? textStyle,
  }) {
    return ShadcnProgressTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      progressColor: progressColor ?? this.progressColor,
      height: height ?? this.height,
      size: size ?? this.size,
      strokeWidth: strokeWidth ?? this.strokeWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      textStyle: textStyle ?? this.textStyle,
    );
  }

  @override
  ShadcnProgressTheme lerp(ShadcnProgressTheme? other, double t) {
    if (other is! ShadcnProgressTheme) return this;

    return ShadcnProgressTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      progressColor: Color.lerp(progressColor, other.progressColor, t),
      height: lerpDouble(height, other.height, t),
      size: lerpDouble(size, other.size, t),
      strokeWidth: lerpDouble(strokeWidth, other.strokeWidth, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      animationDuration: other.animationDuration,
      animationCurve: other.animationCurve,
      textStyle: TextStyle.lerp(textStyle, other.textStyle, t),
    );
  }

  /// Creates a default progress theme based on the provided color scheme.
  static ShadcnProgressTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnProgressTheme(
      backgroundColor: colorScheme.surfaceVariant.withOpacity(0.2),
      progressColor: colorScheme.primary,
      height: 8.0,
      size: 32.0,
      strokeWidth: 3.0,
      borderRadius: BorderRadius.circular(4.0),
      animationDuration: const Duration(milliseconds: 300),
      animationCurve: Curves.easeInOut,
      textStyle: TextStyle(
        color: colorScheme.onSurface,
        fontSize: 12.0,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  @override
  List<Object?> get props => [
        backgroundColor,
        progressColor,
        height,
        size,
        strokeWidth,
        borderRadius,
        animationDuration,
        animationCurve,
        textStyle,
      ];
}