import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';

/// Theme extension for the ShadcnSkeleton component.
/// 
/// Provides theming for skeleton loading animations,
/// including colors, shimmer effects, and animation properties.
class ShadcnSkeletonTheme extends ShadcnThemeExtension<ShadcnSkeletonTheme> {
  /// Base color of the skeleton
  final Color? baseColor;
  
  /// Highlight color for shimmer effect
  final Color? highlightColor;
  
  /// Duration for shimmer animation cycle
  final Duration? animationDuration;
  
  /// Animation curve for shimmer effect
  final Curve? animationCurve;
  
  /// Border radius for skeleton shapes
  final BorderRadius? borderRadius;
  
  /// Default height for skeleton lines
  final double? lineHeight;
  
  /// Default width for skeleton elements
  final double? defaultWidth;
  
  /// Gradient stops for shimmer effect
  final List<double>? gradientStops;
  
  /// Whether to enable shimmer animation
  final bool? enableShimmer;

  const ShadcnSkeletonTheme({
    this.baseColor,
    this.highlightColor,
    this.animationDuration,
    this.animationCurve,
    this.borderRadius,
    this.lineHeight,
    this.defaultWidth,
    this.gradientStops,
    this.enableShimmer,
  });

  @override
  ShadcnSkeletonTheme copyWith({
    Color? baseColor,
    Color? highlightColor,
    Duration? animationDuration,
    Curve? animationCurve,
    BorderRadius? borderRadius,
    double? lineHeight,
    double? defaultWidth,
    List<double>? gradientStops,
    bool? enableShimmer,
  }) {
    return ShadcnSkeletonTheme(
      baseColor: baseColor ?? this.baseColor,
      highlightColor: highlightColor ?? this.highlightColor,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      borderRadius: borderRadius ?? this.borderRadius,
      lineHeight: lineHeight ?? this.lineHeight,
      defaultWidth: defaultWidth ?? this.defaultWidth,
      gradientStops: gradientStops ?? this.gradientStops,
      enableShimmer: enableShimmer ?? this.enableShimmer,
    );
  }

  @override
  ShadcnSkeletonTheme lerp(ShadcnSkeletonTheme? other, double t) {
    if (other is! ShadcnSkeletonTheme) return this;

    return ShadcnSkeletonTheme(
      baseColor: Color.lerp(baseColor, other.baseColor, t),
      highlightColor: Color.lerp(highlightColor, other.highlightColor, t),
      animationDuration: other.animationDuration,
      animationCurve: other.animationCurve,
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      lineHeight: lerpDouble(lineHeight, other.lineHeight, t),
      defaultWidth: lerpDouble(defaultWidth, other.defaultWidth, t),
      gradientStops: other.gradientStops,
      enableShimmer: other.enableShimmer,
    );
  }

  /// Creates a default skeleton theme based on the provided color scheme.
  static ShadcnSkeletonTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnSkeletonTheme(
      baseColor: isDark 
          ? colorScheme.surfaceVariant.withOpacity(0.2)
          : colorScheme.surfaceVariant.withOpacity(0.3),
      highlightColor: isDark
          ? colorScheme.surfaceVariant.withOpacity(0.4)
          : colorScheme.surfaceVariant.withOpacity(0.5),
      animationDuration: const Duration(milliseconds: 1500),
      animationCurve: Curves.easeInOut,
      borderRadius: BorderRadius.circular(4.0),
      lineHeight: 16.0,
      defaultWidth: 100.0,
      gradientStops: const [0.0, 0.5, 1.0],
      enableShimmer: true,
    );
  }

  @override
  List<Object?> get props => [
        baseColor,
        highlightColor,
        animationDuration,
        animationCurve,
        borderRadius,
        lineHeight,
        defaultWidth,
        gradientStops,
        enableShimmer,
      ];
}