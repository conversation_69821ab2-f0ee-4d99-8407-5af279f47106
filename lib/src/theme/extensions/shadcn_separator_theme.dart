import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for ShadcnSeparator component.
/// 
/// Provides theming properties for separator dividers with support for
/// horizontal and vertical orientations. Integrates with Material Design
/// color schemes while providing shadcn-specific styling options.
class ShadcnSeparatorTheme extends ShadcnThemeExtension<ShadcnSeparatorTheme> {
  /// Color of the separator line
  final Color? color;
  
  /// Thickness of the separator line
  final double? thickness;
  
  /// Spacing around the separator
  final EdgeInsets? margin;
  
  /// Opacity of the separator
  final double? opacity;
  
  /// Additional height for horizontal separators (affects touch target)
  final double? height;
  
  /// Additional width for vertical separators (affects touch target)
  final double? width;
  
  const ShadcnSeparatorTheme({
    this.color,
    this.thickness,
    this.margin,
    this.opacity,
    this.height,
    this.width,
  });

  @override
  ShadcnSeparatorTheme copyWith({
    Color? color,
    double? thickness,
    EdgeInsets? margin,
    double? opacity,
    double? height,
    double? width,
  }) {
    return ShadcnSeparatorTheme(
      color: color ?? this.color,
      thickness: thickness ?? this.thickness,
      margin: margin ?? this.margin,
      opacity: opacity ?? this.opacity,
      height: height ?? this.height,
      width: width ?? this.width,
    );
  }

  @override
  ShadcnSeparatorTheme lerp(
    covariant ThemeExtension<ShadcnSeparatorTheme>? other, 
    double t,
  ) {
    if (other is! ShadcnSeparatorTheme) {
      return this;
    }

    return ShadcnSeparatorTheme(
      color: Color.lerp(color, other.color, t),
      thickness: lerpDouble(thickness, other.thickness, t),
      margin: EdgeInsets.lerp(margin, other.margin, t),
      opacity: lerpDouble(opacity, other.opacity, t),
      height: lerpDouble(height, other.height, t),
      width: lerpDouble(width, other.width, t),
    );
  }

  /// Creates a default theme based on the provided color scheme.
  /// 
  /// Uses shadcn design tokens and Material color schemes to provide
  /// consistent default styling that matches shadcn specifications.
  static ShadcnSeparatorTheme defaultTheme(ColorScheme colorScheme) {
    return ShadcnSeparatorTheme(
      color: colorScheme.outline,
      thickness: ShadcnTokens.borderWidth,
      margin: EdgeInsets.zero,
      opacity: 1.0,
      height: ShadcnTokens.spacing4, // 16px touch target for horizontal
      width: ShadcnTokens.spacing4,  // 16px touch target for vertical
    );
  }

  /// Creates a light theme variant.
  static ShadcnSeparatorTheme lightTheme(ColorScheme colorScheme) {
    return ShadcnSeparatorTheme(
      color: colorScheme.outlineVariant,
      thickness: ShadcnTokens.borderWidth,
      margin: EdgeInsets.zero,
      opacity: 0.8,
      height: ShadcnTokens.spacing4,
      width: ShadcnTokens.spacing4,
    );
  }

  /// Creates a dark theme variant.
  static ShadcnSeparatorTheme darkTheme(ColorScheme colorScheme) {
    return ShadcnSeparatorTheme(
      color: colorScheme.outline,
      thickness: ShadcnTokens.borderWidth,
      margin: EdgeInsets.zero,
      opacity: 0.6,
      height: ShadcnTokens.spacing4,
      width: ShadcnTokens.spacing4,
    );
  }

  /// Gets the resolved color for the separator.
  /// 
  /// Uses the theme-aware fallback chain to ensure a valid color
  /// is always returned, with proper Material theme integration.
  Color getColor(BuildContext context) {
    return resolveColor(
      context,
      color,
      (theme) => theme.colorScheme.outline,
      Colors.grey.shade400,
    );
  }

  /// Gets the resolved thickness for the separator.
  /// 
  /// Applies visual density adjustments while maintaining
  /// minimum thickness for accessibility.
  double getThickness(BuildContext context) {
    final resolvedThickness = resolveDouble(
      context,
      thickness,
      ShadcnTokens.borderWidth,
      applyVerticalDensity: false,
    );
    
    // Ensure minimum thickness for accessibility
    return resolvedThickness.clamp(0.5, double.infinity);
  }

  /// Gets the resolved margin for the separator.
  /// 
  /// Applies visual density adjustments to ensure proper spacing.
  EdgeInsets getMargin(BuildContext context) {
    return resolveSpacing(
      context,
      margin,
      EdgeInsets.zero,
    );
  }

  /// Gets the resolved opacity for the separator.
  /// 
  /// Ensures opacity is within valid range while respecting theme values.
  double getOpacity(BuildContext context) {
    final resolvedOpacity = opacity ?? 1.0;
    return resolvedOpacity.clamp(0.0, 1.0);
  }

  /// Gets the resolved height for horizontal separators.
  /// 
  /// Applies visual density adjustments while maintaining minimum
  /// touch targets for accessibility compliance.
  double getHeight(BuildContext context) {
    return resolveDouble(
      context,
      height,
      ShadcnTokens.spacing4,
      applyVerticalDensity: true,
    ).clamp(getThickness(context), double.infinity);
  }

  /// Gets the resolved width for vertical separators.
  /// 
  /// Applies visual density adjustments while maintaining minimum
  /// touch targets for accessibility compliance.
  double getWidth(BuildContext context) {
    return resolveDouble(
      context,
      width,
      ShadcnTokens.spacing4,
      applyVerticalDensity: false,
    ).clamp(getThickness(context), double.infinity);
  }

  @override
  bool validate({bool throwOnError = false}) {
    try {
      // Validate thickness
      if (thickness != null && thickness! < 0) {
        if (throwOnError) {
          throw ArgumentError('Separator thickness must be non-negative');
        }
        return false;
      }

      // Validate opacity
      if (opacity != null && (opacity! < 0.0 || opacity! > 1.0)) {
        if (throwOnError) {
          throw ArgumentError('Separator opacity must be between 0.0 and 1.0');
        }
        return false;
      }

      // Validate dimensions
      if (height != null && height! < 0) {
        if (throwOnError) {
          throw ArgumentError('Separator height must be non-negative');
        }
        return false;
      }

      if (width != null && width! < 0) {
        if (throwOnError) {
          throw ArgumentError('Separator width must be non-negative');
        }
        return false;
      }

      return true;
    } catch (e) {
      if (throwOnError) rethrow;
      return false;
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShadcnSeparatorTheme &&
          runtimeType == other.runtimeType &&
          color == other.color &&
          thickness == other.thickness &&
          margin == other.margin &&
          opacity == other.opacity &&
          height == other.height &&
          width == other.width;

  @override
  int get hashCode =>
      color.hashCode ^
      thickness.hashCode ^
      margin.hashCode ^
      opacity.hashCode ^
      height.hashCode ^
      width.hashCode;

  @override
  String toString() {
    return 'ShadcnSeparatorTheme('
        'color: $color, '
        'thickness: $thickness, '
        'margin: $margin, '
        'opacity: $opacity, '
        'height: $height, '
        'width: $width'
        ')';
  }
}

/// Helper class for separator thickness calculations.
/// 
/// Provides utilities for calculating appropriate thickness values
/// based on different contexts and visual densities.
class ShadcnSeparatorThickness {
  ShadcnSeparatorThickness._();

  /// Standard thickness for most separators
  static const double standard = ShadcnTokens.borderWidth;

  /// Thick thickness for emphasis
  static const double thick = ShadcnTokens.borderWidthThick;

  /// Calculates thickness with visual density adjustments.
  /// 
  /// Ensures thickness remains visible across different screen densities
  /// while maintaining design consistency.
  static double adaptive(BuildContext context, double baseThickness) {
    final theme = Theme.of(context);
    final density = theme.visualDensity;
    
    // Apply minimal adjustments to maintain line visibility
    final adjustment = density.vertical * 0.5; // Subtle adjustment
    final adjustedThickness = baseThickness + adjustment;
    
    // Ensure minimum thickness for visibility
    return adjustedThickness.clamp(0.5, baseThickness * 2);
  }
}

/// Extension for convenient separator theme access.
/// 
/// Provides convenient methods for accessing separator theme properties
/// from the current theme context.
extension ShadcnSeparatorThemeExtension on ThemeData {
  /// Gets the separator theme extension from this theme data.
  /// 
  /// Returns null if no separator theme is configured.
  ShadcnSeparatorTheme? get separatorTheme => extension<ShadcnSeparatorTheme>();

  /// Gets the separator theme extension or creates a default one.
  /// 
  /// Ensures a valid separator theme is always available.
  ShadcnSeparatorTheme separatorThemeOrDefault() =>
      separatorTheme ?? ShadcnSeparatorTheme.defaultTheme(colorScheme);
}