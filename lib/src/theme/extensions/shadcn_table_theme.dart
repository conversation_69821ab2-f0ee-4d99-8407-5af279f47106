import 'package:flutter/material.dart';
import 'shadcn_theme_extension.dart';
import '../../constants/shadcn_tokens.dart';

/// Theme extension for shadcn table components.
/// 
/// This extension provides comprehensive theming for table components including
/// headers, cells, borders, and interactive states like hover and selection.
/// All styling is derived from the Material theme with shadcn design tokens.
class ShadcnTableTheme extends ShadcnThemeExtension<ShadcnTableTheme> {
  /// Background color for the table container
  final Color? backgroundColor;
  
  /// Border color for table borders and dividers
  final Color? borderColor;
  
  /// Header background color
  final Color? headerBackgroundColor;
  
  /// Header text color
  final Color? headerForegroundColor;
  
  /// Header text style
  final TextStyle? headerTextStyle;
  
  /// Header padding
  final EdgeInsets? headerPadding;
  
  /// Cell background color (for regular cells)
  final Color? cellBackgroundColor;
  
  /// Cell text color
  final Color? cellForegroundColor;
  
  /// Cell text style
  final TextStyle? cellTextStyle;
  
  /// Cell padding
  final EdgeInsets? cellPadding;
  
  /// Alternating row background color (for striped rows)
  final Color? alternatingRowColor;
  
  /// Hover row background color
  final Color? hoverRowColor;
  
  /// Selected row background color
  final Color? selectedRowColor;
  
  /// Selected row text color
  final Color? selectedRowForegroundColor;
  
  /// Focused row border color
  final Color? focusedRowBorderColor;
  
  /// Border width for table borders
  final double? borderWidth;
  
  /// Row height for table rows
  final double? rowHeight;
  
  /// Header height
  final double? headerHeight;
  
  /// Border radius for the table container
  final BorderRadius? borderRadius;
  
  /// Sort indicator color for sortable columns
  final Color? sortIndicatorColor;
  
  /// Sort indicator size
  final double? sortIndicatorSize;
  
  /// Checkbox theme data for selection checkboxes
  final CheckboxThemeData? checkboxTheme;

  const ShadcnTableTheme({
    this.backgroundColor,
    this.borderColor,
    this.headerBackgroundColor,
    this.headerForegroundColor,
    this.headerTextStyle,
    this.headerPadding,
    this.cellBackgroundColor,
    this.cellForegroundColor,
    this.cellTextStyle,
    this.cellPadding,
    this.alternatingRowColor,
    this.hoverRowColor,
    this.selectedRowColor,
    this.selectedRowForegroundColor,
    this.focusedRowBorderColor,
    this.borderWidth,
    this.rowHeight,
    this.headerHeight,
    this.borderRadius,
    this.sortIndicatorColor,
    this.sortIndicatorSize,
    this.checkboxTheme,
  });

  @override
  ShadcnTableTheme copyWith({
    Color? backgroundColor,
    Color? borderColor,
    Color? headerBackgroundColor,
    Color? headerForegroundColor,
    TextStyle? headerTextStyle,
    EdgeInsets? headerPadding,
    Color? cellBackgroundColor,
    Color? cellForegroundColor,
    TextStyle? cellTextStyle,
    EdgeInsets? cellPadding,
    Color? alternatingRowColor,
    Color? hoverRowColor,
    Color? selectedRowColor,
    Color? selectedRowForegroundColor,
    Color? focusedRowBorderColor,
    double? borderWidth,
    double? rowHeight,
    double? headerHeight,
    BorderRadius? borderRadius,
    Color? sortIndicatorColor,
    double? sortIndicatorSize,
    CheckboxThemeData? checkboxTheme,
  }) {
    return ShadcnTableTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderColor: borderColor ?? this.borderColor,
      headerBackgroundColor: headerBackgroundColor ?? this.headerBackgroundColor,
      headerForegroundColor: headerForegroundColor ?? this.headerForegroundColor,
      headerTextStyle: headerTextStyle ?? this.headerTextStyle,
      headerPadding: headerPadding ?? this.headerPadding,
      cellBackgroundColor: cellBackgroundColor ?? this.cellBackgroundColor,
      cellForegroundColor: cellForegroundColor ?? this.cellForegroundColor,
      cellTextStyle: cellTextStyle ?? this.cellTextStyle,
      cellPadding: cellPadding ?? this.cellPadding,
      alternatingRowColor: alternatingRowColor ?? this.alternatingRowColor,
      hoverRowColor: hoverRowColor ?? this.hoverRowColor,
      selectedRowColor: selectedRowColor ?? this.selectedRowColor,
      selectedRowForegroundColor: selectedRowForegroundColor ?? this.selectedRowForegroundColor,
      focusedRowBorderColor: focusedRowBorderColor ?? this.focusedRowBorderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      rowHeight: rowHeight ?? this.rowHeight,
      headerHeight: headerHeight ?? this.headerHeight,
      borderRadius: borderRadius ?? this.borderRadius,
      sortIndicatorColor: sortIndicatorColor ?? this.sortIndicatorColor,
      sortIndicatorSize: sortIndicatorSize ?? this.sortIndicatorSize,
      checkboxTheme: checkboxTheme ?? this.checkboxTheme,
    );
  }

  @override
  ShadcnTableTheme lerp(ShadcnTableTheme? other, double t) {
    if (other == null) return this;
    
    return ShadcnTableTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      headerBackgroundColor: Color.lerp(headerBackgroundColor, other.headerBackgroundColor, t),
      headerForegroundColor: Color.lerp(headerForegroundColor, other.headerForegroundColor, t),
      headerTextStyle: TextStyle.lerp(headerTextStyle, other.headerTextStyle, t),
      headerPadding: EdgeInsets.lerp(headerPadding, other.headerPadding, t),
      cellBackgroundColor: Color.lerp(cellBackgroundColor, other.cellBackgroundColor, t),
      cellForegroundColor: Color.lerp(cellForegroundColor, other.cellForegroundColor, t),
      cellTextStyle: TextStyle.lerp(cellTextStyle, other.cellTextStyle, t),
      cellPadding: EdgeInsets.lerp(cellPadding, other.cellPadding, t),
      alternatingRowColor: Color.lerp(alternatingRowColor, other.alternatingRowColor, t),
      hoverRowColor: Color.lerp(hoverRowColor, other.hoverRowColor, t),
      selectedRowColor: Color.lerp(selectedRowColor, other.selectedRowColor, t),
      selectedRowForegroundColor: Color.lerp(selectedRowForegroundColor, other.selectedRowForegroundColor, t),
      focusedRowBorderColor: Color.lerp(focusedRowBorderColor, other.focusedRowBorderColor, t),
      borderWidth: lerpDouble(borderWidth, other.borderWidth, t),
      rowHeight: lerpDouble(rowHeight, other.rowHeight, t),
      headerHeight: lerpDouble(headerHeight, other.headerHeight, t),
      borderRadius: BorderRadius.lerp(borderRadius, other.borderRadius, t),
      sortIndicatorColor: Color.lerp(sortIndicatorColor, other.sortIndicatorColor, t),
      sortIndicatorSize: lerpDouble(sortIndicatorSize, other.sortIndicatorSize, t),
      checkboxTheme: CheckboxThemeData.lerp(checkboxTheme, other.checkboxTheme, t),
    );
  }

  /// Creates a default theme based on the Material color scheme and shadcn design tokens.
  /// 
  /// This factory method provides sensible defaults that align with shadcn table
  /// styling while integrating with Material Design principles.
  static ShadcnTableTheme defaultTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;
    
    return ShadcnTableTheme(
      backgroundColor: colorScheme.surface,
      borderColor: colorScheme.outline.withOpacity(0.2),
      headerBackgroundColor: isDark 
          ? colorScheme.surface.withOpacity(0.8)
          : colorScheme.surfaceVariant.withOpacity(0.3),
      headerForegroundColor: colorScheme.onSurface,
      headerTextStyle: const TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.05,
      ),
      headerPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing3,
        vertical: ShadcnTokens.spacing2,
      ),
      cellBackgroundColor: colorScheme.surface,
      cellForegroundColor: colorScheme.onSurface,
      cellTextStyle: const TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: FontWeight.w400,
      ),
      cellPadding: const EdgeInsets.symmetric(
        horizontal: ShadcnTokens.spacing3,
        vertical: ShadcnTokens.spacing2,
      ),
      alternatingRowColor: isDark 
          ? colorScheme.surface.withOpacity(0.4)
          : colorScheme.surfaceVariant.withOpacity(0.1),
      hoverRowColor: isDark 
          ? colorScheme.onSurface.withOpacity(0.05)
          : colorScheme.primary.withOpacity(0.05),
      selectedRowColor: colorScheme.primary.withOpacity(0.1),
      selectedRowForegroundColor: colorScheme.onSurface,
      focusedRowBorderColor: colorScheme.primary,
      borderWidth: 1.0,
      rowHeight: 48.0, // shadcn standard row height
      headerHeight: 52.0, // slightly taller for headers
      borderRadius: const BorderRadius.all(Radius.circular(ShadcnTokens.radiusMd)),
      sortIndicatorColor: colorScheme.onSurface.withOpacity(0.6),
      sortIndicatorSize: 16.0,
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return colorScheme.primary;
          }
          return Colors.transparent;
        }),
        checkColor: MaterialStateProperty.all(colorScheme.onPrimary),
        side: MaterialStateBorderSide.resolveWith((states) {
          return BorderSide(
            color: states.contains(MaterialState.selected) 
                ? colorScheme.primary 
                : colorScheme.outline,
            width: 1.5,
          );
        }),
      ),
    );
  }

  /// Resolves background color using the theme resolution chain
  Color resolveBackgroundColor(BuildContext context) {
    return resolveColor(
      context,
      backgroundColor,
      (theme) => theme.colorScheme.surface,
      Colors.white,
    );
  }

  /// Resolves border color using the theme resolution chain
  Color resolveBorderColor(BuildContext context) {
    return resolveColor(
      context,
      borderColor,
      (theme) => theme.colorScheme.outline.withOpacity(0.2),
      Colors.grey.withOpacity(0.2),
    );
  }

  /// Resolves header background color using the theme resolution chain
  Color resolveHeaderBackgroundColor(BuildContext context) {
    return resolveColor(
      context,
      headerBackgroundColor,
      (theme) => theme.colorScheme.surfaceVariant.withOpacity(0.3),
      Colors.grey.withOpacity(0.1),
    );
  }

  /// Resolves header text style using the theme resolution chain
  TextStyle resolveHeaderTextStyle(BuildContext context) {
    return resolveTextStyle(
      context,
      headerTextStyle,
      (textTheme) => textTheme.labelMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ) ?? const TextStyle(),
      const TextStyle(
        fontSize: ShadcnTokens.fontSizeSm,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// Resolves cell text style using the theme resolution chain
  TextStyle resolveCellTextStyle(BuildContext context) {
    return resolveTextStyle(
      context,
      cellTextStyle,
      (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
      const TextStyle(fontSize: ShadcnTokens.fontSizeSm),
    );
  }

  /// Resolves hover row color using the theme resolution chain
  Color resolveHoverRowColor(BuildContext context) {
    return resolveColor(
      context,
      hoverRowColor,
      (theme) => theme.colorScheme.primary.withOpacity(0.05),
      Colors.grey.withOpacity(0.05),
    );
  }

  /// Resolves selected row color using the theme resolution chain
  Color resolveSelectedRowColor(BuildContext context) {
    return resolveColor(
      context,
      selectedRowColor,
      (theme) => theme.colorScheme.primary.withOpacity(0.1),
      Colors.blue.withOpacity(0.1),
    );
  }

  @override
  bool validate({bool throwOnError = false}) {
    // Validate that essential properties have reasonable values
    final issues = <String>[];
    
    if (borderWidth != null && borderWidth! < 0) {
      issues.add('borderWidth cannot be negative');
    }
    
    if (rowHeight != null && rowHeight! <= 0) {
      issues.add('rowHeight must be positive');
    }
    
    if (headerHeight != null && headerHeight! <= 0) {
      issues.add('headerHeight must be positive');
    }
    
    if (sortIndicatorSize != null && sortIndicatorSize! <= 0) {
      issues.add('sortIndicatorSize must be positive');
    }
    
    if (issues.isNotEmpty && throwOnError) {
      throw FlutterError('ShadcnTableTheme validation failed: ${issues.join(', ')}');
    }
    
    return issues.isEmpty;
  }
}