import 'package:flutter/material.dart';
import '../../theme/extensions/shadcn_progress_theme.dart';
import '../../utils/theme_resolver.dart';
import '../shadcn_component.dart';

/// Variants for progress indicator display
enum ShadcnProgressVariant {
  /// Linear horizontal progress bar
  linear,
  
  /// Circular progress indicator
  circular,
}

/// A progress indicator component that follows shadcn design principles.
/// 
/// The [ShadcnProgress] widget displays progress with theme-aware styling
/// and smooth animations. It supports both linear and circular variants.
class ShadcnProgress extends ShadcnComponent {
  /// Current progress value (0.0 to 1.0)
  final double value;
  
  /// Progress variant (linear or circular)
  final ShadcnProgressVariant variant;
  
  /// Whether to show percentage text
  final bool showPercentage;
  
  /// Custom width for linear progress (null uses parent constraints)
  final double? width;
  
  /// Custom height for linear progress (overrides theme)
  final double? height;
  
  /// Custom size for circular progress (overrides theme)
  final double? size;
  
  /// Custom stroke width for circular progress (overrides theme)
  final double? strokeWidth;
  
  /// Custom progress color (overrides theme)
  final Color? progressColor;
  
  /// Custom background color (overrides theme)
  final Color? backgroundColor;
  
  /// Whether to animate progress changes
  final bool animate;
  
  /// Custom animation duration (overrides theme)
  final Duration? animationDuration;
  
  /// Accessibility label for the progress indicator
  final String? semanticLabel;

  const ShadcnProgress({
    super.key,
    required this.value,
    this.variant = ShadcnProgressVariant.linear,
    this.showPercentage = false,
    this.width,
    this.height,
    this.size,
    this.strokeWidth,
    this.progressColor,
    this.backgroundColor,
    this.animate = true,
    this.animationDuration,
    this.semanticLabel,
  }) : assert(value >= 0.0 && value <= 1.0, 'Progress value must be between 0.0 and 1.0');

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    final progressTheme = ShadcnThemeResolver.resolveThemeExtension<ShadcnProgressTheme>(
      context,
      ShadcnProgressTheme.defaultTheme,
    );

    final resolvedProgressColor = progressColor ?? progressTheme.progressColor ?? theme.colorScheme.primary;
    final resolvedBackgroundColor = backgroundColor ?? progressTheme.backgroundColor ?? 
        theme.colorScheme.surfaceVariant.withOpacity(0.2);
    final duration = animationDuration ?? progressTheme.animationDuration ?? 
        const Duration(milliseconds: 300);
    final curve = progressTheme.animationCurve ?? Curves.easeInOut;

    Widget progressWidget;
    
    switch (variant) {
      case ShadcnProgressVariant.linear:
        progressWidget = _buildLinearProgress(
          context,
          progressTheme,
          resolvedProgressColor,
          resolvedBackgroundColor,
          duration,
          curve,
        );
        break;
      case ShadcnProgressVariant.circular:
        progressWidget = _buildCircularProgress(
          context,
          progressTheme,
          resolvedProgressColor,
          resolvedBackgroundColor,
          duration,
          curve,
        );
        break;
    }

    // Add semantic label for accessibility
    return Semantics(
      label: semanticLabel ?? 'Progress ${(value * 100).round()}%',
      value: '${(value * 100).round()}%',
      child: progressWidget,
    );
  }

  Widget _buildLinearProgress(
    BuildContext context,
    ShadcnProgressTheme theme,
    Color progressColor,
    Color backgroundColor,
    Duration duration,
    Curve curve,
  ) {
    final resolvedHeight = height ?? theme.height ?? 8.0;
    final resolvedBorderRadius = theme.borderRadius ?? BorderRadius.circular(4.0);
    
    Widget progressBar = Container(
      width: width,
      height: resolvedHeight,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: resolvedBorderRadius,
      ),
      child: ClipRRect(
        borderRadius: resolvedBorderRadius,
        child: Stack(
          children: [
            if (animate)
              AnimatedContainer(
                duration: duration,
                curve: curve,
                width: width != null ? width! * value : null,
                height: resolvedHeight,
                decoration: BoxDecoration(
                  color: progressColor,
                  borderRadius: resolvedBorderRadius,
                ),
                alignment: Alignment.centerLeft,
                child: FractionallySizedBox(
                  widthFactor: width == null ? value : 1.0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: progressColor,
                      borderRadius: resolvedBorderRadius,
                    ),
                  ),
                ),
              )
            else
              Align(
                alignment: Alignment.centerLeft,
                child: FractionallySizedBox(
                  widthFactor: value,
                  child: Container(
                    height: resolvedHeight,
                    decoration: BoxDecoration(
                      color: progressColor,
                      borderRadius: resolvedBorderRadius,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );

    if (showPercentage) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          progressBar,
          const SizedBox(height: 4),
          Text(
            '${(value * 100).round()}%',
            style: theme.textStyle ?? Theme.of(context).textTheme.bodySmall,
          ),
        ],
      );
    }

    return progressBar;
  }

  Widget _buildCircularProgress(
    BuildContext context,
    ShadcnProgressTheme theme,
    Color progressColor,
    Color backgroundColor,
    Duration duration,
    Curve curve,
  ) {
    final resolvedSize = size ?? theme.size ?? 32.0;
    final resolvedStrokeWidth = strokeWidth ?? theme.strokeWidth ?? 3.0;

    Widget progressIndicator = SizedBox(
      width: resolvedSize,
      height: resolvedSize,
      child: Stack(
        children: [
          // Background circle
          CustomPaint(
            size: Size(resolvedSize, resolvedSize),
            painter: _CircularProgressPainter(
              progress: 1.0,
              color: backgroundColor,
              strokeWidth: resolvedStrokeWidth,
            ),
          ),
          // Progress circle
          if (animate)
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: value),
              duration: duration,
              curve: curve,
              builder: (context, animatedValue, child) {
                return CustomPaint(
                  size: Size(resolvedSize, resolvedSize),
                  painter: _CircularProgressPainter(
                    progress: animatedValue,
                    color: progressColor,
                    strokeWidth: resolvedStrokeWidth,
                  ),
                );
              },
            )
          else
            CustomPaint(
              size: Size(resolvedSize, resolvedSize),
              painter: _CircularProgressPainter(
                progress: value,
                color: progressColor,
                strokeWidth: resolvedStrokeWidth,
              ),
            ),
        ],
      ),
    );

    if (showPercentage) {
      return Stack(
        alignment: Alignment.center,
        children: [
          progressIndicator,
          Text(
            '${(value * 100).round()}%',
            style: theme.textStyle ?? Theme.of(context).textTheme.bodySmall,
          ),
        ],
      );
    }

    return progressIndicator;
  }
}

/// Custom painter for circular progress indicator
class _CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  _CircularProgressPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;
    
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    const startAngle = -math.pi / 2; // Start from top
    final sweepAngle = 2 * math.pi * progress;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant _CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.color != color ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}

// Import math for circular progress calculations
import 'dart:math' as math;