import 'package:flutter/material.dart';
import '../utils/theme_resolver.dart';

/// Abstract base class for all shadcn components.
/// 
/// This class provides a consistent interface and theme resolution pattern
/// for all shadcn components. It ensures proper theme inheritance and
/// provides validation for theme context requirements.
/// 
/// All shadcn components should extend this base class to ensure:
/// - Consistent theme resolution patterns
/// - Proper fallback handling for missing themes
/// - Built-in validation and debugging support
/// - Material Design integration
abstract class ShadcnComponent extends StatelessWidget {
  const ShadcnComponent({super.key});
  
  /// Resolves the component's theme extension using the theme resolver.
  /// 
  /// This method provides safe theme resolution with the following fallback chain:
  /// 1. Theme extension from current context
  /// 2. Default factory using Material ColorScheme
  /// 3. Emergency fallback using hardcoded Material theme
  /// 
  /// Example usage:
  /// ```dart
  /// final buttonTheme = resolveTheme<ShadcnButtonTheme>(
  ///   context,
  ///   ShadcnButtonTheme.defaultTheme,
  /// );
  /// ```
  T resolveTheme<T extends ThemeExtension<T>>(
    BuildContext context,
    T Function(ColorScheme) defaultFactory,
  ) {
    return ShadcnThemeResolver.resolveThemeExtension<T>(
      context,
      defaultFactory,
    );
  }
  
  /// Resolves colors using the shadcn theme-aware fallback chain.
  /// 
  /// This method ensures components always have valid colors by following
  /// the resolution order: custom color → Material theme color → fallback color.
  Color resolveColor(
    BuildContext context,
    Color? customColor,
    Color Function(ThemeData) materialColorGetter, [
    Color fallbackColor = Colors.grey,
  ]) {
    return ShadcnThemeResolver.resolveColor(
      context,
      customColor,
      materialColorGetter,
      fallbackColor,
    );
  }
  
  /// Resolves text styles with theme-aware merging.
  /// 
  /// Merges custom styles with Material base styles to maintain consistency
  /// while allowing customization.
  TextStyle resolveTextStyle(
    BuildContext context,
    TextStyle? customStyle,
    TextStyle Function(TextTheme) materialStyleGetter, [
    TextStyle? fallbackStyle,
  ]) {
    return ShadcnThemeResolver.resolveTextStyle(
      context,
      customStyle,
      materialStyleGetter,
      fallbackStyle,
    );
  }
  
  /// Resolves spacing with visual density adjustments.
  /// 
  /// Automatically adjusts spacing based on the theme's visual density
  /// to ensure proper accessibility and platform compliance.
  EdgeInsets resolveSpacing(
    BuildContext context,
    EdgeInsets? customSpacing,
    EdgeInsets defaultSpacing,
  ) {
    return ShadcnThemeResolver.resolveSpacing(
      context,
      customSpacing,
      defaultSpacing,
    );
  }
  
  /// Resolves double values with visual density adjustments.
  /// 
  /// Applies visual density adjustments to dimensions like heights and widths
  /// to maintain proper spacing and sizing across different screen densities.
  double resolveDouble(
    BuildContext context,
    double? customValue,
    double defaultValue, {
    bool applyVerticalDensity = true,
  }) {
    return ShadcnThemeResolver.resolveDouble(
      context,
      customValue,
      defaultValue,
      applyVerticalDensity: applyVerticalDensity,
    );
  }
  
  /// Resolves border radius with theme consistency.
  /// 
  /// Ensures border radius values are consistent with the overall theme
  /// while allowing component-specific customization.
  BorderRadius resolveBorderRadius(
    BuildContext context,
    BorderRadius? customRadius,
    BorderRadius defaultRadius,
  ) {
    return ShadcnThemeResolver.resolveBorderRadius(
      context,
      customRadius,
      defaultRadius,
    );
  }
  
  /// Builds the component with the resolved theme data.
  /// 
  /// Subclasses must implement this method to build their UI using the
  /// provided theme data. The theme is guaranteed to be valid by the time
  /// this method is called.
  /// 
  /// The method receives both the BuildContext and resolved ThemeData
  /// for maximum flexibility in theme-aware component construction.
  Widget buildWithTheme(BuildContext context, ThemeData theme);
  
  @override
  Widget build(BuildContext context) {
    // Validate theme context in debug mode
    assert(() {
      ShadcnThemeResolver.validateThemeContext(context);
      return true;
    }());
    
    final theme = Theme.of(context);
    return buildWithTheme(context, theme);
  }
}

/// Mixin for component validation and debugging support.
/// 
/// This mixin provides comprehensive validation methods and debugging utilities
/// that can be used by shadcn components to ensure proper configuration
/// and provide helpful error messages during development.
/// 
/// Components can use this mixin to:
/// - Validate theme properties and configuration
/// - Debug theme resolution issues
/// - Ensure required properties are provided
/// - Validate component state and configuration
mixin ShadcnComponentValidation {
  /// Validates that the theme context is properly configured.
  /// 
  /// This method performs comprehensive theme validation including:
  /// - Theme data availability
  /// - ColorScheme completeness
  /// - TextTheme availability
  /// - Visual density configuration
  void validateThemeProperties(BuildContext context) {
    assert(() {
      try {
        ShadcnThemeResolver.validateCompleteTheme(context, throwOnError: true);
        return true;
      } catch (e) {
        debugPrint('Theme validation failed: $e');
        // Re-throw in debug mode to help developers identify issues
        throw FlutterError(
          'ShadcnComponent theme validation failed: $e\n'
          'Ensure your widget tree includes a properly configured Theme widget.',
        );
      }
    }());
  }
  
  /// Validates that required properties are not null.
  /// 
  /// This method checks a map of property names to values and ensures
  /// that all required properties have non-null values. Throws descriptive
  /// errors in debug mode to help identify missing configurations.
  void validateRequiredProperties(Map<String, dynamic> properties) {
    assert(() {
      final nullProperties = <String>[];
      
      for (final entry in properties.entries) {
        if (entry.value == null) {
          nullProperties.add(entry.key);
        }
      }
      
      if (nullProperties.isNotEmpty) {
        throw FlutterError(
          'Required properties cannot be null: ${nullProperties.join(', ')}\n'
          'Ensure you provide valid values or check theme configuration.\n'
          'This error indicates that essential component properties are missing.',
        );
      }
      
      return true;
    }());
  }
  
  /// Validates component-specific theme extension.
  /// 
  /// Ensures that a specific theme extension is properly configured
  /// and contains all required properties for the component.
  void validateThemeExtension<T extends ThemeExtension<T>>(
    BuildContext context,
    List<String> requiredProperties, [
    String? componentName,
  ]) {
    assert(() {
      try {
        final theme = Theme.of(context);
        final extension = theme.extension<T>();
        
        if (extension == null) {
          final name = componentName ?? T.toString();
          debugPrint(
            'Warning: Theme extension $T not found for $name. '
            'Using default theme values.',
          );
        }
        
        // Additional validation could be added here if theme extensions
        // implement a validation interface
        return true;
      } catch (e) {
        final name = componentName ?? T.toString();
        throw FlutterError(
          'Theme extension validation failed for $name: $e\n'
          'Ensure the theme extension $T is properly configured.',
        );
      }
    }());
  }
  
  /// Validates component variant configuration.
  /// 
  /// Ensures that variant values are valid and supported by the component.
  void validateVariant<T extends Enum>(
    T variant,
    List<T> supportedVariants,
    String componentName,
  ) {
    assert(() {
      if (!supportedVariants.contains(variant)) {
        throw FlutterError(
          'Invalid variant "$variant" for $componentName.\n'
          'Supported variants: ${supportedVariants.join(', ')}',
        );
      }
      return true;
    }());
  }
  
  /// Validates size configuration.
  /// 
  /// Ensures that size values are valid and within reasonable bounds.
  void validateSize({
    double? width,
    double? height,
    String componentName = 'Component',
  }) {
    assert(() {
      if (width != null && (width < 0 || width > 10000)) {
        throw FlutterError(
          'Invalid width "$width" for $componentName. '
          'Width must be between 0 and 10000.',
        );
      }
      
      if (height != null && (height < 0 || height > 10000)) {
        throw FlutterError(
          'Invalid height "$height" for $componentName. '
          'Height must be between 0 and 10000.',
        );
      }
      
      return true;
    }());
  }
  
  /// Validates callback configuration.
  /// 
  /// Ensures that interactive components have proper callback configuration.
  void validateCallbacks(
    Map<String, VoidCallback?> callbacks,
    String componentName, {
    bool requireAtLeastOne = false,
  }) {
    assert(() {
      if (requireAtLeastOne) {
        final hasCallback = callbacks.values.any((callback) => callback != null);
        if (!hasCallback) {
          debugPrint(
            'Warning: Interactive component $componentName has no callbacks. '
            'Consider providing at least one interaction handler.',
          );
        }
      }
      
      return true;
    }());
  }
  
  /// Provides debugging information about the component's theme resolution.
  /// 
  /// This method outputs detailed debugging information including:
  /// - Theme configuration
  /// - Color scheme details
  /// - Text theme information
  /// - Visual density settings
  /// - Component-specific details
  void debugThemeResolution(
    BuildContext context,
    String componentName, [
    Map<String, dynamic>? additionalInfo,
  ]) {
    assert(() {
      debugPrint('=== $componentName Theme Resolution Debug ===');
      
      try {
        final theme = Theme.of(context);
        
        // Basic theme information
        debugPrint('Theme brightness: ${theme.brightness}');
        debugPrint('Platform: ${theme.platform}');
        debugPrint('Material version: ${theme.useMaterial3 ? "Material 3" : "Material 2"}');
        debugPrint('Visual density: ${theme.visualDensity}');
        
        // Color scheme details
        final colorScheme = theme.colorScheme;
        debugPrint('Color scheme brightness: ${colorScheme.brightness}');
        debugPrint('Primary color: ${colorScheme.primary}');
        debugPrint('Secondary color: ${colorScheme.secondary}');
        debugPrint('Surface color: ${colorScheme.surface}');
        debugPrint('Error color: ${colorScheme.error}');
        
        // Text theme details
        final textTheme = theme.textTheme;
        debugPrint('Body medium style: ${textTheme.bodyMedium}');
        debugPrint('Label large style: ${textTheme.labelLarge}');
        
        // Extension information
        debugPrint('Theme extensions: ${theme.extensions.keys.toList()}');
        
        if (additionalInfo != null && additionalInfo.isNotEmpty) {
          debugPrint('Component-specific information:');
          for (final entry in additionalInfo.entries) {
            debugPrint('  ${entry.key}: ${entry.value}');
          }
        }
      } catch (e) {
        debugPrint('Error during theme debugging: $e');
      }
      
      debugPrint('=== End $componentName Theme Resolution Debug ===');
      return true;
    }());
  }
  
  /// Provides debugging information about component state.
  /// 
  /// Outputs information about the component's current configuration
  /// and state to help with debugging and development.
  void debugComponentState(
    String componentName,
    Map<String, dynamic> state,
  ) {
    assert(() {
      debugPrint('=== $componentName State Debug ===');
      
      for (final entry in state.entries) {
        debugPrint('${entry.key}: ${entry.value} (${entry.value.runtimeType})');
      }
      
      debugPrint('=== End $componentName State Debug ===');
      return true;
    }());
  }
  
  /// Validates accessibility configuration.
  /// 
  /// Ensures that components have proper accessibility support configured.
  void validateAccessibility({
    String? semanticLabel,
    String? tooltip,
    bool? excludeFromSemantics,
    String componentName = 'Component',
  }) {
    assert(() {
      if (semanticLabel?.isEmpty == true) {
        debugPrint(
          'Warning: Empty semantic label for $componentName. '
          'Consider providing a meaningful description for accessibility.',
        );
      }
      
      if (tooltip?.isEmpty == true) {
        debugPrint(
          'Warning: Empty tooltip for $componentName. '
          'Consider providing a helpful tooltip for user guidance.',
        );
      }
      
      if (excludeFromSemantics == true) {
        debugPrint(
          'Info: $componentName is excluded from semantics. '
          'Ensure this is intentional and doesn\'t harm accessibility.',
        );
      }
      
      return true;
    }());
  }
}

/// Helper class for building consistent component UI patterns.
/// 
/// This class provides common UI building methods that maintain consistency
/// across all shadcn components while reducing code duplication. It follows
/// Material Design principles while providing shadcn-specific styling patterns.
/// 
/// All methods in this class are designed to work with the shadcn theme system
/// and provide proper fallback handling for missing theme configurations.
class ShadcnComponentBuilder {
  ShadcnComponentBuilder._();
  
  /// Builds a styled container with theme-aware properties.
  /// 
  /// This method creates a container with consistent styling patterns
  /// that integrates with the shadcn theme system. It automatically
  /// handles visual density adjustments and Material Design compliance.
  static Widget styledContainer({
    required BuildContext context,
    required Widget child,
    Color? backgroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    double? width,
    double? height,
    BoxConstraints? constraints,
    List<BoxShadow>? boxShadow,
    Gradient? gradient,
    double? borderWidth,
  }) {
    // Apply visual density adjustments to padding if provided
    final adjustedPadding = padding != null
        ? ShadcnThemeResolver.resolveSpacing(context, padding, padding)
        : null;
    
    // Apply visual density adjustments to dimensions
    final adjustedWidth = width != null
        ? ShadcnThemeResolver.resolveDouble(context, width, width, applyVerticalDensity: false)
        : null;
    final adjustedHeight = height != null
        ? ShadcnThemeResolver.resolveDouble(context, height, height)
        : null;
    
    return Container(
      width: adjustedWidth,
      height: adjustedHeight,
      constraints: constraints,
      padding: adjustedPadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        gradient: gradient,
        border: borderColor != null 
          ? Border.all(
              color: borderColor, 
              width: borderWidth ?? 1.0,
            ) 
          : null,
        borderRadius: borderRadius,
        boxShadow: boxShadow,
      ),
      child: child,
    );
  }
  
  /// Builds an interactive widget with proper Material design patterns.
  /// 
  /// This method creates interactive widgets that follow Material Design
  /// principles while supporting shadcn-specific styling. It handles
  /// all interaction states and provides proper accessibility support.
  static Widget interactiveWidget({
    required BuildContext context,
    required Widget child,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    VoidCallback? onDoubleTap,
    BorderRadius? borderRadius,
    Color? splashColor,
    Color? highlightColor,
    Color? hoverColor,
    bool enableFeedback = true,
    bool excludeFromSemantics = false,
    String? semanticLabel,
    String? tooltip,
    MouseCursor? mouseCursor,
  }) {
    // If no interaction callbacks are provided, return child as-is
    if (onTap == null && onLongPress == null && onDoubleTap == null) {
      return child;
    }
    
    Widget result = Material(
      type: MaterialType.transparency,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        onDoubleTap: onDoubleTap,
        borderRadius: borderRadius,
        splashColor: splashColor,
        highlightColor: highlightColor,
        hoverColor: hoverColor,
        enableFeedback: enableFeedback,
        excludeFromSemantics: excludeFromSemantics,
        mouseCursor: mouseCursor ?? SystemMouseCursors.click,
        child: child,
      ),
    );
    
    // Add semantic label if provided
    if (semanticLabel != null) {
      result = Semantics(
        label: semanticLabel,
        button: onTap != null,
        child: result,
      );
    }
    
    // Add tooltip if provided
    if (tooltip != null) {
      result = Tooltip(
        message: tooltip,
        child: result,
      );
    }
    
    return result;
  }
  
  /// Builds a focus-aware widget with proper focus indicators.
  /// 
  /// This method creates widgets that properly handle focus states
  /// and keyboard navigation, essential for accessibility compliance.
  static Widget focusableWidget({
    required BuildContext context,
    required Widget child,
    FocusNode? focusNode,
    bool autofocus = false,
    ValueChanged<bool>? onFocusChange,
    bool canRequestFocus = true,
    bool skipTraversal = false,
    String? debugLabel,
  }) {
    return Focus(
      focusNode: focusNode,
      autofocus: autofocus,
      onFocusChange: onFocusChange,
      canRequestFocus: canRequestFocus,
      skipTraversal: skipTraversal,
      debugLabel: debugLabel,
      child: child,
    );
  }
  
  /// Builds an animated widget with theme-consistent transitions.
  /// 
  /// This method provides animated widgets that use theme-consistent
  /// durations and curves for smooth, cohesive animations.
  static Widget animatedWidget({
    required BuildContext context,
    required Widget child,
    Duration? duration,
    Curve curve = Curves.easeInOut,
  }) {
    // Use provided duration or default to 200ms for consistent animations
    final animationDuration = duration ?? const Duration(milliseconds: 200);
    
    return AnimatedSwitcher(
      duration: animationDuration,
      switchInCurve: curve,
      switchOutCurve: curve,
      child: child,
    );
  }
  
  /// Builds a state-aware widget that responds to interaction states.
  /// 
  /// This method creates widgets that automatically handle different
  /// visual states like hover, focus, pressed, and disabled.
  static Widget stateAwareWidget({
    required BuildContext context,
    required Widget Function(BuildContext context, Set<WidgetState> states) builder,
    VoidCallback? onTap,
    FocusNode? focusNode,
    bool enabled = true,
    bool autofocus = false,
    BorderRadius? borderRadius,
    MouseCursor? mouseCursor,
  }) {
    return Builder(
      builder: (context) {
        final states = <WidgetState>{};
        
        if (!enabled) {
          states.add(WidgetState.disabled);
        }
        
        return Focus(
          focusNode: focusNode,
          autofocus: autofocus,
          child: MouseRegion(
            cursor: mouseCursor ?? (enabled ? SystemMouseCursors.click : SystemMouseCursors.basic),
            child: GestureDetector(
              onTap: enabled ? onTap : null,
              child: builder(context, states),
            ),
          ),
        );
      },
    );
  }
  
  /// Builds a widget with proper loading state handling.
  /// 
  /// This method creates widgets that can display loading states
  /// with theme-consistent loading indicators.
  static Widget loadingStateWidget({
    required BuildContext context,
    required Widget child,
    bool isLoading = false,
    Widget? loadingIndicator,
    String? loadingText,
    double opacity = 0.6,
  }) {
    if (!isLoading) {
      return child;
    }
    
    final theme = Theme.of(context);
    final defaultIndicator = SizedBox(
      width: 16,
      height: 16,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          theme.colorScheme.primary,
        ),
      ),
    );
    
    return Stack(
      children: [
        Opacity(
          opacity: opacity,
          child: AbsorbPointer(
            absorbing: true,
            child: child,
          ),
        ),
        Positioned.fill(
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                loadingIndicator ?? defaultIndicator,
                if (loadingText != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    loadingText,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  /// Builds a widget with error state handling.
  /// 
  /// This method creates widgets that can display error states
  /// with theme-consistent error styling and user feedback.
  static Widget errorStateWidget({
    required BuildContext context,
    required Widget child,
    String? errorMessage,
    Widget? errorIcon,
    VoidCallback? onRetry,
    bool hasError = false,
  }) {
    if (!hasError || errorMessage == null) {
      return child;
    }
    
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          errorIcon ?? 
            Icon(
              Icons.error_outline,
              color: theme.colorScheme.error,
              size: 24,
            ),
          const SizedBox(height: 8),
          Text(
            errorMessage,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 16),
            TextButton(
              onPressed: onRetry,
              child: const Text('Retry'),
            ),
          ],
        ],
      ),
    );
  }
  
  /// Builds a responsive widget that adapts to screen size.
  /// 
  /// This method creates widgets that automatically adapt their
  /// layout based on the available screen space and platform conventions.
  static Widget responsiveWidget({
    required BuildContext context,
    required Widget mobile,
    Widget? tablet,
    Widget? desktop,
    double mobileBreakpoint = 600,
    double tabletBreakpoint = 1024,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        if (width >= tabletBreakpoint && desktop != null) {
          return desktop;
        } else if (width >= mobileBreakpoint && tablet != null) {
          return tablet;
        } else {
          return mobile;
        }
      },
    );
  }
  
  /// Creates a consistent Material surface with shadcn styling.
  /// 
  /// This method provides a standardized way to create surfaces
  /// that follow both Material Design and shadcn design principles.
  static Widget shadcnSurface({
    required BuildContext context,
    required Widget child,
    double? elevation,
    Color? surfaceColor,
    Color? shadowColor,
    BorderRadius? borderRadius,
    EdgeInsets? padding,
    Clip clipBehavior = Clip.none,
  }) {
    final theme = Theme.of(context);
    
    return Material(
      elevation: elevation ?? 0,
      color: surfaceColor ?? theme.colorScheme.surface,
      shadowColor: shadowColor ?? theme.colorScheme.shadow,
      borderRadius: borderRadius,
      clipBehavior: clipBehavior,
      child: padding != null
        ? Padding(
            padding: ShadcnThemeResolver.resolveSpacing(context, padding, padding),
            child: child,
          )
        : child,
    );
  }
}