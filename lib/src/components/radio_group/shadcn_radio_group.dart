import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../shadcn_component.dart';
import '../../theme/extensions/shadcn_radio_group_theme.dart';
import '../../constants/shadcn_tokens.dart';

/// A shadcn-styled radio group component with comprehensive theming support.
/// 
/// This radio group component provides radio button selection functionality
/// while maintaining full Material Design integration. It supports all interactive
/// states (hover, pressed, focused, disabled) with theme-aware styling.
/// 
/// The radio group automatically derives its styling from the current theme context,
/// with fallback to Material Design values. All styling is customizable through
/// the [ShadcnRadioGroupTheme] extension.
/// 
/// Example usage:
/// ```dart
/// ShadcnRadioGroup<String>(
///   value: selectedOption,
///   onChanged: (value) => setState(() => selectedOption = value),
///   items: [
///     ShadcnRadioGroupItem(value: 'option1', label: 'Option 1'),
///     ShadcnRadioGroupItem(value: 'option2', label: 'Option 2'),
///     ShadcnRadioGroupItem(value: 'option3', label: 'Option 3'),
///   ],
/// )
/// ```
class ShadcnRadioGroup<T> extends ShadcnComponent with ShadcnComponentValidation {
  /// The current selected value.
  final T? value;
  
  /// Callback function called when the selection changes.
  final ValueChanged<T?>? onChanged;
  
  /// List of radio group items to display.
  final List<ShadcnRadioGroupItem<T>> items;
  
  /// Helper text displayed below the radio group.
  final String? helperText;
  
  /// Error text displayed when validation fails.
  final String? errorText;
  
  /// The size of the radio buttons.
  final ShadcnRadioGroupSize size;
  
  /// The layout direction of the radio group.
  final ShadcnRadioGroupDirection direction;
  
  /// Custom spacing between radio items.
  final double? itemSpacing;
  
  /// Custom padding around the radio group.
  final EdgeInsets? padding;
  
  /// Focus node for keyboard navigation.
  final FocusNode? focusNode;
  
  /// Whether the radio group should autofocus when first built.
  final bool autofocus;
  
  /// Tooltip message displayed on hover.
  final String? tooltip;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;
  
  /// Whether to exclude this radio group from semantics tree.
  final bool excludeFromSemantics;
  
  /// Whether to enable haptic feedback on selection.
  final bool enableFeedback;
  
  /// Animation duration for state transitions.
  final Duration? animationDuration;
  
  /// Form field validator function.
  final FormFieldValidator<T>? validator;
  
  /// Whether this radio group is part of a form and should save its value.
  final bool autovalidate;
  
  /// Initial value for form field.
  final T? initialValue;
  
  /// Form field key for accessing the field state.
  final GlobalKey<FormFieldState<T>>? formFieldKey;
  
  /// Custom wrap alignment for wrap direction.
  final WrapAlignment? wrapAlignment;
  
  /// Custom wrap cross alignment for wrap direction.
  final WrapCrossAlignment? wrapCrossAlignment;
  
  /// Custom run spacing for wrap direction.
  final double? runSpacing;

  const ShadcnRadioGroup({
    super.key,
    required this.value,
    required this.onChanged,
    required this.items,
    this.helperText,
    this.errorText,
    this.size = ShadcnRadioGroupSize.medium,
    this.direction = ShadcnRadioGroupDirection.vertical,
    this.itemSpacing,
    this.padding,
    this.focusNode,
    this.autofocus = false,
    this.tooltip,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.enableFeedback = true,
    this.animationDuration,
    this.validator,
    this.autovalidate = false,
    this.initialValue,
    this.formFieldKey,
    this.wrapAlignment,
    this.wrapCrossAlignment,
    this.runSpacing,
  }) : assert(items.isNotEmpty, 'Items cannot be empty');

  /// Factory constructor for form field radio group with validation support.
  factory ShadcnRadioGroup.formField({
    Key? key,
    T? initialValue,
    required ValueChanged<T?>? onChanged,
    required List<ShadcnRadioGroupItem<T>> items,
    FormFieldValidator<T>? validator,
    bool autovalidate = false,
    GlobalKey<FormFieldState<T>>? formFieldKey,
    String? helperText,
    ShadcnRadioGroupSize size = ShadcnRadioGroupSize.medium,
    ShadcnRadioGroupDirection direction = ShadcnRadioGroupDirection.vertical,
    String? tooltip,
  }) {
    return ShadcnRadioGroup<T>(
      key: key,
      value: initialValue,
      onChanged: onChanged,
      items: items,
      validator: validator,
      autovalidate: autovalidate,
      formFieldKey: formFieldKey,
      initialValue: initialValue,
      helperText: helperText,
      size: size,
      direction: direction,
      tooltip: tooltip,
    );
  }

  /// Whether the radio group is enabled (has onChanged callback and enabled items).
  bool get enabled => onChanged != null && items.any((item) => item.enabled);

  @override
  Widget buildWithTheme(BuildContext context, ThemeData theme) {
    // Validate theme context and properties
    validateThemeProperties(context);
    validateVariant(size, ShadcnRadioGroupSize.values, 'ShadcnRadioGroup');
    validateVariant(direction, ShadcnRadioGroupDirection.values, 'ShadcnRadioGroup');
    validateCallbacks({'onChanged': onChanged}, 'ShadcnRadioGroup');
    validateAccessibility(
      semanticLabel: semanticLabel,
      tooltip: tooltip,
      excludeFromSemantics: excludeFromSemantics,
      componentName: 'ShadcnRadioGroup',
    );

    // Resolve radio group theme
    final radioGroupTheme = resolveTheme<ShadcnRadioGroupTheme>(
      context,
      ShadcnRadioGroupTheme.defaultTheme,
    );

    // If this is a form field, wrap with FormField
    if (validator != null || formFieldKey != null) {
      return _buildFormField(context, theme, radioGroupTheme);
    }

    return _buildRadioGroup(context, theme, radioGroupTheme);
  }

  /// Builds the form field wrapper if validation is needed.
  Widget _buildFormField(BuildContext context, ThemeData theme, ShadcnRadioGroupTheme radioGroupTheme) {
    return FormField<T>(
      key: formFieldKey,
      initialValue: initialValue ?? value,
      validator: validator,
      autovalidateMode: autovalidate ? AutovalidateMode.always : AutovalidateMode.disabled,
      builder: (FormFieldState<T> field) {
        final hasError = field.hasError;
        final currentValue = field.value;
        
        return _buildRadioGroup(
          context,
          theme,
          radioGroupTheme,
          formFieldState: field,
          hasError: hasError,
          currentValue: currentValue,
        );
      },
    );
  }

  /// Builds the main radio group widget.
  Widget _buildRadioGroup(
    BuildContext context,
    ThemeData theme,
    ShadcnRadioGroupTheme radioGroupTheme, {
    FormFieldState<T>? formFieldState,
    bool hasError = false,
    T? currentValue,
  }) {
    final effectiveValue = currentValue ?? value;
    final effectiveErrorText = hasError ? (formFieldState?.errorText ?? errorText) : errorText;

    // Build the radio buttons
    final radioButtons = _buildRadioButtons(
      context,
      theme,
      radioGroupTheme,
      effectiveValue,
      formFieldState,
      hasError,
    );

    // Build the complete widget with helper text
    return _buildCompleteWidget(
      context,
      theme,
      radioGroupTheme,
      radioButtons,
      effectiveErrorText,
      hasError,
    );
  }

  /// Builds the radio buttons based on the direction.
  Widget _buildRadioButtons(
    BuildContext context,
    ThemeData theme,
    ShadcnRadioGroupTheme radioGroupTheme,
    T? currentValue,
    FormFieldState<T>? formFieldState,
    bool hasError,
  ) {
    final effectivePadding = padding ?? radioGroupTheme.padding ?? EdgeInsets.all(ShadcnTokens.spacing1);
    final effectiveItemSpacing = itemSpacing ?? radioGroupTheme.itemSpacing ?? ShadcnTokens.spacing4;

    final radioWidgets = items.map((item) {
      return _buildRadioItem(
        context,
        theme,
        radioGroupTheme,
        item,
        currentValue,
        formFieldState,
        hasError,
      );
    }).toList();

    Widget radioGroup;

    switch (direction) {
      case ShadcnRadioGroupDirection.vertical:
        radioGroup = Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: radioGroupTheme.crossAxisAlignment ?? CrossAxisAlignment.start,
          children: _intersperse(
            radioWidgets,
            SizedBox(height: effectiveItemSpacing),
          ),
        );
        break;
      case ShadcnRadioGroupDirection.horizontal:
        radioGroup = SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: radioGroupTheme.mainAxisAlignment ?? MainAxisAlignment.start,
            children: _intersperse(
              radioWidgets,
              SizedBox(width: effectiveItemSpacing),
            ),
          ),
        );
        break;
      case ShadcnRadioGroupDirection.wrap:
        radioGroup = Wrap(
          alignment: wrapAlignment ?? radioGroupTheme.wrapAlignment ?? WrapAlignment.start,
          crossAxisAlignment: wrapCrossAlignment ?? radioGroupTheme.wrapCrossAlignment ?? WrapCrossAlignment.center,
          spacing: effectiveItemSpacing,
          runSpacing: runSpacing ?? radioGroupTheme.runSpacing ?? ShadcnTokens.spacing2,
          children: radioWidgets,
        );
        break;
    }

    return Padding(
      padding: effectivePadding,
      child: radioGroup,
    );
  }

  /// Builds a single radio item.
  Widget _buildRadioItem(
    BuildContext context,
    ThemeData theme,
    ShadcnRadioGroupTheme radioGroupTheme,
    ShadcnRadioGroupItem<T> item,
    T? currentValue,
    FormFieldState<T>? formFieldState,
    bool hasError,
  ) {
    final isSelected = currentValue == item.value;
    final isEnabled = enabled && item.enabled;

    // Resolve colors based on current state
    final colors = _resolveColors(radioGroupTheme, theme.colorScheme, isSelected, hasError, isEnabled);
    final sizeProperties = _resolveSizeProperties(radioGroupTheme, size);

    final radioButton = GestureDetector(
      onTap: isEnabled ? () => _handleTap(context, item.value, formFieldState) : null,
      child: MouseRegion(
        cursor: isEnabled ? SystemMouseCursors.click : SystemMouseCursors.basic,
        child: AnimatedContainer(
          duration: animationDuration ?? radioGroupTheme.animationDuration ?? ShadcnTokens.durationFast,
          curve: radioGroupTheme.animationCurve ?? Curves.easeInOut,
          width: sizeProperties.size,
          height: sizeProperties.size,
          decoration: BoxDecoration(
            color: colors.background,
            border: Border.all(
              color: colors.border,
              width: radioGroupTheme.borderWidth ?? ShadcnTokens.borderWidth,
            ),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: AnimatedContainer(
              duration: animationDuration ?? radioGroupTheme.animationDuration ?? ShadcnTokens.durationFast,
              curve: radioGroupTheme.animationCurve ?? Curves.easeInOut,
              width: isSelected ? sizeProperties.innerCircleSize : 0,
              height: isSelected ? sizeProperties.innerCircleSize : 0,
              decoration: BoxDecoration(
                color: colors.innerCircle,
                shape: BoxShape.circle,
              ),
            ),
          ),
        ),
      ),
    );

    // Build the complete item with label
    final List<Widget> itemChildren = [radioButton];

    if (item.child != null || (item.label != null && item.label!.isNotEmpty)) {
      itemChildren.add(SizedBox(width: radioGroupTheme.labelSpacing ?? ShadcnTokens.spacing2));
      
      Widget labelWidget = item.child ?? Text(
        item.label!,
        style: resolveTextStyle(
          context,
          radioGroupTheme.labelStyle?.copyWith(
            color: isEnabled ? radioGroupTheme.labelStyle?.color : theme.colorScheme.onSurface.withOpacity(0.38),
          ),
          (textTheme) => textTheme.bodyMedium ?? const TextStyle(),
        ),
      );

      if (isEnabled) {
        labelWidget = GestureDetector(
          onTap: () => _handleTap(context, item.value, formFieldState),
          child: labelWidget,
        );
      }

      itemChildren.add(Expanded(child: labelWidget));
    }

    Widget result = Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: itemChildren,
    );

    // Add semantics
    if (!excludeFromSemantics) {
      result = Semantics(
        label: item.semanticLabel ?? item.label,
        inMutuallyExclusiveGroup: true,
        checked: isSelected,
        enabled: isEnabled,
        focusable: isEnabled,
        child: result,
      );
    }

    // Add tooltip if provided
    if (item.tooltip != null) {
      result = Tooltip(
        message: item.tooltip!,
        child: result,
      );
    }

    return result;
  }

  /// Builds the complete widget with helper text and error text.
  Widget _buildCompleteWidget(
    BuildContext context,
    ThemeData theme,
    ShadcnRadioGroupTheme radioGroupTheme,
    Widget radioButtons,
    String? errorText,
    bool hasError,
  ) {
    final List<Widget> children = [radioButtons];

    // Add helper text
    if (helperText != null && helperText!.isNotEmpty && !hasError) {
      children.add(const SizedBox(height: ShadcnTokens.spacing1));
      children.add(
        Text(
          helperText!,
          style: resolveTextStyle(
            context,
            radioGroupTheme.helperStyle,
            (textTheme) => textTheme.bodySmall ?? const TextStyle(),
          ),
        ),
      );
    }

    // Add error text
    if (errorText != null && errorText!.isNotEmpty) {
      children.add(const SizedBox(height: ShadcnTokens.spacing1));
      children.add(
        Text(
          errorText!,
          style: resolveTextStyle(
            context,
            radioGroupTheme.errorStyle,
            (textTheme) => textTheme.bodySmall?.copyWith(color: theme.colorScheme.error) ?? TextStyle(color: theme.colorScheme.error),
          ),
        ),
      );
    }

    Widget result = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );

    // Add focus support
    if (focusNode != null) {
      result = Focus(
        focusNode: focusNode,
        autofocus: autofocus,
        child: result,
      );
    }

    // Add semantics for the group
    if (!excludeFromSemantics) {
      result = Semantics(
        label: semanticLabel,
        child: result,
      );
    }

    // Add tooltip for the group
    if (tooltip != null) {
      result = Tooltip(
        message: tooltip!,
        child: result,
      );
    }

    return result;
  }

  /// Resolves colors for the current state.
  _RadioGroupColors _resolveColors(
    ShadcnRadioGroupTheme theme,
    ColorScheme colorScheme,
    bool isSelected,
    bool hasError,
    bool isEnabled,
  ) {
    if (!isEnabled) {
      // Disabled state
      if (isSelected) {
        return _RadioGroupColors(
          background: theme.disabledSelectedBackground ?? Colors.transparent,
          border: theme.disabledSelectedBorder ?? colorScheme.onSurface.withOpacity(0.12),
          innerCircle: theme.disabledSelectedInnerCircle ?? colorScheme.onSurface.withOpacity(0.38),
        );
      } else {
        return _RadioGroupColors(
          background: theme.disabledUnselectedBackground ?? Colors.transparent,
          border: theme.disabledUnselectedBorder ?? colorScheme.onSurface.withOpacity(0.12),
          innerCircle: Colors.transparent,
        );
      }
    } else if (hasError) {
      // Error state
      return _RadioGroupColors(
        background: isSelected ? Colors.transparent : Colors.transparent,
        border: colorScheme.error,
        innerCircle: isSelected ? colorScheme.error : Colors.transparent,
      );
    } else if (isSelected) {
      // Selected state
      return _RadioGroupColors(
        background: theme.selectedBackground ?? Colors.transparent,
        border: theme.selectedBorder ?? colorScheme.primary,
        innerCircle: theme.selectedInnerCircle ?? colorScheme.primary,
      );
    } else {
      // Unselected state
      return _RadioGroupColors(
        background: theme.unselectedBackground ?? Colors.transparent,
        border: theme.unselectedBorder ?? colorScheme.outline,
        innerCircle: Colors.transparent,
      );
    }
  }

  /// Resolves size properties for the current size.
  _RadioGroupSizeProperties _resolveSizeProperties(ShadcnRadioGroupTheme theme, ShadcnRadioGroupSize size) {
    return _RadioGroupSizeProperties(
      size: theme.resolveSizeForVariant(size),
      innerCircleSize: theme.resolveInnerCircleSizeForVariant(size),
    );
  }

  /// Handles radio button tap with haptic feedback.
  void _handleTap(BuildContext context, T value, FormFieldState<T>? formFieldState) {
    if (enableFeedback) {
      HapticFeedback.lightImpact();
    }

    // Update form field if present
    formFieldState?.didChange(value);
    
    // Call the onChanged callback
    onChanged?.call(value);
  }

  /// Helper method to intersperse widgets with separators.
  List<Widget> _intersperse<W extends Widget>(List<W> list, Widget separator) {
    if (list.isEmpty) return [];
    
    final result = <Widget>[list.first];
    for (int i = 1; i < list.length; i++) {
      result.add(separator);
      result.add(list[i]);
    }
    return result;
  }
}

/// Represents a single radio group item.
class ShadcnRadioGroupItem<T> {
  /// The value associated with this radio item.
  final T value;
  
  /// The text label displayed next to the radio button.
  final String? label;
  
  /// The child widget to display instead of [label]. Takes precedence over [label].
  final Widget? child;
  
  /// Whether this item is enabled.
  final bool enabled;
  
  /// Tooltip message displayed on hover.
  final String? tooltip;
  
  /// Semantic label for accessibility.
  final String? semanticLabel;

  const ShadcnRadioGroupItem({
    required this.value,
    this.label,
    this.child,
    this.enabled = true,
    this.tooltip,
    this.semanticLabel,
  }) : assert(label != null || child != null, 'Either label or child must be provided');
}

/// Helper class to hold resolved radio group colors for different states.
class _RadioGroupColors {
  final Color background;
  final Color border;
  final Color innerCircle;

  const _RadioGroupColors({
    required this.background,
    required this.border,
    required this.innerCircle,
  });
}

/// Helper class to hold resolved radio group size properties.
class _RadioGroupSizeProperties {
  final double size;
  final double innerCircleSize;

  const _RadioGroupSizeProperties({
    required this.size,
    required this.innerCircleSize,
  });
}