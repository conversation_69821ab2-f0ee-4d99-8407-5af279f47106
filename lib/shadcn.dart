// Flutter Shadcn Component Library
// 
// A Flutter component library inspired by shadcn/ui that provides 51 themed 
// UI components with full Material Design integration. The library follows 
// a theme-first design approach where all styling is derived from Flutter's 
// theme system using ThemeExtension patterns.

// Core theme system exports
export 'src/theme/extensions/shadcn_theme_extension.dart';
export 'src/theme/extensions/shadcn_accordion_theme.dart';
export 'src/theme/extensions/shadcn_alert_theme.dart';
export 'src/theme/extensions/shadcn_button_theme.dart';
export 'src/theme/extensions/shadcn_card_theme.dart';
export 'src/theme/extensions/shadcn_input_theme.dart';
export 'src/theme/extensions/shadcn_select_theme.dart';
export 'src/theme/color_schemes/shadcn_color_scheme.dart';
export 'src/constants/shadcn_tokens.dart';
export 'src/utils/theme_resolver.dart';

// Base component exports
export 'src/components/shadcn_component.dart';

// Component exports
export 'src/components/accordion/accordion.dart';
export 'src/components/alert/alert.dart';
export 'src/components/button/button.dart';
export 'src/components/input/input.dart';
export 'src/components/card/card.dart';
export 'src/components/select/select.dart';
