import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

/// Demonstrates the usage of ShadcnAlert and ShadcnAlertDialog components
/// with various configurations and variants.
class AlertDemo extends StatelessWidget {
  const AlertDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Alert Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildSection(
              context,
              'Alert Components',
              [
                _buildAlertExamples(context),
              ],
            ),
            const SizedBox(height: 32),
            _buildSection(
              context,
              'Alert Dialog Examples',
              [
                _buildAlertDialogExamples(context),
              ],
            ),
            const SizedBox(height: 32),
            _buildSection(
              context,
              'Themed Alerts',
              [
                _buildThemedAlertExamples(context),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildAlertExamples(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Default alert with title and description
        const ShadcnAlert(
          title: Text('Information'),
          description: Text('This is a default alert with informational content.'),
        ),
        
        const SizedBox(height: 16),
        
        // Destructive alert
        const ShadcnAlert.destructive(
          title: Text('Warning'),
          description: Text('This is a destructive alert indicating a warning or error.'),
        ),
        
        const SizedBox(height: 16),
        
        // Alert with custom icon
        const ShadcnAlert(
          icon: Icon(Icons.lightbulb_outline),
          title: Text('Tip'),
          description: Text('Here\'s a helpful tip with a custom icon.'),
        ),
        
        const SizedBox(height: 16),
        
        // Alert with only title
        const ShadcnAlert(
          title: Text('Simple Alert'),
        ),
        
        const SizedBox(height: 16),
        
        // Dismissible alert
        ShadcnAlert(
          title: const Text('Dismissible Alert'),
          description: const Text('This alert can be dismissed by tapping the X button.'),
          dismissible: true,
          onDismiss: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Alert dismissed')),
            );
          },
        ),
        
        const SizedBox(height: 16),
        
        // Interactive alert
        ShadcnAlert(
          title: const Text('Interactive Alert'),
          description: const Text('Tap this alert to trigger an action.'),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Alert tapped!')),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAlertDialogExamples(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Basic info dialog button
        ElevatedButton(
          onPressed: () => _showInfoDialog(context),
          child: const Text('Show Info Dialog'),
        ),
        
        const SizedBox(height: 8),
        
        // Destructive dialog button
        ElevatedButton(
          onPressed: () => _showDestructiveDialog(context),
          child: const Text('Show Warning Dialog'),
        ),
        
        const SizedBox(height: 8),
        
        // Confirmation dialog button
        ElevatedButton(
          onPressed: () => _showConfirmationDialog(context),
          child: const Text('Show Confirmation Dialog'),
        ),
        
        const SizedBox(height: 8),
        
        // Destructive confirmation dialog button
        ElevatedButton(
          onPressed: () => _showDestructiveConfirmationDialog(context),
          child: const Text('Show Delete Confirmation'),
        ),
        
        const SizedBox(height: 8),
        
        // Custom dialog with vertical actions
        ElevatedButton(
          onPressed: () => _showCustomDialog(context),
          child: const Text('Show Custom Dialog'),
        ),
      ],
    );
  }

  Widget _buildThemedAlertExamples(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        extensions: [
          // Custom alert theme with different colors
          ShadcnAlertTheme(
            defaultBackground: Colors.blue.shade50,
            defaultForeground: Colors.blue.shade800,
            defaultBorder: Colors.blue.shade200,
            defaultIconColor: Colors.blue.shade600,
            
            destructiveBackground: Colors.orange.shade50,
            destructiveForeground: Colors.orange.shade800,
            destructiveBorder: Colors.orange.shade200,
            destructiveIconColor: Colors.orange.shade600,
            
            borderRadius: BorderRadius.circular(12),
            padding: const EdgeInsets.all(20),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const ShadcnAlert(
            title: Text('Custom Themed Alert'),
            description: Text('This alert uses a custom theme with blue colors and rounded corners.'),
          ),
          
          const SizedBox(height: 16),
          
          const ShadcnAlert.destructive(
            title: Text('Custom Themed Warning'),
            description: Text('This warning uses custom orange colors instead of red.'),
          ),
        ],
      ),
    );
  }

  void _showInfoDialog(BuildContext context) {
    ShadcnAlertDialog.info(
      title: 'Information',
      content: 'This is an informational dialog with default styling.',
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('OK'),
        ),
      ],
    ).show(context);
  }

  void _showDestructiveDialog(BuildContext context) {
    ShadcnAlertDialog.destructive(
      title: 'Warning',
      content: 'This is a warning dialog with destructive styling.',
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Dismiss'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Understand'),
        ),
      ],
    ).show(context);
  }

  void _showConfirmationDialog(BuildContext context) async {
    final result = await ShadcnAlertDialogExtension.showConfirmation(
      context,
      title: 'Confirm Action',
      content: 'Are you sure you want to perform this action?',
    );
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result == true 
                ? 'User confirmed the action'
                : result == false
                    ? 'User cancelled the action'
                    : 'User dismissed the dialog',
          ),
        ),
      );
    }
  }

  void _showDestructiveConfirmationDialog(BuildContext context) async {
    final result = await ShadcnAlertDialogExtension.showDestructiveConfirmation(
      context,
      title: 'Delete Item',
      content: 'Are you sure you want to delete this item? This action cannot be undone.',
    );
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result == true 
                ? 'Item would be deleted'
                : result == false
                    ? 'Delete operation cancelled'
                    : 'Delete dialog dismissed',
          ),
        ),
      );
    }
  }

  void _showCustomDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ShadcnAlertDialog(
        title: 'Custom Dialog',
        contentWidget: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('This dialog has custom content with multiple elements:'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, size: 16),
                  SizedBox(width: 8),
                  Text('Important information'),
                ],
              ),
            ),
          ],
        ),
        verticalActions: true,
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Primary Action'),
          ),
          OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Secondary Action'),
          ),
        ],
      ),
    );
  }
}

/// Example showing advanced theming options
class CustomAlertThemeExample extends StatelessWidget {
  const CustomAlertThemeExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        extensions: [
          ShadcnAlertTheme(
            // Dark theme-inspired colors
            defaultBackground: Colors.grey.shade900,
            defaultForeground: Colors.grey.shade100,
            defaultBorder: Colors.grey.shade700,
            defaultIconColor: Colors.grey.shade400,
            
            destructiveBackground: Colors.red.shade900.withValues(alpha: 0.2),
            destructiveForeground: Colors.red.shade200,
            destructiveBorder: Colors.red.shade800,
            destructiveIconColor: Colors.red.shade400,
            
            // Custom layout
            padding: const EdgeInsets.all(24),
            borderRadius: BorderRadius.circular(16),
            borderWidth: 2,
            iconGap: 16,
            contentGap: 8,
            
            // Custom typography
            titleTextStyle: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
            descriptionTextStyle: const TextStyle(
              fontSize: 15,
              height: 1.4,
            ),
            iconSize: 24,
          ),
        ],
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ShadcnAlert(
            title: Text('Dark Theme Alert'),
            description: Text('This alert demonstrates custom dark theme styling with larger spacing and borders.'),
          ),
          
          SizedBox(height: 16),
          
          ShadcnAlert.destructive(
            title: Text('Dark Theme Warning'),
            description: Text('This warning alert also follows the dark theme with red accent colors.'),
          ),
        ],
      ),
    );
  }
}