import 'package:flutter/material.dart';
import '../lib/src/components/breadcrumb/shadcn_breadcrumb.dart';
import '../lib/src/theme/extensions/shadcn_breadcrumb_theme.dart';

class BreadcrumbDemo extends StatelessWidget {
  const BreadcrumbDemo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Breadcrumb Demo'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Breadcrumb',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ShadcnBreadcrumb(
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Home',
                  onTap: () => print('Home tapped'),
                ),
                ShadcnBreadcrumbItem(
                  text: 'Products',
                  onTap: () => print('Products tapped'),
                ),
                const ShadcnBreadcrumbItem(
                  text: 'Electronics',
                ),
              ],
            ),
            const SizedBox(height: 32),
            const Text(
              'Breadcrumb with Home Icon',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ShadcnBreadcrumb(
              showHome: true,
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Category',
                  onTap: () => print('Category tapped'),
                ),
                const ShadcnBreadcrumbItem(
                  text: 'Sub-category',
                ),
              ],
            ),
            const SizedBox(height: 32),
            const Text(
              'Breadcrumb with Icons',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ShadcnBreadcrumb(
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Dashboard',
                  icon: const Icon(Icons.dashboard),
                  onTap: () => print('Dashboard tapped'),
                ),
                ShadcnBreadcrumbItem(
                  text: 'Settings',
                  icon: const Icon(Icons.settings),
                  onTap: () => print('Settings tapped'),
                ),
                const ShadcnBreadcrumbItem(
                  text: 'Profile',
                  icon: Icon(Icons.person),
                ),
              ],
            ),
            const SizedBox(height: 32),
            const Text(
              'Truncated Breadcrumb',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ShadcnBreadcrumb(
              maxItems: 3,
              items: [
                ShadcnBreadcrumbItem(
                  text: 'Root',
                  onTap: () => print('Root tapped'),
                ),
                ShadcnBreadcrumbItem(
                  text: 'Level 1',
                  onTap: () => print('Level 1 tapped'),
                ),
                ShadcnBreadcrumbItem(
                  text: 'Level 2',
                  onTap: () => print('Level 2 tapped'),
                ),
                ShadcnBreadcrumbItem(
                  text: 'Level 3',
                  onTap: () => print('Level 3 tapped'),
                ),
                const ShadcnBreadcrumbItem(
                  text: 'Current',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}