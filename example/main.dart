import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Create a theme with shadcn extensions
    final lightColorScheme = ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.light,
    );
    
    final darkColorScheme = ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.dark,
    );
    
    return MaterialApp(
      title: 'Shadcn Flutter Demo',
      theme: ThemeData(
        colorScheme: lightColorScheme,
        extensions: [
          ShadcnColorScheme.fromMaterial(lightColorScheme),
        ],
      ),
      darkTheme: ThemeData(
        colorScheme: darkColorScheme,
        extensions: [
          ShadcnColorScheme.fromMaterial(darkColorScheme),
        ],
      ),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatelessWidget {
  const MyHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final shadcnColors = Theme.of(context).extension<ShadcnColorScheme>() ??
        ShadcnColorScheme.fromMaterial(Theme.of(context).colorScheme);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Shadcn Flutter Demo'),
        backgroundColor: shadcnColors.card,
        foregroundColor: shadcnColors.cardForeground,
      ),
      body: Padding(
        padding: EdgeInsets.all(ShadcnTokens.spacing6),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Shadcn Foundation Demo',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: shadcnColors.foreground,
                fontWeight: ShadcnTokens.fontWeightBold,
              ),
            ),
            SizedBox(height: ShadcnTokens.spacing4),
            
            // Demo of shadcn color tokens
            Card(
              color: shadcnColors.card,
              child: Padding(
                padding: EdgeInsets.all(ShadcnTokens.spacing4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Color Tokens',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: shadcnColors.cardForeground,
                        fontWeight: ShadcnTokens.fontWeightSemibold,
                      ),
                    ),
                    SizedBox(height: ShadcnTokens.spacing2),
                    
                    _buildColorDemo('Background', shadcnColors.background),
                    _buildColorDemo('Card', shadcnColors.card),
                    _buildColorDemo('Muted', shadcnColors.muted),
                    _buildColorDemo('Accent', shadcnColors.accent),
                    _buildColorDemo('Destructive', shadcnColors.destructive),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: ShadcnTokens.spacing4),
            
            // Demo of spacing tokens
            Card(
              color: shadcnColors.card,
              child: Padding(
                padding: EdgeInsets.all(ShadcnTokens.spacing4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Spacing & Size Tokens',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: shadcnColors.cardForeground,
                        fontWeight: ShadcnTokens.fontWeightSemibold,
                      ),
                    ),
                    SizedBox(height: ShadcnTokens.spacing2),
                    
                    Text(
                      'Button Height (Medium): ${ShadcnTokens.buttonHeightMd}px',
                      style: TextStyle(color: shadcnColors.mutedForeground),
                    ),
                    Text(
                      'Input Height (Medium): ${ShadcnTokens.inputHeightMd}px',
                      style: TextStyle(color: shadcnColors.mutedForeground),
                    ),
                    Text(
                      'Border Radius (Medium): ${ShadcnTokens.radiusMd}px',
                      style: TextStyle(color: shadcnColors.mutedForeground),
                    ),
                    Text(
                      'Spacing 4: ${ShadcnTokens.spacing4}px',
                      style: TextStyle(color: shadcnColors.mutedForeground),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildColorDemo(String name, Color color) {
    return Padding(
      padding: EdgeInsets.only(bottom: ShadcnTokens.spacing1),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: color,
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(ShadcnTokens.radiusSm),
            ),
          ),
          SizedBox(width: ShadcnTokens.spacing2),
          Text(name),
        ],
      ),
    );
  }
}