import 'package:flutter/material.dart';
import '../lib/src/components/navigation_menu/shadcn_navigation_menu.dart';
import '../lib/src/theme/extensions/shadcn_navigation_menu_theme.dart';

class NavigationMenuDemo extends StatefulWidget {
  const NavigationMenuDemo({Key? key}) : super(key: key);

  @override
  State<NavigationMenuDemo> createState() => _NavigationMenuDemoState();
}

class _NavigationMenuDemoState extends State<NavigationMenuDemo> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Navigation Menu Demo'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Horizontal Navigation Menu',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ShadcnNavigationMenu(
              onItemSelected: (item) {
                print('Selected: ${item.text}');
              },
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Home',
                  icon: const Icon(Icons.home),
                  active: _selectedIndex == 0,
                  onTap: () => setState(() => _selectedIndex = 0),
                ),
                ShadcnNavigationMenuItem(
                  text: 'About',
                  icon: const Icon(Icons.info),
                  active: _selectedIndex == 1,
                  onTap: () => setState(() => _selectedIndex = 1),
                ),
                ShadcnNavigationMenuItem(
                  text: 'Services',
                  icon: const Icon(Icons.work),
                  active: _selectedIndex == 2,
                  children: [
                    ShadcnNavigationMenuItem(
                      text: 'Web Development',
                      onTap: () => print('Web Development selected'),
                    ),
                    ShadcnNavigationMenuItem(
                      text: 'Mobile Development',
                      onTap: () => print('Mobile Development selected'),
                    ),
                    const ShadcnNavigationMenuItem(
                      text: 'Consulting',
                      showDividerAfter: true,
                    ),
                    ShadcnNavigationMenuItem(
                      text: 'Support',
                      onTap: () => print('Support selected'),
                    ),
                  ],
                ),
                ShadcnNavigationMenuItem(
                  text: 'Contact',
                  icon: const Icon(Icons.contact_mail),
                  active: _selectedIndex == 3,
                  onTap: () => setState(() => _selectedIndex = 3),
                ),
              ],
            ),
            const SizedBox(height: 32),
            const Text(
              'Vertical Navigation Menu',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ShadcnNavigationMenu(
              orientation: Axis.vertical,
              showActiveIndicator: true,
              indicatorPosition: NavigationIndicatorPosition.left,
              onItemSelected: (item) {
                print('Selected: ${item.text}');
              },
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Dashboard',
                  leading: const Icon(Icons.dashboard),
                  active: true,
                  onTap: () => print('Dashboard selected'),
                ),
                ShadcnNavigationMenuItem(
                  text: 'Users',
                  leading: const Icon(Icons.people),
                  onTap: () => print('Users selected'),
                ),
                ShadcnNavigationMenuItem(
                  text: 'Settings',
                  leading: const Icon(Icons.settings),
                  onTap: () => print('Settings selected'),
                ),
                ShadcnNavigationMenuItem(
                  text: 'Help',
                  leading: const Icon(Icons.help),
                  disabled: true,
                  onTap: () => print('Help selected'),
                ),
              ],
            ),
            const SizedBox(height: 32),
            const Text(
              'Navigation Menu with Indicators',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ShadcnNavigationMenu(
              showActiveIndicator: true,
              indicatorPosition: NavigationIndicatorPosition.bottom,
              items: [
                ShadcnNavigationMenuItem(
                  text: 'Files',
                  icon: const Icon(Icons.folder),
                  active: true,
                  onTap: () => print('Files selected'),
                ),
                ShadcnNavigationMenuItem(
                  text: 'Recent',
                  icon: const Icon(Icons.history),
                  onTap: () => print('Recent selected'),
                ),
                ShadcnNavigationMenuItem(
                  text: 'Shared',
                  icon: const Icon(Icons.share),
                  onTap: () => print('Shared selected'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}