import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  runApp(const CardDemoApp());
}

class CardDemoApp extends StatelessWidget {
  const CardDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    final lightColorScheme = ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.light,
    );
    
    final darkColorScheme = ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.dark,
    );

    final lightTheme = ThemeData(
      colorScheme: lightColorScheme,
      useMaterial3: true,
      extensions: [
        ShadcnCardTheme.lightTheme(lightColorScheme),
      ],
    );

    final darkTheme = ThemeData(
      colorScheme: darkColorScheme,
      useMaterial3: true,
      extensions: [
        ShadcnCardTheme.darkTheme(darkColorScheme),
      ],
    );

    return MaterialApp(
      title: 'ShadcnCard Demo',
      theme: lightTheme,
      darkTheme: darkTheme,
      home: const CardDemoHome(),
    );
  }
}

class CardDemoHome extends StatefulWidget {
  const CardDemoHome({super.key});

  @override
  State<CardDemoHome> createState() => _CardDemoHomeState();
}

class _CardDemoHomeState extends State<CardDemoHome> {
  bool _darkMode = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ShadcnCard Demo'),
        actions: [
          Switch(
            value: _darkMode,
            onChanged: (value) {
              setState(() {
                _darkMode = value;
              });
            },
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Theme(
        data: _darkMode ? 
          Theme.of(context).copyWith(
            brightness: Brightness.dark,
            colorScheme: Theme.of(context).colorScheme.copyWith(
              brightness: Brightness.dark,
            ),
          ) : 
          Theme.of(context),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Card Variants',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              
              // Default Card
              ShadcnCard(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Default Card',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'This is a default card with subtle styling and no elevation.',
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 16),
              
              // Outlined Card
              ShadcnCard.outlined(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Outlined Card',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'This is an outlined card with visible borders and no elevation.',
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 16),
              
              // Elevated Card
              ShadcnCard.elevated(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Elevated Card',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'This is an elevated card with Material Design elevation system.',
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 24),
              
              Text(
                'Interactive Cards',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              
              // Clickable Card  
              ShadcnCard.outlined(
                onTap: () {
                  // Handle tap
                },
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.touch_app),
                      SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Clickable Card',
                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                            ),
                            SizedBox(height: 4),
                            Text('Tap this card to interact with it.'),
                          ],
                        ),
                      ),
                      Icon(Icons.chevron_right),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 24),
              
              Text(
                'Custom Styled Cards',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              
              // Custom Colors Card
              ShadcnCard(
                backgroundColor: Colors.blue.withValues(alpha: 0.1),
                borderColor: Colors.blue,
                borderWidth: 2.0,
                variant: ShadcnCardVariant.outlined,
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Custom Colors',
                        style: TextStyle(
                          fontSize: 18, 
                          fontWeight: FontWeight.w600,
                          color: Colors.blue[700],
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'This card uses custom background and border colors.',
                        style: TextStyle(color: Colors.blue[600]),
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 16),
              
              // High Elevation Card
              ShadcnCard(
                variant: ShadcnCardVariant.elevated,
                elevation: 12.0,
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'High Elevation',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'This card has custom high elevation for dramatic shadow effect.',
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 16),
              
              // Complex Content Card
              ShadcnCard.outlined(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Complex Content Card',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'This card demonstrates complex content layouts with multiple sections.',
                          ),
                        ],
                      ),
                    ),
                    Divider(height: 1),
                    Padding(
                      padding: EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () {},
                            child: Text('Cancel'),
                          ),
                          SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () {},
                            child: Text('Confirm'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}