import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Shadcn Accordion Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        extensions: [
          ShadcnAccordionTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue),
          ),
        ],
      ),
      home: AccordionDemo(),
    );
  }
}

class AccordionDemo extends StatefulWidget {
  @override
  _AccordionDemoState createState() => _AccordionDemoState();
}

class _AccordionDemoState extends State<AccordionDemo> {
  String? singleExpandedValue;
  Set<String> multipleExpandedValues = {};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Accordion Demo'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Single Selection Accordion',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            ShadcnAccordion(
              expandedValue: singleExpandedValue,
              onExpandedValueChanged: (value) {
                setState(() {
                  singleExpandedValue = value;
                });
              },
              collapsible: true,
              items: [
                ShadcnAccordionItem(
                  value: 'item1',
                  header: Text('Getting Started'),
                  content: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Welcome to the Shadcn Flutter component library!'),
                      SizedBox(height: 8),
                      Text('This is a comprehensive UI component library that brings shadcn/ui design system to Flutter.'),
                    ],
                  ),
                ),
                ShadcnAccordionItem(
                  value: 'item2',
                  header: Text('Installation'),
                  content: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Add the following to your pubspec.yaml:'),
                      SizedBox(height: 8),
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'dependencies:\n  shadcn: ^1.0.0',
                          style: TextStyle(fontFamily: 'monospace'),
                        ),
                      ),
                    ],
                  ),
                ),
                ShadcnAccordionItem(
                  value: 'item3',
                  header: Text('Usage'),
                  content: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Import the library and start using components:'),
                      SizedBox(height: 8),
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          "import 'package:shadcn/shadcn.dart';\n\nShadcnButton(\n  text: 'Click me',\n  onPressed: () {},\n)",
                          style: TextStyle(fontFamily: 'monospace'),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 32),
            
            Text(
              'Multiple Selection Accordion',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            ShadcnAccordion(
              allowMultipleExpanded: true,
              expandedValues: multipleExpandedValues,
              onExpandedValuesChanged: (values) {
                setState(() {
                  multipleExpandedValues = values;
                });
              },
              items: [
                ShadcnAccordionItem(
                  value: 'faq1',
                  header: Text('What is Shadcn?'),
                  content: Text('Shadcn is a collection of reusable components built using Radix UI and Tailwind CSS. This Flutter version brings the same design philosophy to Flutter applications.'),
                ),
                ShadcnAccordionItem(
                  value: 'faq2',
                  header: Text('Is it free?'),
                  content: Text('Yes! This is an open-source library that you can use in your projects for free.'),
                ),
                ShadcnAccordionItem(
                  value: 'faq3',
                  header: Text('How do I customize themes?'),
                  content: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('You can customize themes by extending the base theme classes:'),
                      SizedBox(height: 8),
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'ShadcnAccordionTheme.defaultTheme(colorScheme).copyWith(\n  headerBackground: Colors.blue,\n  borderRadius: BorderRadius.circular(8),\n)',
                          style: TextStyle(fontFamily: 'monospace', fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 32),
            
            Text(
              'Custom Styled Accordion',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            ShadcnAccordion(
              theme: ShadcnAccordionTheme(
                headerBackground: Colors.blue[50],
                headerForeground: Colors.blue[900],
                headerHoverBackground: Colors.blue[100],
                contentBackground: Colors.blue[25],
                borderColor: Colors.blue[300],
                borderRadius: BorderRadius.circular(12),
                triggerIconColor: Colors.blue[700],
                triggerIconHoverColor: Colors.blue[900],
              ),
              expandedValue: 'custom1',
              items: [
                ShadcnAccordionItem(
                  value: 'custom1',
                  header: Text('Custom Themed Section'),
                  content: Text('This accordion uses custom colors and styling to demonstrate the theming capabilities.'),
                  triggerIcon: Icon(Icons.add),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}