import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  runApp(const InputDemoApp());
}

class InputDemoApp extends StatelessWidget {
  const InputDemoApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ShadCN Input Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        extensions: [
          ShadcnInputTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue),
          ),
        ],
      ),
      home: const InputDemo(),
    );
  }
}

class InputDemo extends StatefulWidget {
  const InputDemo({Key? key}) : super(key: key);

  @override
  State<InputDemo> createState() => _InputDemoState();
}

class _InputDemoState extends State<InputDemo> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _searchController = TextEditingController();
  String _inputValue = '';

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('ShadCN Input Demo')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Input Sizes',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Column(
                children: [
                  ShadcnInput.small(
                    placeholder: 'Small input (32px)',
                    onChanged: (value) => _showSnackBar(context, 'Small: $value'),
                  ),
                  const SizedBox(height: 8),
                  ShadcnInput.medium(
                    placeholder: 'Medium input (36px) - default',
                    onChanged: (value) => _showSnackBar(context, 'Medium: $value'),
                  ),
                  const SizedBox(height: 8),
                  ShadcnInput.large(
                    placeholder: 'Large input (40px)',
                    onChanged: (value) => _showSnackBar(context, 'Large: $value'),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              const Text('Input Types',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Column(
                children: [
                  ShadcnInput.email(
                    controller: _emailController,
                    label: 'Email',
                    helperText: 'Enter your email address',
                  ),
                  const SizedBox(height: 16),
                  ShadcnInput.password(
                    controller: _passwordController,
                    label: 'Password',
                    helperText: 'Must be at least 8 characters',
                  ),
                  const SizedBox(height: 16),
                  ShadcnInput(
                    controller: _searchController,
                    placeholder: 'Search...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () => _searchController.clear(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              const Text('Input States',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Column(
                children: [
                  const ShadcnInput(
                    placeholder: 'Normal input',
                    helperText: 'This is a helper text',
                  ),
                  const SizedBox(height: 16),
                  const ShadcnInput(
                    placeholder: 'Error input',
                    errorText: 'This field has an error',
                    helperText: 'Helper text still shows',
                  ),
                  const SizedBox(height: 16),
                  const ShadcnInput(
                    placeholder: 'Disabled input',
                    enabled: false,
                    helperText: 'This input is disabled',
                  ),
                  const SizedBox(height: 16),
                  const ShadcnInput(
                    placeholder: 'Read-only input',
                    readOnly: true,
                    helperText: 'This input is read-only',
                  ),
                ],
              ),
              const SizedBox(height: 32),

              const Text('Form Integration',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    ShadcnInput(
                      placeholder: 'Required field',
                      label: 'Name *',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'This field is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    ShadcnInput(
                      placeholder: 'Enter at least 3 characters',
                      label: 'Description',
                      maxLines: 3,
                      validator: (value) {
                        if (value != null && value.isNotEmpty && value.length < 3) {
                          return 'Must be at least 3 characters';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              if (_formKey.currentState?.validate() == true) {
                                _showSnackBar(context, 'Form is valid!');
                              }
                            },
                            child: const Text('Validate'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              _formKey.currentState?.reset();
                            },
                            child: const Text('Reset'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              const Text('Custom Styling',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Column(
                children: [
                  ShadcnInput(
                    placeholder: 'Custom width input',
                    width: 200,
                    onChanged: (value) => setState(() => _inputValue = value),
                  ),
                  const SizedBox(height: 8),
                  Text('Input value: $_inputValue',
                      style: Theme.of(context).textTheme.bodySmall),
                  const SizedBox(height: 16),
                  const ShadcnInput(
                    placeholder: 'Custom padding',
                    contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  ),
                  const SizedBox(height: 16),
                  ShadcnInput(
                    placeholder: 'Custom border radius',
                    borderRadius: BorderRadius.circular(20),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}