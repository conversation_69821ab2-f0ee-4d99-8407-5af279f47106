import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

class ButtonDemo extends StatelessWidget {
  const ButtonDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('ShadCN Button Demo')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Button Variants',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ShadcnButton.primary(
                    text: 'Primary',
                    onPressed: () =>
                        _showSnackBar(context, 'Primary button pressed'),
                  ),
                  ShadcnButton.secondary(
                    text: 'Secondary',
                    onPressed: () =>
                        _showSnackBar(context, 'Secondary button pressed'),
                  ),
                  ShadcnButton.destructive(
                    text: 'Destructive',
                    onPressed: () =>
                        _showSnackBar(context, 'Destructive button pressed'),
                  ),
                  ShadcnButton.outline(
                    text: 'Outline',
                    onPressed: () =>
                        _showSnackBar(context, 'Outline button pressed'),
                  ),
                  ShadcnButton.ghost(
                    text: 'Ghost',
                    onPressed: () =>
                        _showSnackBar(context, 'Ghost button pressed'),
                  ),
                  ShadcnButton.link(
                    text: 'Link',
                    onPressed: () =>
                        _showSnackBar(context, 'Link button pressed'),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              const Text('Button Sizes',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ShadcnButton(
                    text: 'Small',
                    size: ShadcnButtonSize.small,
                    onPressed: () =>
                        _showSnackBar(context, 'Small button pressed'),
                  ),
                  ShadcnButton(
                    text: 'Medium',
                    size: ShadcnButtonSize.medium,
                    onPressed: () =>
                        _showSnackBar(context, 'Medium button pressed'),
                  ),
                  ShadcnButton(
                    text: 'Large',
                    size: ShadcnButtonSize.large,
                    onPressed: () =>
                        _showSnackBar(context, 'Large button pressed'),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              const Text('Buttons with Icons',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ShadcnButton.primary(
                    text: 'Download',
                    leading: const Icon(Icons.download),
                    onPressed: () =>
                        _showSnackBar(context, 'Download button pressed'),
                  ),
                  ShadcnButton.secondary(
                    text: 'Continue',
                    trailing: const Icon(Icons.arrow_forward),
                    onPressed: () =>
                        _showSnackBar(context, 'Continue button pressed'),
                  ),
                  ShadcnButton.outline(
                    text: 'Share',
                    leading: const Icon(Icons.share),
                    trailing: const Icon(Icons.open_in_new),
                    onPressed: () =>
                        _showSnackBar(context, 'Share button pressed'),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              const Text('Button States',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ShadcnButton.primary(
                    text: 'Enabled',
                    onPressed: () =>
                        _showSnackBar(context, 'Enabled button pressed'),
                  ),
                  ShadcnButton.primary(
                    text: 'Disabled',
                    onPressed: null,
                  ),
                  ShadcnButton.primary(
                    text: 'Loading',
                    isLoading: true,
                    onPressed: () {},
                  ),
                ],
              ),
              const SizedBox(height: 32),
              const Text('Custom Properties',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShadcnButton.primary(
                    text: 'Full Width Button',
                    expand: true,
                    onPressed: () =>
                        _showSnackBar(context, 'Full width button pressed'),
                  ),
                  const SizedBox(height: 8),
                  ShadcnButton.secondary(
                    text: 'Custom Size',
                    onPressed: () =>
                        _showSnackBar(context, 'Custom size button pressed'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}

void main() {
  runApp(
    MaterialApp(
      title: 'ShadCN Button Demo',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        extensions: [
          ShadcnButtonTheme.defaultTheme(
            ColorScheme.fromSeed(seedColor: Colors.blue),
          ),
        ],
      ),
      home: const ButtonDemo(),
    ),
  );
}
