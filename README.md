# Flutter Shadcn Component Library

A Flutter component library inspired by [shadcn/ui](https://ui.shadcn.com) that provides 51 themed UI components with full Material Design integration. The library follows a theme-first design approach where all styling is derived from <PERSON>lutter's theme system using ThemeExtension patterns.

## Features

- **51 Shadcn Components**: Complete implementation of all shadcn/ui components for Flutter
- **Theme-First Design**: All styling derived from Flutter's theme system - no hardcoded values
- **Material Design Integration**: Full compatibility with Material Design 3 theming
- **Dark Mode Support**: Automatic light/dark theme switching with proper color adaptation
- **Customizable**: Override individual component themes while maintaining consistency
- **Performance Optimized**: Efficient theme resolution using Flutter's InheritedWidget system

## Core Foundation (Task 1 Complete)

This package currently includes the foundational theme system:

### Theme System
- `ShadcnThemeExtension`: Abstract base class for all component themes
- `ShadcnColorScheme`: Complete shadcn color token system with Material integration  
- `ShadcnTokens`: All shadcn design tokens (spacing, sizes, colors, typography)
- `ShadcnThemeResolver`: Safe theme resolution with fallback handling
- `ShadcnComponent`: Base component class with theme validation

### Design Tokens
- Spacing tokens: `spacing0` (0px) to `spacing12` (48px)
- Size tokens: Button heights (36px, 40px, 44px), Input heights (32px, 36px, 40px)  
- Border radius: `radiusSm` (4px) to `radiusXxl` (16px)
- Typography: Font sizes, weights, and line heights
- Colors: Complete shadcn color palette with Material mapping

## Getting Started

Add this package to your `pubspec.yaml`:

```yaml
dependencies:
  shadcn: ^0.0.1
```

## Usage

### Basic Setup

```dart
import 'package:flutter/material.dart';
import 'package:shadcn/shadcn.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final lightColorScheme = ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.light,
    );
    
    return MaterialApp(
      theme: ThemeData(
        colorScheme: lightColorScheme,
        extensions: [
          ShadcnColorScheme.fromMaterial(lightColorScheme),
        ],
      ),
      home: const MyHomePage(),
    );
  }
}
```

### Using Theme Tokens

```dart
// Use spacing tokens
Padding(
  padding: EdgeInsets.all(ShadcnTokens.spacing4), // 16px
  child: Container(
    height: ShadcnTokens.buttonHeightMd, // 40px
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(ShadcnTokens.radiusMd), // 6px
    ),
  ),
)

// Use color tokens
final shadcnColors = Theme.of(context).extension<ShadcnColorScheme>()!;
Container(
  color: shadcnColors.card,
  child: Text(
    'Themed content',
    style: TextStyle(color: shadcnColors.cardForeground),
  ),
)
```

### Creating Custom Component Themes

```dart
class CustomButtonTheme extends ShadcnThemeExtension<CustomButtonTheme> {
  final Color? primaryColor;
  final double? height;
  
  const CustomButtonTheme({this.primaryColor, this.height});
  
  @override
  CustomButtonTheme copyWith({Color? primaryColor, double? height}) {
    return CustomButtonTheme(
      primaryColor: primaryColor ?? this.primaryColor,
      height: height ?? this.height,
    );
  }
  
  @override
  CustomButtonTheme lerp(ThemeExtension<CustomButtonTheme>? other, double t) {
    // Implementation here
  }
}
```

## Development Roadmap

This is an active development project implementing all 51 shadcn components:

- [x] **Task 1**: Project structure and core theme foundation
- [ ] **Task 2**: Core theme system (color scheme, theme resolver, base component)
- [ ] **Task 3**: Foundational components (Button, Input, Card)
- [ ] **Task 4-11**: Remaining component implementations

See the full roadmap in [tasks.md](.kiro/specs/flutter-shadcn-components/tasks.md).

## Architecture

The library follows a strict theme-first architecture:

1. **No Hardcoded Values**: All styling derived from theme context
2. **Material Compatibility**: Extends rather than replaces Material theming  
3. **Fallback Chain**: Custom theme → Material theme → hardcoded defaults
4. **Performance**: Efficient InheritedWidget usage for theme propagation

See [design.md](.kiro/specs/flutter-shadcn-components/design.md) for detailed architecture documentation.

## Contributing

This project follows spec-driven development. See:
- [requirements.md](.kiro/specs/flutter-shadcn-components/requirements.md)
- [design.md](.kiro/specs/flutter-shadcn-components/design.md)  
- [tasks.md](.kiro/specs/flutter-shadcn-components/tasks.md)

## License

MIT License - see LICENSE file for details.
